import request from '@/config/axios'

export interface OrganizationVO {
    id: number
    name: string
    code: string
    sort: number
    status: number
    type: number
    dataScope: number
    dataScopeDeptIds: number[]
    createTime: Date
}


// 查询行政组织属性列表
export const getAdmOrganizationList = async (id: number): Promise<any[]> => {
    return await request.get({ url: '/system/orgdata/list-adminlist?id=' + id })
}
// 查询法人组织属性列表
export const getTmpOrganizationList = async (id: number): Promise<any[]> => {
    return await request.get({ url: '/system/orgdata/dept-tmplist?id=' + id })
}

// 修改组织结构
export const getSynOrg = async (orgMode: number) => {
    return await request.get({ url: '/system/dept/syn-org?orgMode=' + orgMode })
}
