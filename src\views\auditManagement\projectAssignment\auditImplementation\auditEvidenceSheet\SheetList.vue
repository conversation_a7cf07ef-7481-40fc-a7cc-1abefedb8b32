
<template>
  <Dialog
    :title="dialogTitle"
    :scroll="true"
    v-model="dialogVisible"
    width="85%"
    :loading="formLoading"
    maxHeight="72vh"
    setScrollTop="2vh"
  >
    <ContentWrap class="h-1/1">
      <el-table border v-loading="loading" :data="list">
        <el-table-column label="#" width="50" align="center">
          <template
            #default="{ $index }"
          >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
        </el-table-column>
        <el-table-column
          label="取证单名称"
          align="left"
          prop="projectName"
          :show-overflow-tooltip="true"
          min-width="200"
        />

        <el-table-column
          label="编制人"
          align="center"
          prop="auditUserName"
          :show-overflow-tooltip="true"
          min-width="100"
        />
        <el-table-column
          label="资料清单"
          align="left"
          prop="materialName"
          :show-overflow-tooltip="true"
          min-width="200"
        />
        <el-table-column
          label="资料类型"
          align="center"
          prop="resourceName"
          :show-overflow-tooltip="true"
          min-width="120"
        />
        <el-table-column
          label="上传人"
          align="center"
          prop="uploadUser"
          :show-overflow-tooltip="true"
          min-width="100"
        />
        <el-table-column label="上传时间" align="center" prop="uploadTime" min-width="120">
          <template #default="{row}">
            <span>{{ formatTime(row.uploadTime, 'yyyy-MM-dd') }}</span>
          </template>
        </el-table-column>
      </el-table>
    </ContentWrap>
    <template #footer>
      <!-- <el-button @click="handleConfirmUnit" type="primary" :disabled="formLoading">保存</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">提交</el-button>-->
      <el-button @click="dialogVisible = false">取消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { AuditRoleApi, AuditRoleVO } from '@/api/basicData/auditRole'
import FileForm from '@/views/infra/file/FileForm.vue'
import { formatTime } from '@/utils'
import { AuditEvidenceSheetApi } from '@/api/auditManagement/projectAssignment/auditImplem/auditEvidenceSheet'
defineOptions({ name: 'SheetList' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  auditRoleName: undefined,
  parentId: undefined,
  sort: undefined,
  status: undefined,
  projectPhase: undefined,
  date: undefined,
  content: undefined,
  wbflfgList: [],
  nbgzzdList: [],
  sjmxList: []
})
const detailData = ref({
  ids: []
})
const getLinkList = async () => {
  let data = {
    ids: detailData.value.ids
  }
  list.value = await AuditEvidenceSheetApi.getEvidenceDetailList(data)
}
const list = ref()
const formRules = reactive({
  auditRoleName: [{ required: true, message: '不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  parentId: [{ required: true, message: '父级编号不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref
const activeName = ref('1')

/** 打开弹窗 */
const open = async (ids: any) => {
  detailData.value.ids = ids.map((item) => item.id)
  dialogVisible.value = true
  // dialogTitle.value = t('审计取证单列表')
  // 修改时，设置数据
  formLoading.value = true
  try {
    getLinkList()
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AuditRoleVO
    if (formType.value === 'create') {
      await AuditRoleApi.createAuditRole(data)
      message.success(t('common.createSuccess'))
    } else {
      await AuditRoleApi.updateAuditRole(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
</script>
<style lang="scss" scoped>
.title {
  font-weight: 900;
  font-size: 16px;
  margin-bottom: 16px;
  margin-top: 16px;
  display: flex;
  align-items: center;
}
</style>