import request from '@/config/axios'

export const ResourceTableDetailAPI = {
  /**
   * 获取数据资源表信息数据
   * @param id
   */
  resourceTableInfoData: async (id: string) => {
    return request.post({
      url: `/model/resourceTable/resourceTableInfoData/${id}`
    })
  },

  /**
   * 获取目标表字段数据
   * @param data
   */
  targetTableFieldData: async (data: any) => {
    return request.post({
      url: `/model/resourceTableField/targetTableFieldData`,
      data
    })
  },

  /**
   * 获取数据资源表血缘关系树
   * @param id
   */
  resourceTableRelationTree: async (id: string) => {
    return request.post({
      url: `/model/resourceTable/resourceTableRelationTree/${id}`
    })
  },

  /**
   * 获取目标资源表样例数据
   * @param id
   */
  targetTableSampleData: async (id: string) => {
    return request.post({
      url: `/model/resourceTable/targetTableSampleData/${id}`
    })
  }
}
