import request from '@/config/axios'

// 获取模型分页
export function getWarningInfoPage(queryParams) {
  return request.post({
    url: '/model/warnConfig/warnConfigPageData',
    data: queryParams
  })
}


// 获取模型下拉列表
export function getModelList() {
  return request.post({
    url: '/model/modelInfo/allFixModels'
  })
}

// 保存预警
export function saveWarningInfo(data) {
  return request.post({
    url: '/model/warnConfig/saveWarnConfigData',
    data: data
  })
}

// 查询单条
export function getWarningInfo(id) {
  return request.post({
    url: '/model/warnConfig/warnConfigData/' + id
  })
}


// 删除预警
export function deleteWarningInfo(id) {
  return request.post({
    url: '/model/warnConfig/deleteWarnConfigData/' + id
  })
}

// 查询预警结果列表
export function getWarningResultList(queryParams) {
  return request.post({
    url: '/model/warnResult/warnResultPageData',
    data: queryParams
  })
}

//保存预警结果核查结果
export function saveWarnResultCheckResult(data) {
  return request.post({
    url: '/model/warnResult/saveWarnResultCheckResult',
    data: data
  })
}

// 获取疑点核查人列表
export function doubtfulCheckUserList(params){
  return request.get({
    url: '/system/user/userPageData',
    params
  })
  // return request.post({
  //   url: '/model/doubtfulInfo/doubtfulCheckUserList'
  // })
}

/**
 * 获取预警结果数据
 * @param id
 * @returns {*}
 */
export function getWarningResultData(id) {
  return request.post({
    url: `/model/warnResult/warnResultDataById/${id}`
  })
}
