import request from '@/config/axios'

// 审计类型 VO
export interface AuditRoleVO {
    id?: number // 主键，唯一标识
    auditRoleName: string // 审计类型名称
    sort: number //排序
    status: number //状态
}
export interface AuditRoleDetailVO {
    id: number // 主键，唯一标识
    auditRoleName: string // 审计角色名称
    sort: number //排序
    status: number //状态
    adder: string
    createTime: Date
}

/**
 * 使用 ： 审批流程模型配置
 */
export interface  AuditRoleOption {
  id: number // 主键，唯一标识
  roleName: string, //角色名称
  roleCode: string, //角色编码


}


// 审计类型 API
export const AuditRoleApi = {
    // 查询审计类型列表分页
    getAuditRoleList: async (params: any) => {
        return await request.get({ url: `/system/audit-role/page`, params })
    },
    getAuditRoleOptionList: async (params: any) => {
        return await request.get({ url: `/system/audit-role/getRoleOptionList`, params })
    },
    getAuditRoleAll: async () => {
        return await request.get({ url: `/system/audit-role/getAll` })
    },

    // 查询审计类型详情
    getAuditRole: async (id: number) => {
        return await request.get({ url: `/system/audit-role/get?id=` + id })
    },

    // 新增审计类型
    createAuditRole: async (data: AuditRoleVO) => {
        return await request.post({ url: `/system/audit-role/create`, data })
    },

    // 修改审计类型
    updateAuditRole: async (data: AuditRoleVO) => {
        return await request.put({ url: `/system/audit-role/update`, data })
    },

    // 删除审计类型
    deleteAuditRole: async (id: number) => {
        return await request.delete({ url: `/system/audit-role/delete?id=` + id })
    },
  /**
   * 获取审计项目角色
   * @param params
   */
  getList: async (params: any) => {
      return await request.get({ url: `/system/audit-role/list`, params })
    },

    // 导出审计类型 Excel
    exportAuditRole: async (params: any) => {
        return await request.download({ url: `/system/audit-role/export-excel`, params })
    },
    // 查询资料清单与分配列表
    projectDocumentAndDept: async (id: number) => {
        return await request.get({ url: `/manage/proj-plan-project/projectDocumentAndDept?id=` + id })
    }
}
