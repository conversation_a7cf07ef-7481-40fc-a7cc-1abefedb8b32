import request from '@/config/axios'

export const getProcessDefinition = async (id: number, key: string,token: string) => {
  return await request.get({
    url: '/bpm/process-definition/get',
    params: { id, key },
    headers: {"user-external" :token}
  })
}

export const getProcessDefinitionPage = async (params) => {
  return await request.get({
    url: '/bpm/process-definition/page',
    params
  })
}

export const getProcessDefinitionList = async (params) => {
  return await request.get({
    url: '/bpm/process-definition/list',
    params
  })
}
