import request from '@/config/axios'

// 获取模型领域统计数据
export function getModelAreaDomainStatistics (year)  {
  return request.post({
    url: `/model/modelInfoStatistics/modelAreaStatisticsData/${year}`
  })
}

// 获取已上线模型
export function getOnlineModelList (year, params){
  return request.post({
    url: `/model/modelInfoStatistics/alreadyLineModels/${year}`,
    params: params
  })
}


// 模型按主题统计数据
export function getModelByThemeStatistics (year) {
  return request.post({
    url: `/model/modelInfoStatistics/modelThemeStatisticsData/${year}`
  })
}

// 模型按预警统计数据
export function getModelByWarningStatistics (year) {
  return request.post({
    url: `/model/modelInfoStatistics/modelWarnStatisticsData/${year}`
  })
}

// 模型按月度统计数据
export function getModelByMonthStatistics (year) {
  return request.post({
    url: `/model/modelInfoStatistics/modelMonthStatisticsData/${year}`
  })
}


// 月度使用量top10数据
export function getModelUseTop10 (year) {
  return request.post({
    url: `/model/modelInfoStatistics/modelMonthUseTopTenData/${year}`
  })
}
