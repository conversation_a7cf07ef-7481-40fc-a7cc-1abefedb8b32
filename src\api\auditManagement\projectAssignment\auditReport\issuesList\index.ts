import request from '@/config/axios'

// 问题清单 VO
export interface AuditProblemsVO {
	id ?: number,
	projectId : string, //项目ID
	questionName : string,// 问题标题
	quesDigest : string,// 问题摘要
	auditSugg : string,// 审计建议
	quesTypeId : number,// 问题类型
	discoverUserId : number,// 发现人id
	abarbeitungFlag : number,// 是否整改
	handOverFlag : number,// 是否移交
}
export interface AuditProblemsDetailVO {
	id: number, 
    projectId: string, //项目ID
    questionCode: number,//问题编号id
    questionName: string,// 问题标题
    quesDigest: string,// 问题摘要
    auditSugg: string,// 审计建议
    quesTypeId: number,// 问题类型id
    quesTypeName: string //问题类型
    discoverUserId: number,// 发现人id
    discoverUserName: string, //发现人
    abarbeitungFlag: number,// 是否整改
    handOverFlag: number,// 是否移交
    createdBy: undefined, //创建人
    createdTime: undefined, //创建时间
    updatedBy: undefined, //更新人
    updatedTime: Date, //更新时间
}

export interface searchListVO {
    pageNo: number
    pageSize: number
    projectName?: string
    orgType?: number
    auditObject?: number
    auditType?: number
    auditYear?: string
    projStage?: number
    overseasFlag?: number
}

export interface AuditProblemsDeriveVO {
    projectId: string, //项目ID
    questionCode: string, //问题编号
    questionName: string,// 问题标题
    quesDigest: string,// 问题摘要
    auditSugg: string,// 审计建议
    quesTypeId: number,// 问题类型id
    quesTypeName: number, //问题类型
    discoverUserId: number,// 发现人id
    abarbeitungFlag: number,// 是否整改
    handOverFlag: number,// 是否移交
    createdBy: undefined, //创建人id
    createdTime: undefined, //创建时间
    updatedBy: undefined, //更新人
    updatedTime: undefined, //更新时间
}

// 问题清单 API
export const ListOfProblemsApi = {
	// 查询问题清单列表分页
	getListOfProblemsList: async (params : searchListVO) => {
		// return await request.post({ url: `/manage/proj-plan-project/page`, data })
        return await request.get({ url: `/manage/proj-plan-project/cert-page-by-project`, params })
	},
    // 问题列表接口
    getListOfProblemsProList: async (params : any) => {
		return await request.get({ url: `/manage/proj-assign-question/page`, params })
	},
	// 新增发现问题
	getListOfProblemsAdd: async (data : AuditProblemsVO) => {
		return await request.post({ url: `/manage/proj-assign-question/create`, data })
	},
	// 获取问题清单详情
	getListOfProblemsDetails: async (id : number) => {
		return await request.get({ url: `/manage/proj-assign-question/get?id=` + id })
	},
	// 修改问题清单详情
	getListOfProblemsUpdate: async (data : AuditProblemsDetailVO) => {
		return await request.put({ url: `/manage/proj-assign-question/update`, data })
	},
	// 删除问题清单
	getListOfProblemsDelete: async (id : number) => {
		return await request.delete({ url: `/manage/proj-assign-question/delete?id=` + id })
	},
    // 数据导入
    getListOfProblemsLead: async (params : any) => {
		return await request.post({ url: `/manage/proj-assign-question/import-excel`,params })
	},
    // 数据导出
    getListOfProblemsDownload: async(params : any) => {
        return await request.get({url: `/manage/proj-assign-question/export-excel`, params})
    },
    // 下载模版公用接口
    getListOfProblemsTemplate: async(type: string) => {
        return await request.get({ url: `/infra/file/getTemplate?templateType=${type}` })
    },

    // 问题类型数据
    getListOfProblemsType: async() => {
        // return await request.get({url: `/audit/tank-ques-qualitation/get-all-classification`})
		return await request.get({url: `/audit/tank-trees-type/get?type=5`})
    },

    // 查询问题定性库列表分页(新增时的问题标题)
	getQualitativeTitleList: async (params : any) => {
		return await request.get({ url: `/audit/tank-ques-qualitation/page`, params })
	},

    // 获取项目审计类型
    getListOfProblemsItemType: async (id : number) => {
		return await request.get({ url: `/manage/proj-plan-project/get?id=` + id })
	},

    // 获取项目是否配置重大线索程序
    getListOfProblemsHandOver: async (params: any) => {
        return await request.get({ url: `/manage/assign-prog-conf/page`, params})
    },

    // 获取取证单列表数据
    getListOfProblemsObtain: async (params: any) => {
        return await request.get({ url: `/manage/manage-cert/not-link-question`, params})
    },

    // 通过取证单选择新增问题清单
    getListOfProblemsObtainAdd: async(data: any) => {
        return await request.post({ url: `/manage/proj-assign-question/cert-link`, data })
    }
}   