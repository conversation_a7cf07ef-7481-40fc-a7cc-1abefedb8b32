import request from '@/config/axios'

// 审计报告征求意见稿 VO
export interface AuditProblemsVO {
    id?: number,
    projectId: string, //项目ID
    questionName: string,// 问题标题
    quesDigest: string,// 问题摘要
    auditSugg: string,// 审计建议
    quesTypeId: number,// 问题类型
    discoverUserId: number,// 发现人id
    abarbeitungFlag: number,// 是否整改
    handOverFlag: number,// 是否移交
}
export interface AuditProblemsDetailVO {
    id: number,
    projectId: string, //项目ID
    questionName: string,// 问题标题
    quesDigest: string,// 问题摘要
    auditSugg: string,// 审计建议
    quesTypeId: number,// 问题类型
    discoverUserId: number,// 发现人id
    abarbeitungFlag: number,// 是否整改
    handOverFlag: number,// 是否移交
    createdBy: undefined, //创建人id
    createdTime: undefined, //创建时间
    updatedBy: undefined, //更新人
    updatedTime: undefined, //更新时间
}

export interface searchListVO {
    pageNo: number
    pageSize: number
    projectName?: string
    orgType?: number
    auditObject?: number
    auditType?: number
    auditYear?: string
    projStage?: number
    overseasFlag?: number
}

// 审计报告征求意见稿 API
export const opinionsOnAuditReportApi = {
    // 审计报告征求意见稿分页
    getOpinionsOnAuditReportList: async (params: any) => {
        return await request.get({ url: `/manage/audit/report/page`, params })
    },
    // 审计报告征求意见稿分页
    updateOrAudit: async (data: any) => {
        return await request.post({ url: `/manage/audit/report/submit`, data })
    },
    // 获取projectId
    getProjId: async (id: any) => {
        return await request.get({ url: `/manage/audit/report/show/` + id })
    },
    // 提交弹窗查询流程相关内容
    getProcessContent: async (id: number) => {
        return await request.get({ url: `/manage/audit/report/show/${id}` })
    },


}
