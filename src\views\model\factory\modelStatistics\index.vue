<!-- 全景视图 -->
<template>
  <ContentWrap class="bgImg border-0">
    <div class="flex_title border-0!">
      <span class="font-size-18px! font-700"> 模型领域统计 </span>
      <div class="float_r">年度
        <el-date-picker v-model="year" type="year" size="default" style="width: 110px" value-format="YYYY"
          @change="changeDate" />
      </div>
    </div>
    <div class="w-100% flex flex-justify-around">
      <div class="w-19.9% h-150px m-12px flex">
        <div class="jb w-100% h-100% flex flex-content-around flex-justify-around flex-items-center">
          <div class="flex flex-items-center flex-col">
            <span>上线模型</span>
            <img src="@/assets/imgs/index/auditThinkTank/modelStatistics/<EMAIL>" alt=""
              class="w-64px h-64px m-15px" />
          </div>
          <div class="flex flex-col" style="text-align: center">
            <span class="font-size-28px font-700">{{ alreadyOnlineModelNumber || 0 }}</span>
            <span class="font-size-15px color-#999">已上线模型总数</span>
          </div>
        </div>
      </div>
      <div class="w-19.9% h-150px m-12px flex">
        <div class="jb w-100% h-100% flex flex-content-around flex-justify-around flex-items-center">
          <div class="flex flex-items-center flex-col">
            <span>在建模型</span>
            <img src="@/assets/imgs/index/auditThinkTank/modelStatistics/<EMAIL>" alt=""
              class="w-64px h-64px m-15px" />
          </div>
          <div class="flex flex-col" style="text-align: center">
            <span class="font-size-28px font-700">{{ buildingModelNumber || 0 }}</span>
            <span class="font-size-15px color-#999">建设中</span>
          </div>
        </div>
      </div>
      <div class="w-19.9% h-150px m-12px flex">
        <div class="jb w-100% h-100% flex flex-content-around flex-justify-around flex-items-center">
          <div class="flex flex-items-center flex-col">
            <span>{{ modelClassifyData[0] && modelClassifyData[0].className }}</span>
            <img src="@/assets/imgs/index/auditThinkTank/modelStatistics/<EMAIL>" alt=""
              class="w-64px h-64px m-15px" />
          </div>
          <div class="flex flex-col" style="text-align: center">
            <span class="font-size-28px font-700">{{
              modelClassifyData[0] ? modelClassifyData[0].modelNumber : 0
            }}</span>
            <span class="font-size-15px color-#999">{{ modelClassifyData[0] && modelClassifyData[0].className }}类</span>
          </div>
          <div class="flex flex-col" style="text-align: center">
            <span class="font-size-28px font-700">{{ modelClassifyData[0] ? modelClassifyData[0].modelRate : 0
              }}%</span>
            <span class="font-size-15px color-#999">占比</span>
          </div>
        </div>
      </div>
      <div class="w-19.9% h-150px m-12px flex">
        <div class="jb w-100% h-100% flex flex-content-around flex-justify-around flex-items-center">
          <div class="flex flex-items-center flex-col">
            <span>{{ modelClassifyData[1] && modelClassifyData[1].className }}</span>
            <img src="@/assets/imgs/index/auditThinkTank/modelStatistics/<EMAIL>" alt=""
              class="w-64px h-64px m-15px" />
          </div>
          <div class="flex flex-col" style="text-align: center">
            <span class="font-size-28px font-700">{{
              modelClassifyData[1] ? modelClassifyData[1].modelNumber : 0
            }}</span>
            <span class="font-size-15px color-#999">{{ modelClassifyData[1] && modelClassifyData[1].className }}类</span>
          </div>
          <div class="flex flex-col" style="text-align: center">
            <span class="font-size-28px font-700">{{ modelClassifyData[1] ? modelClassifyData[1].modelRate : 0
              }}%</span>
            <span class="font-size-15px color-#999">占比</span>
          </div>
        </div>
      </div>
      <div class="w-19.9% h-150px m-12px flex">
        <div class="jb w-100% h-100% flex flex-content-around flex-justify-around flex-items-center">
          <div class="flex flex-items-center flex-col">
            <span>{{ modelClassifyData[2] && modelClassifyData[2].className }}</span>
            <img src="@/assets/imgs/index/auditThinkTank/modelStatistics/<EMAIL>" alt=""
              class="w-64px h-64px m-15px" />
          </div>
          <div class="flex flex-col" style="text-align: center">
            <span class="font-size-28px font-700">{{
              modelClassifyData[2] ? modelClassifyData[2].modelNumber : 0
            }}</span>
            <span class="font-size-15px color-#999">{{ modelClassifyData[2] && modelClassifyData[2].className }}类</span>
          </div>
          <div class="flex flex-col" style="text-align: center">
            <span class="font-size-28px font-700">{{ modelClassifyData[2] ? modelClassifyData[2].modelRate : 0
              }}%</span>
            <span class="font-size-15px color-#999">占比</span>
          </div>
        </div>
      </div>
    </div>
  </ContentWrap>
  <div class="flex w-100%">
    <ContentWrap class="w-12% m-r-15px">
      <el-input v-model="modelName" style="width: 100%" placeholder="请输入" class="input-with-select">
        <template #append>
          <el-button size="small" @click="refreshModelList">
            <Icon icon="ep:search" class="mr-5px" />
          </el-button>
        </template>
      </el-input>
      <el-menu default-active="2" style="border-right: 0">
        <el-menu-item v-for="(menuItem, index) in modelList" :key="menuItem.id" :index="index + 1" class="p-l-15px!">
          <span @click="openModelModal(menuItem)">{{ menuItem.modelName }}</span>
        </el-menu-item>
      </el-menu>
    </ContentWrap>
    <div class="w-85%">
      <el-row :gutter="16">
        <el-col :span="12" :xs="24">
          <ContentWrap>
            <div class="flex_title"><span class="shut"></span>按主题统计</div>
            <div class="w-100% overflow-auto">
              <div :class="bottomChart" ref="bottomChartRef" style="height: 250px; min-width: 100%; width: auto"></div>
            </div>
          </ContentWrap>
        </el-col>
        <el-col :span="12" :xs="24">
          <ContentWrap>
            <div class="flex_title"><span class="shut"></span>按预警统计</div>
            <div>
              <div :class="topTenChart" ref="topTenChartRef" style="height: 250px; width: 100%"></div>
            </div>
          </ContentWrap>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12" :xs="24">
          <ContentWrap>
            <div class="flex_title"><span class="shut"></span>模型月度统计</div>
            <div>
              <div :class="rightChart" ref="rightChartRef" style="height: 250px; width: 100%"></div>
            </div>
          </ContentWrap>
        </el-col>
        <el-col :span="12" :xs="24">
          <ContentWrap>
            <div class="flex_title"><span class="shut"></span>模型使用top10</div>
            <div>
              <div :class="bottomChart2" ref="bottomChartRef2" style="height: 250px; width: 100%"></div>
            </div>
          </ContentWrap>
        </el-col>
      </el-row>
    </div>
  </div>
  <SqlModelResult ref="sqlResultRef" />
</template>

<script lang="ts" setup>
import * as echarts from 'echarts'
import * as statisticsApi from '@/api/model/statistics'
import SqlModelResult from "../construction/components/SqlModelResult.vue";
import * as modelApi from "@/api/model/info";
import router from "@/router";

defineOptions({
  name: 'ModelStatistics'
})

const message = useMessage()
const queryParams = reactive({
  id: undefined,
  auditRoleName: undefined,
  pageSize: 10,
  pageNo: 1,
  projectPhase: undefined,
  date: undefined,
  status: undefined
})
const queryFormRef = ref() // 搜索的表单
const loading = ref(true) // 列表的加载中
const year = ref('')
onMounted(async () => {
  year.value = new Date().getFullYear().toString()
  await count(year.value)
  await getChartData(year.value)
  getAllApi()
})

const count = async (year) => {
  await statisticsApi.getModelAreaDomainStatistics(year).then((res) => {
    if (res) {
      alreadyOnlineModelNumber.value = res.alreadyOnlineModelNumber
      buildingModelNumber.value = res.buildingModelNumber
      modelClassifyData.value = res.modelClassifyData
    }
  })
}

const modelName = ref('')
const modelList = ref([])
const leftTopXAxis = ref([])
const leftTopSeries = ref([])
const rightTopData = ref([])
const leftBottomXAxis = ref([])
const leftBottomData = ref([])
const rightBottomXAxis = ref([])
const rightBottomData = ref([])
const salvProMax = ref([])

const getChartData = async (year) => {
  await statisticsApi.getOnlineModelList(year, {}).then((res) => {
    modelList.value = res
  })
  const res = await statisticsApi.getModelByThemeStatistics(year)
  leftTopXAxis.value = res.xaxisData
  yLength.value = res.xaxisData.length
  leftTopSeries.value = res.seriesData
  await statisticsApi.getModelByWarningStatistics(year).then((res) => {
    rightTopData.value = res
  })

  await statisticsApi.getModelByMonthStatistics(year).then((res) => {
    leftBottomXAxis.value = res.xaxisData
    leftBottomData.value = res.seriesData
  })

  await statisticsApi.getModelUseTop10(year).then((res) => {
    rightBottomXAxis.value = res.seriesData
    rightBottomData.value = res.yaxisData

    const data = rightBottomXAxis.value;
    console.log(data)
    if (data.length === 0) {
      console.warn('rightBottomData is empty.');
      return;
    }

    // 确保所有元素都是数字
    const numbers = data.map(item => Number(item)).filter(num => !isNaN(num));

    if (numbers.length === 0) {
      console.warn('No valid numbers found in rightBottomData.');
      return;
    }

    const maxValue = Math.max(...numbers);

    data.forEach(item => {
      salvProMax.value.push(maxValue)
    })

  })
}

const refreshModelList = () => {
  const params = { modelName: modelName.value }
  statisticsApi.getOnlineModelList(year.value, params).then((res) => {
    modelList.value = res
  })
}

const sqlResultRef = ref()
const openModelModal = async (data) => {
  if ('sql' === data.buildMethod) {
    sqlResultRef.value.open(data.id, data.modelName)
  } else {
    const params = {
      modelId: data.id
    }
    const modelData = await modelApi.getModelInfo(data.id)
    const path = modelData.pageLink
    if (path) {
      await router.push({
        path: path,
        query: params
      })
    }
  }
}

const yLength = ref()
const changeDate = async (value) => {
  if (!value) {
    message.error("年度不能为空！")
  } else {
    year.value = value
    count(value)
    await getChartData(year.value)
    getAllApi()
  }
}

const alreadyOnlineModelNumber = ref(Number)
const buildingModelNumber = ref(Number)
const modelClassifyData = ref([])

let topTenChart = null
const topTenChartRef = ref<InstanceType<typeof ElTree>>()
const getTopTenChart = async () => {
  topTenChart = echarts.init(topTenChartRef.value)
  const data = rightTopData.value
  topTenChart.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)'
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      y: 'center',
      data: data
    },
    series: [
      {
        name: '预警占比',
        type: 'pie',
        radius: '55%',
        center: ['40%', '50%'],
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  })
}

let rightChart = null
const rightChartRef = ref<InstanceType<typeof ElTree>>()
const getRightChart = async () => {
  rightChart = echarts.init(rightChartRef.value)
  rightChart.setOption({
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      y: 'bottom',
      data: ['固化模型', 'SQL模型']
    },
    grid: {
      left: '3%',
      right: '4%',
      top: '11%',
      bottom: '11%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: leftBottomXAxis.value
    },
    yAxis: {
      type: 'value',
      minInterval: 1
    },
    series: leftBottomData.value
  })
}

const activeName = ref('0')
const editableTabs = ref([
  {
    title: '问题数据',
    name: '0'
  },
  {
    title: '涉及金额',
    name: '1'
  }
])
const handleClick = () => { }
let bottomChart = null
const bottomChartRef = ref<InstanceType<typeof ElTree>>()
const getBottomChart = async () => {
  bottomChart = echarts.init(bottomChartRef.value)
  bottomChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      containLabel: true
    },
    xAxis: {
      data: leftTopXAxis.value,
      // data: ['合3333同', '采3333购', '财333务', '工3333程', '其3333他', '其333他', '其333他', '其333他'],
      axisLabel: {
        interval: 0, //横轴信息全部显
        show: true
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1
    },
    dataZoom: [
      {
        // 设置滚动条的隐藏与显示
        show: true,
        // 设置滚动条类型
        type: 'slider',
        startValue: 0,
        // 数据窗口范围的结束数值（一页显示多少条数据）
        endValue: 5,
        // empty：当前数据窗口外的数据，被设置为空。
        // 即不会影响其他轴的数据范围
        filterMode: 'empty',
        // 设置滚动条宽度，相对于盒子宽度
        width: '50%',
        // 设置滚动条高度
        height: 11,
        // 设置滚动条显示位置
        left: 'center',
        // 是否锁定选择区域（或叫做数据窗口）的大小
        zoomLoxk: true,
        // 控制手柄的尺寸
        handleSize: 0,
        // dataZoom-slider组件离容器下侧的距离
        bottom: 6
      },
      {
        // 没有下面这块的话，只能拖动滚动条，
        // 鼠标滚轮在区域内不能控制外部滚动条
        type: 'inside',
        // 滚轮是否触发缩放
        zoomOnMouseWheel: false,
        // 鼠标滚轮触发滚动
        moveOnMouseMove: true,
        moveOnMouseWheel: true
      }
    ],
    series: [
      {
        type: 'bar',
        barWidth: '50px',
        stack: 'total',
        label: {},
        itemStyle: {
          normal: {
            barBorderRadius: [8, 8, 0, 0]
          },
          color: '#2A8AF7'
        },
        data: leftTopSeries.value
      }
    ]
  })
}
let bottomChart2 = null
const bottomChartRef2 = ref<InstanceType<typeof ElTree>>()
const getBottomChart2 = async () => {
  console.log(salvProMax.value)
  bottomChart2 = echarts.init(bottomChartRef2.value)
  bottomChart2.setOption({
    tooltip: {
        show:"true",
        trigger: 'axis',
        axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
        }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      show: false // 是否显示
    },
    yAxis: {
      type: 'category',
      // 坐标轴刻度
      axisTick: {
        show: false // 是否显示坐标轴刻度 默认显示
      },
      // 坐标轴轴线
      axisLine: {
        // 是否显示坐标轴轴线 默认显示
        show: false // 是否显示坐标轴轴线 默认显示
      },
      axisLabel: {
        inside: true,
        color: '#fff'
      },
      z: 10,
      data: rightBottomData.value,
      inverse: true,
      animationDuration: 300,
      animationDurationUpdate: 300
    },
    series: [

      {
        realtimeSort: true,
        type: 'bar',
        itemStyle: {
          normal: {
            barBorderRadius: 0,
            color: '#5087EC',
          }
        },
        label: {
          show: false,
          position: 'right',
          valueAnimation: true
        },
        barWidth: 26,

        data: rightBottomXAxis.value
      },

      {
        name: '',
        type: 'bar',
        barWidth: 26,
        z: 0,
        barGap: '-100%',
        data: salvProMax.value,

        itemStyle: {
          normal: {
            barBorderRadius: 0,
            color: 'rgba(0, 0, 0, 0.25)' //rgba设置透明度0.14
          }
        },

        label: {
          show: true,
          position: 'right',
          distance: 30,
          align: "right",
          formatter: function (params) {
            return rightBottomXAxis.value[params.dataIndex];
          },
          color: '#02afff',
          fontSize: 18,
        },


      },

    ],
    legend: {
      show: true
    }
  })
}

const getAllApi = async () => {
  await Promise.all([getTopTenChart()])
  await Promise.all([getRightChart()])
  await Promise.all([getBottomChart()])
  await Promise.all([getBottomChart2()])
  loading.value = false
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
</script>

<style scoped>
.bgImg {
  /* bg_mxtj.png */
  background-image: url('@/assets/imgs/bg_mxtj.png');
  background-size: cover;
}

.shut {
  padding-left: 5px;
  border-left: 3px solid rgb(96 165 250);
  height: 15px;
}

.float_r {
  position: relative;
  margin-left: calc(100% - 260px);
}

.jb {
  background-color: rgba(246, 247, 254, 0.7);
  border-radius: 8px;
  border: 1px solid #ffffff;
}

.input-with-select .el-input-group__prepend {
  background-color: var(--el-fill-color-blank);
}

.flex_title {
  display: flex;
  align-items: center;
  line-height: 30px;
  border-bottom: 1px solid #cacaca;
}

/deep/ .el-tabs__nav-wrap::after {
  /* 去掉下划线 */
  position: static !important;
}
</style>
