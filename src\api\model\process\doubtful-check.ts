import request from '@/config/axios'

export const DoubtfulCheckAPI = {
  /**
   * 获取疑点信息
   * @param id
   */
  getDoubtfulInfo: async (id: string) => {
    return request.post({url: `/model/doubtfulInfo/doubtfulInfoData/${id}`})
  },

  /**
   * 获取疑点核查附件
   * @param businessId
   */
  getDoubtfulCheckFiles: async (businessId: string) => {
    return request.post({url: `/model/fileInfo/targetBusinessFiles/${businessId}`})
  },

  /**
   * 上传数智作业附件
   * @param data
   */
  uploadModelFile: async (data: any) => {
    return request.upload({url: '/model/fileInfo/uploadModelFile', data})
  },

  /**
   * 删除数智作业附件
   * @param id
   */
  deleteModelFile: async (id: string) => {
    return request.post({url: `/model/fileInfo/deleteModelFile/${id}`})
  },

  /**
   * 保存疑点核查结果
   * @param data
   */
  saveDoubtfulResultCheck: async (data: any) => {
    return request.post({url: '/model/doubtfulInfo/saveDoubtfulCheckResult', data})
  }
}
