import request from '@/config/axios'

// 根据indexId 获取财务指标
export function getFinanceIndexById(indexId) {
  return request.post({
    url: '/model/financeAnalysisCommonParameter/targetFinanceIndexMapAccounts/' + indexId,
  })
}

// 获取财务科目树数据
export function getFinanceSubjectTreeData() {
  return request.post({
    url: '/model/financeAnalysisCommonParameter/financeAccountTreeData'
  })
}

// 获取财务科目分析图形数据
export function getFinanceSubjectAnalysisChartData(data) {
  return request.post({
    url: '/model/financeAccountAnalysis/financeAccountAnalysisChartData',
    data: data
  })
}

//获取财务科目动态表头
export function getFinanceSubjectTableHeader(data) {
  return request.post({
    url: '/model/financeAccountAnalysis/financeAccountActiveHeader',
    data: data
  })
}

// 获取财务科目分析表格数据
export function getFinanceSubjectTableData(data) {
  return request.post({
    url: '/model/financeAccountAnalysis/financeAccountPageData',
    data: data
  })
}

/**
 * 获取默认的财务科目编码
 * @returns {*}
 */
export function defaultFinanceAccountCodes() {
  return request.post({
    url: '/model/financeAnalysisCommonParameter/defaultFinanceAccountCodes'
  })
}
