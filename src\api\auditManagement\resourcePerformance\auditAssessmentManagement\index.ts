import request from '@/config/axios'

export interface CreateAssessmentVo {
	taskYear: string
	taskName: string
	details: []
}
export interface SearchPersonVo {
	pageNo: number
	pageSize: number
	userName: string
	deptName: string
}

// 审计考核管理 API
export const AuditAssessmentManageApi = {
	// 查询审计考核管理列表分页
	getAuditAssessmentManageList: async (params: any) => {
		return await request.get({ url: `/manage/merits-audit-user-task/page`, params })
	},
	createAssessment: async (data: CreateAssessmentVo) => {
		return await request.post({ url: `/manage/merits-audit-user-task/create`, data })
	},
	getAuditPersonList: async (params: SearchPersonVo) => {
		return await request.get({ url: `/manage/merits-audit-user/list`, params })
	},
	getAssessMangePersonList: async (id: number) => {
		return await request.get({ url: `/manage/merits-audit-user-task/detail?id=${id}`, })
	},

}