import request from '@/config/axios'


// 加入疑点箱 保存单条信息
export function saveDoubtInfo(data) {
  return request.post({
    url: '/model/doubtfulInfo/saveFoundDoubtfulInfo',
    data: data
  })
}


//获取疑点信息分页数据
export function getDoubtfulInfoPage(queryParams) {
  return request.post({
    url: '/model/doubtfulInfo/doubtfulInfoPageData',
    data: queryParams
  })
}

// 删除疑点数据
export function deleteDoubtfulInfo(id) {
  return request.post({
    url: '/model/doubtfulInfo/deleteDoubtfulInfoData/' + id
  })
}

// 获取疑点统计数量
export function getDoubtfulInfoCount(data) {
  return request.post({
    url: '/model/doubtfulInfo/doubtfulStatisticsData',
    data:data
  })
}

// 加入疑点箱页面提交
export function batchStartDoubtfulCheckFlow(id){
  return request.post({
    url: '/model/doubtfulInfo/batchStartDoubtfulCheckFlow/' + id
  })
}
