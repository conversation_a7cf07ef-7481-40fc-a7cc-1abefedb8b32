import request from '@/config/axios'

// 项目阶段配置 API
export const ConfigStageApi = {
	// 查询项目阶段配置列表分页
	getConfigStageList: async (params : any) => {
		return await request.get({ url: `/system/config-stage/page`, params })
	},
	// 新增
	configStageCreate: async (data : any) => {
		return await request.post({ url: `/system/config-stage/create`, data })
	},
	// 查询
	configStageGet: async (id : any) => {
		return await request.get({ url: `/system/config-stage/get?id=${id}` })
	},
	// 更新
	configStageUpdate: async (data : any) => {
		return await request.put({ url: `/system/config-stage/update`, data })
	},
	// 删除
	configStageDelete: async (id : any) => {
		return await request.delete({ url: `/system/config-stage/delete?id=${id}` })
	},
}
