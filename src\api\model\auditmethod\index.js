import request from '@/config/axios'

// 创建数智作业-审计方法信息表;
export function createModelAuditMethodInfo(data) {
  return request.post({
    url: '/model/model-audit-method-info/create',
    data: data
  })
}

// 更新数智作业-审计方法信息表;
export function updateModelAuditMethodInfo(data) {
  return request.put({
    url: '/model/model-audit-method-info/update',
    data: data
  })
}

// 删除数智作业-审计方法信息表;
export function deleteModelAuditMethodInfo(id) {
  return request.delete({
    url: '/model/model-audit-method-info/delete?id=' + id,
  })
}

// 获得数智作业-审计方法信息表;
export function getModelAuditMethodInfo(id) {
  return request.get({
    url: '/model/model-audit-method-info/get?id=' + id,
  })
}

// 获得数智作业-审计方法信息表;分页
export function getModelAuditMethodInfoPage(params) {
  return request.get({
    url: '/model/model-audit-method-info/page',
    params
  })
}

// 导出数智作业-审计方法信息表; Excel
export function exportModelAuditMethodInfoExcel(params) {
  return request.get({
    url: '/model/model-audit-method-info/export-excel',
    params,
    responseType: 'blob'
  })
}
