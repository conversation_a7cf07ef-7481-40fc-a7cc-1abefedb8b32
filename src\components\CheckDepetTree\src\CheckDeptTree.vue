<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form ref="formRef" v-loading="formLoading" :model="formData">
      <el-form-item>
        <el-card class="cardHeight">
          <el-tree
            ref="treeRef"
            :data="deptList"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            :props="defaultProps"
            :check-strictly='checkStrictly'
            :default-expand-all="false"
            empty-text="加载中，请稍候"
            node-key="id"
            @node-click="handleNodeClick"
            :load="loadNode"
            :default-expanded-keys="defaultExpandedKeys"
            lazy
            accordion
            show-checkbox
            @check-change="handleCheckChange"
          />
        </el-card>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { defaultProps, handleTree, handleLazyTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import type Node from 'element-plus/es/components/tree/src/model/node'
interface DeptNode {
  id: number
  masterOrgId?: number | string
  name: string
  parentId: number | string
  children?: DeptNode[]
  isLeaf?: boolean
}
defineOptions({ name: 'CheckDeptTree' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用

const formRef = ref() // 表单 Ref
const menuOptions = ref<any[]>([]) // 菜单树形结构
const menuExpand = ref(false) // 展开/折叠
const treeRef = ref() // 菜单树组件 Ref
const treeNodeAll = ref(false) // 全选/全不选

const queryParams = ref({})
const emits = defineEmits(['node-click', 'success'])
/** 打开弹窗 */
const checkedIds = ref<number[]>([])
const props = defineProps({
    checkStrictly: {
        default: false,
        type: Boolean
    }
})
const dialogTitle = ref('')
const open = async (type: string = '新增', id: number) => {
  formLoading.value = true
  dialogVisible.value = true
  dialogTitle.value = type
  resetForm()
  // 加载 Menu 列表。注意，必须放在前面，不然下面 setChecked 没数据节点
  // 设置数据
  try {
    await getTree(0)
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
const filterNode = (name: string, data: DeptNode) => {
  if (!name) return true
  return data.name.includes(name)
}
const handleCheckChange = (data: Tree, checked: boolean, indeterminate: boolean) => {
  if (checked) {
    // 添加到已选择节点 ID 数组
    checkedIds.value.push(data.id)
  } else {
    // 从已选择节点 ID 数组中移除
    checkedIds.value = checkedIds.value.filter((id) => id !== data.id)
  }
}
const selectedNodes = ref([])

/** 提交表单 */
const submitForm = async () => {
  formLoading.value = true
  let checkedArr = treeRef.value.getCheckedNodes()
  if (checkedArr && checkedArr.length === 0) {
    message.error('请选择数据')
    return
  }
  try {
    dialogVisible.value = false
    emits('success', checkedArr)
  } catch (error) {
  } finally {
    formLoading.value = false
  }
}
const defaultExpandedKeys = ref<(string | number)[]>([])
/** 获得部门树 */
const getTree = async (id) => {
  deptList.value = []
  const res = await DeptApi.getSimpleDeptList(id)
  deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
  defaultExpandedKeys.value = deptList.value.map((node) => node.id)
  console.log(defaultExpandedKeys.value)
}
const loadNode = async (node: Node, resolve: (data: any[]) => void) => {
  try {
    const nodeId = node.data.id
    if (nodeId == undefined || nodeId == null) {
      return
    }
    const res = await DeptApi.getSimpleDeptList(nodeId)
    const children = handleLazyTree(res, 'id', 'parentId', 'children')
    resolve(children)
  } catch (error) {
    resolve([])
  }
}
const handleNodeClick = async (row: { [key: string]: any }) => {
  emits('node-click', row)
}

const deptList = ref<DeptNode[]>([]) // 树形结构
/** 重置表单 */
const resetForm = () => {
  // 重置选项
  treeNodeAll.value = false
  menuExpand.value = false

  treeRef.value?.setCheckedNodes([])
  formRef.value?.resetFields()
}
</script>
<style lang="scss" scoped>
.cardHeight {
  width: 100%;
  max-height: 400px;
  overflow-y: scroll;
}
</style>
