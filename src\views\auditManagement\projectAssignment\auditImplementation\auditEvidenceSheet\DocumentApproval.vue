
<template>
  <Dialog :title="dialogTitle" :scroll="true" v-model="dialogVisible" width="85%" maxHeight="70vh">
    <el-row :gutter="16">
      <el-col :span="16">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane label="待办处理" name="0">
            <div class="detail-title common-border-left-blue">
              <span>迎审文件接收</span>
            </div>

            <el-table border :data="list" :stripe="true" :show-overflow-tooltip="true">
              <el-table-column label="#" width="50" align="center">
                <template
                  #default="{ $index }"
                >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
              </el-table-column>
              <el-table-column label="文档类型" align="center" prop="name" />

              <el-table-column label="文档名称" align="center" prop="name" min-width="120" />
              <el-table-column label="上传人" align="center" prop="name" />
              <el-table-column label="上传时间" align="center" prop="name" />
              <el-table-column label="是否接收" align="center" prop="name" />
              <el-table-column label="接收时间" align="center" prop="name" />
              <el-table-column label="操作" align="center" :width="150" fixed="right">
                <template #default="scope">
                  <el-button
                    type="primary"
                    link
                    @click="openForm('update', scope.row.id)"
                  >下载</el-button>
                  <el-button
                    type="primary"
                    link
                    @click="openForm('detail', scope.row.id)"
                  >预览</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="detail-title common-border-left-blue">
              <span>项目基本信息</span>
            </div>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="项目名称">{{ detailData.auditRoleCode }}</el-descriptions-item>
              <el-descriptions-item label="项目编号">{{ detailData.auditRoleCode }}</el-descriptions-item>
              <el-descriptions-item label="审计类型">{{ detailData.auditRoleCode}}</el-descriptions-item>
              <el-descriptions-item label="立项年度">{{ detailData.auditRoleCode }}</el-descriptions-item>
              <el-descriptions-item label="组织方式">{{ detailData.auditRoleCode }}</el-descriptions-item>
              <el-descriptions-item label="时间计划">{{ detailData.auditRoleCode}}</el-descriptions-item>
              <el-descriptions-item label="审计期间">{{ detailData.auditRoleCode}}</el-descriptions-item>
              <el-descriptions-item label="开展事项">{{ detailData.auditRoleCode}}</el-descriptions-item>
              <el-descriptions-item label="整体报告">{{ detailData.auditRoleCode}}</el-descriptions-item>
              <el-descriptions-item label="公司领导">{{ detailData.auditRoleCode}}</el-descriptions-item>
              <el-descriptions-item label="曾任职务">{{ detailData.auditRoleCode}}</el-descriptions-item>
              <el-descriptions-item label="发文编号">{{ detailData.auditRoleCode}}</el-descriptions-item>
              <el-descriptions-item label="是否境外">{{ detailData.auditRoleCode}}</el-descriptions-item>
              <el-descriptions-item label="是否重要">{{ detailData.auditRoleCode}}</el-descriptions-item>
              <el-descriptions-item label="实施单位">{{ detailData.auditRoleCode}}</el-descriptions-item>
              <el-descriptions-item label="立项依据" :span="4">{{ detailData.auditRoleCode}}</el-descriptions-item>
            </el-descriptions>
            <div class="mb-10px mt-10px flex_center">
              <div class="flex_center fles_interval title common-border-left-blue">
                审计对象信息
              </div>
              <div>
                <el-button type="primary" plain @click="showTeamDetail">查看小组信息</el-button>
              </div>
            </div>
            <el-table border :data="list" :stripe="true" :show-overflow-tooltip="true">
              <el-table-column label="#" width="50" align="center">
                <template
                  #default="{ $index }"
                >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
              </el-table-column>
              <el-table-column label="项目编号" align="left" prop="name"  min-width="180"/>
              <el-table-column label="项目名称" align="center" prop="name" min-width="120" />
              <el-table-column label="审计对象" align="center" prop="name" />
              <el-table-column label="组长" align="center" prop="name" />
              <el-table-column label="项目状态" align="center" prop="name" />
              <el-table-column label="操作" align="center" :width="180" fixed="right">
                <template #default="scope">
                  <el-button type="primary" link @click="handleShowMessage(scope.row.id)">小组信息</el-button>
                  <el-button type="danger" link @click="handleCreate">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <Pagination
              :total="total"
              v-model:page="queryParams.pageNo"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
            <div class="mb-10px mt-10px flex_center">
              <div class="flex_center fles_interval title common-border-left-blue">
                审计资料清单
              </div>
              <div>
                <el-button type="primary" plain>附件上传</el-button>
              </div>
            </div>
            <el-table border :data="list" :stripe="true" :show-overflow-tooltip="true">
              <el-table-column label="#" width="50" align="center">
                <template
                  #default="{ $index }"
                >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
              </el-table-column>
              <el-table-column label="文档名称" align="center" prop="name" min-width="120" />
              <el-table-column label="文档类型" align="center" prop="name" />
              <el-table-column label="编制单位" align="center" prop="name" />
              <el-table-column label="编制人" align="center" prop="name" />
              <el-table-column label="生成时间" align="center" prop="name" />
              <el-table-column label="操作" align="center" :width="150" fixed="right">
                <template #default="scope">
                  <el-button
                    type="primary"
                    link
                    @click="openForm('update', scope.row.id)"
                  >下载</el-button>
                  <el-button
                    type="primary"
                    link
                    @click="openForm('detail', scope.row.id)"
                  >预览</el-button>
                  <el-button
                    type="primary"
                    link
                    @click="openForm('detail', scope.row.id)"
                  >编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="流程历史" name="1">流程历史</el-tab-pane>
          <el-tab-pane label="流程图" name="2">流程图</el-tab-pane>
        </el-tabs>
      </el-col>
      <el-col :span="8">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="110px"
          label-position="top"
          v-loading="formLoading"
        >
          <el-form-item label="流程审批">
            <template #label="{label}">
              <Icon icon="gravity-ui:branches-right" />
              <span class="ml-4px">{{label}}</span>
            </template>
          </el-form-item>

          <el-form-item label="处理意见" prop="auditRoleName">
            <el-input
              v-model="formData.auditRoleName"
              placeholder="请输入处理意见"
              readonly
              type="textarea"
            />
          </el-form-item>
          <el-form-item label="操作" prop="status">
            <template #label="{label}">
              <Icon icon="fe:edit" />
              <span class="ml-4px">{{label}}</span>
            </template>
            <el-radio-group v-model="formData.radio1">
              <el-radio value="1" size="large">终止</el-radio>
              <el-radio value="2" size="large">转派</el-radio>
              <el-radio value="3" size="large">退回</el-radio>
              <el-radio value="4" size="large">提交</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="下一环" prop="status">
            <template #label="{label}">
              <Icon icon=" ri:menu-unfold-3-line" />
              <span class="ml-4px">{{label}}</span>
            </template>

            <span>年度审计计划填报</span>
          </el-form-item>
          <el-form-item label="处理人" prop="status">
            <template #label="{label}">
              <Icon icon="octicon:people-24" />
              <span class="ml-4px">{{label}}</span>
            </template>
            <el-select v-model="formData.status" placeholder="请选择处理人" clearable class="!w-200px">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="通知方" prop="status">
            <template #label="{label}">
              <Icon icon="iwwa:alarm" />
              <span class="ml-4px">{{label}}</span>
            </template>
            <el-radio-group v-model="formData.radio1">
              <el-radio value="1" size="large">钉钉</el-radio>
              <el-radio value="2" size="large">短信</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="说明">
            <div>1、处理如1人,则默认</div>
          </el-form-item>
          <el-form-item>
            <div>2、处理如多人,则需要选</div>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <template #footer>
      <el-button @click="handleConfirmUnit" type="primary" :disabled="formLoading">保存</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">提交</el-button>
      <el-button @click="dialogVisible = false">取消</el-button>
    </template>
  </Dialog>

  <!-- <Dialog :title="dialogListTitle" v-model="dialogListVisible" width="70%">

  </Dialog>-->
  <!-- 文件上传 -->
  <!-- <FileForm ref="formImgRef" @success="handleUploadSuccess" :type="fileType" :limit="fileLimit" />
  <ConfirmUnit ref="confirmUnitRef" />
  <PlanFillingStatus ref="planFillingStatus" />
  <AdjustModal ref="adjustModal" />-->
  <TeamDetail ref="teamDetailRef" />
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { AuditRoleApi, AuditRoleVO } from '@/api/basicData/auditRole'
import FileForm from '@/views/infra/file/FileForm.vue'
// import ConfirmUnit from './ConfirmUnit.vue'
// import PlanFillingStatus from './PlanFillingStatus.vue'
// import AdjustModal from './AdjustModal.vue'
import TeamDetail from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/TeamDetail.vue'
import { propTypes } from '@/utils/propTypes'
/** 审计角色 表单 */
defineOptions({ name: 'DocumentApproval' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  auditRoleName: undefined,
  parentId: undefined,
  sort: undefined,
  status: undefined,
  projectPhase: undefined,
  date: undefined,
  content: undefined,
  wbflfgList: [],
  nbgzzdList: [],
  sjmxList: []
})
const list = ref()
const formRules = reactive({
  auditRoleName: [{ required: true, message: '不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  parentId: [{ required: true, message: '父级编号不能为空', trigger: 'change' }]
})
const props = defineProps({
  isApprove: propTypes.bool.def(true)
})
const formRef = ref() // 表单 Ref
const adjustModal = ref()
const showPlanAdjust = () => {
  adjustModal.value.open(0)
}
const teamDetailRef = ref()
const showTeamDetail = () => {
  teamDetailRef.value.open()
}
const activeName = ref('0')
const handleClick = () => {}
const planFillingStatus = ref()
const showPlanStatus = () => {
  planFillingStatus.value.open('detail', -1)
}
const formImgRef = ref()
const fileType = ref('img')
const fileLimit = ref(1)
// 上传方法
const handleImport = (type: string) => {
  fileType.value = type
  fileLimit.value = type === 'file' ? 5 : 1
  formImgRef.value.open()
}
const handleUploadSuccess = (fileList) => {
  let fileArr =
    fileList && fileList.length > 0
      ? fileList.map((item) => {
          return {
            url: item.response.data,
            name: item.name
          }
        })
      : []
  if (fileType.value === 'file') {
    formData.value.attachments = formData.value.attachments.concat(...fileArr)
  } else if (fileType.value === 'img') {
    formData.value.showImg = fileArr
    formRef.value.validateField('showImg')
  }
}
const handleDelete = async (type: string, index: number = 0) => {
  await message.delConfirm()
  formData.value[type].splice(index, 1)
  await message.success(t('common.delSuccess'))
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('提交')
  // formType.value = type
  // resetForm()
  // // 修改时，设置数据
  // if (id) {
  //   formLoading.value = true
  //   try {
  //     formData.value = await AuditRoleApi.getAuditRole(id)
  //   } finally {
  //     formLoading.value = false
  //   }
  // }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// const dialogListTitle = ref('') // 选择弹窗的标题
// const dialogListVisible = ref(false) // 选择弹窗的是否展示

const openWbList = () => {
  // dialogListVisible.value = true
}

const handleWbDelete = (val) => {}

const handleWbView = (val) => {}

const openNbList = () => {}
const handleNbDelete = () => {}
const handleNbView = () => {}

const openMxList = () => {}
const handleMxDelete = () => {}
const handleMxView = () => {}
const detailData = ref({
  auditRoleCode: 0
})
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AuditRoleVO
    if (formType.value === 'create') {
      await AuditRoleApi.createAuditRole(data)
      message.success(t('common.createSuccess'))
    } else {
      await AuditRoleApi.updateAuditRole(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
const handleConfirmUnit = () => {}
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    auditRoleName: undefined,
    parentId: undefined,
    sort: undefined,
    status: undefined,
    projectPhase: undefined,
    date: undefined,
    content: undefined,
    wbflfgList: [
      {
        name: '1'
      }
    ],
    nbgzzdList: [],
    sjmxList: []
  }
  formRef.value?.resetFields()
}
</script>
<style lang="scss" scoped>
.title {
  font-weight: 900;
  font-size: 16px;
  margin-bottom: 16px;
  margin-top: 16px;
  display: flex;
  align-items: center;
}
:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
  width: 80px;
}
</style>