import request from '@/config/axios'

// 问题定性库 VO
export interface QualitativeAnalysisVO {
	id ?: number // 主键，唯一标识
	/*分类ID;分类ID */
	typeId : number;
	/*分类名称;分类名称 */
	typeName : string;
	/*问题名称;问题名称 */
	quesName : string;
	/*问题定性;问题定性 */
	quesQualitation : string;
	/*问题描述;问题描述 */
	quesDesc : string;
	auditSugg : string;
	/*状态 1启用 2作废;状态 0草稿 1启用 2作废 */
	status : number;
	/*备注;备注 */
	remark : string;
	/*审计事项id;审计事项id */
	auditMattersId : number;
	/*审计事项名称;审计事项名称 */
	auditMattersName : string;
	/*内部规章制度子表信息 */
	internalRegulations : Record<string, unknown>[];
	/*审计模型信息 */
	auditModel : Record<string, unknown>[];
	/* */
	lawsAndRegulations : {}[];
}
export interface QualitativeAnalysisDetailVO {
	id ?: number // 主键，唯一标识
	/*分类ID;分类ID */
	typeId : number;
	/*分类名称;分类名称 */
	typeName : string;
	/*问题名称;问题名称 */
	quesName : string;
	/*问题定性;问题定性 */
	quesQualitation : string;
	/*问题描述;问题描述 */
	quesDesc : string;
	auditSugg : string;
	/*状态 1启用 2作废;状态 0草稿 1启用 2作废 */
	status : number;
	/*备注;备注 */
	remark : string;
	/*审计事项id;审计事项id */
	auditMattersId : number;
	/*审计事项名称;审计事项名称 */
	auditMattersName : string;
	/*内部规章制度子表信息 */
	internalRegulations : Record<string, unknown>[];
	/*审计模型信息 */
	auditModel : Record<string, unknown>[];
	/* */
	lawsAndRegulations : {}[];
}

// 问题定性库 API
export const QualitativeAnalysisApi = {
	// 查询问题定性库列表分页
	getQualitativeAnalysisList: async (params : any) => {
		return await request.get({ url: `/audit/tank-ques-qualitation/page`, params })
	},
	// 查询问题定性分类列表分页
	getQualitativeAnalysisListType: async (params : any) => {
		return await request.get({ url: `/audit/tank-trees-type/get?type=5`, params })
	},
	// 根据审计事项获得问题定性库分页
	getQualitativeAnalysisTypeList: async (params : any) => {
		return await request.get({ url: `/audit/tank-ques-qualitation/page-by-matters`, params })
	},

	// 获得所有问题分类
	getQualitativeAnalysisAll: async () => {
		// return await request.get({url: `/audit/tank-ques-qualitation/get-all-classification`})
		return await request.get({url: `/audit/tank-trees-type/get?type=5`})
	},

	// 修改事项状态
	changeQualitativeAnalysisList: async (params : any) => {
		return await request.post({ url: `/audit/tank-ques-qualitation/update-statue`, params })
	},

	// 添加问题定性库和审计事项关联关系
	addQualitativeAnalysis: async (data : QualitativeAnalysisVO) => {
		return await request.post({ url: `/audit/tank-ques-qualitation/add-ques-classification`, data })
	},

	// 根据事项ID获得风险点
	getRiskPoints: async () => {
		return await request.get({ url: `/audit/tank-ques-qualitation/get-risk-points  ` })
	},

	// 查询问题定性库详情
	getQualitativeAnalysis: async (id : number) => {
		return await request.get({ url: `/audit/tank-ques-qualitation/get?id=` + id })
	},

	// 新增问题定性库
	createQualitativeAnalysis: async (data : QualitativeAnalysisVO) => {
		return await request.post({ url: `/audit/tank-ques-qualitation/create`, data })
	},

	// 修改问题定性库
	updateQualitativeAnalysis: async (data : QualitativeAnalysisVO) => {
		return await request.put({ url: `/audit/tank-ques-qualitation/update`, data })
	},

	// 删除问题定性库
	deleteQualitativeAnalysis: async (id : number) => {
		return await request.delete({ url: `/audit/tank-ques-qualitation/delete?id=` + id })
	},

	// 导出问题定性库 - 分类 Excel
	exportQualitativeAnalysis: async (params : any) => {
		return await request.download({ url: `/audit/tank-ques-qualitation/export-excel`, params })
	},
	// 导出问题定性库 - 事项 Excel
	itemExportQualitativeAnalysis: async (params : any) => {
		return await request.download({ url: `/audit/tank-ques-qualitation/export-excel-byqustype`, params })
	},
	// 删除问题定性库关联事项
	deleteQualitativeAnalysisRelation: async (id : number) => {
		// return await request.delete({ url: `/audit/tank-ques-qualitation/delet-ques-classification?id=` + id })
		return await request.delete({ url: `/audit/tank-ques-qualitation/delete?id=` + id })
	},
}