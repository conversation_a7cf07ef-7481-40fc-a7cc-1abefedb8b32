import request from '@/config/axios'

// 整改接口人 VO
export interface AuditLiaisonVO {
	id ?: number // 主键，唯一标识
	orgCode : string // 组织机构编码
	orgName : string // 组织机构名称
	deptName : string // 部门名称
	interfacUserId : number // 接口人ID
	interfacUserName : string // 接口人名称
	linkTel : string // 联系方式
	orderNum : number //排序
	status : number //状态
}
export interface AuditLiaisonDetailVO {
	id : number // 主键，唯一标识
	orgCode : string // 组织机构编码
	orgName : string // 组织机构名称
	deptName : string // 部门名称
	interfacUserId : number // 接口人ID
	interfacUserName : string // 接口人名称
	linkTel : string // 联系方式
	orderNum : number //排序
	status : number //状态
	creatorName : string
	createTime : Date

}

// 整改接口人 API
export const AuditLiaisonApi = {
	// 查询整改接口人列表分页
	getAuditLiaisonList: async (params : any) => {
		return await request.get({ url: `/system/rectify-interface-person/page`, params })
	},

	// 获取该单位该部门下的所有用户
	getAuditLiaisonUserList: async (params : any) => {
		return await request.get({ url: `/system/rectify-interface-person/getUserList`, params })
	},

	// 查询整改接口人详情
	getAuditLiaison: async (id : number) => {
		return await request.get({ url: `/system/rectify-interface-person/get?id=` + id })
	},

	// 新增整改接口人
	createAuditLiaison: async (data : AuditLiaisonVO) => {
		return await request.post({ url: `/system/rectify-interface-person/create`, data })
	},
	newCreateAuditLiaison: async (data : any) => {
		return await request.post({ url: `/system/rectify-interface-person/create`, data })
	},

	// 修改整改接口人
	updateAuditLiaison: async (data : AuditLiaisonVO) => {
		return await request.put({ url: `/system/rectify-interface-person/update`, data })
	},

	// 删除整改接口人
	deleteAuditLiaison: async (id : number) => {
		return await request.delete({ url: `/system/rectify-interface-person/delete?id=` + id })
	},

	// 导出整改接口人 Excel
	exportAuditLiaison: async (params : any) => {
		return await request.download({ url: `/system/rectify-interface-person/export-excel`, params })
	}
}
