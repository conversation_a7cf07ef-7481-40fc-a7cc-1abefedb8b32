import request from '@/config/axios'

// 项目资料清单 VO
export interface ProjectDataListVO {
	id?: number // 主键，唯一标识
	auditYear: string // 项目年度
	projectNo: string // 项目编码
	projectName: string // 项目名称
	auditType: string // 审计类别
	auditTypeDesc: string // 审计类别显示文本
	auditSencType: string // 审计二级类型
	auditSencTypeDesc: string // 审计二级类型显示文本
	overseasFlag: number // 是否境外 1是
	auditObject: number //审计对象
	orgType: string //组织方式
	planStart: string //时间计划-开始
	planEnd: string //时间计划-结束
}
export interface ProjectDataListDetailVO {
	id: number // 主键，唯一标识
	auditYear: string // 项目年度
	projectNo: string // 项目编码
	projectName: string // 项目名称
	auditType: string // 审计类别
	auditTypeDesc: string // 审计类别显示文本
	auditSencType: string // 审计二级类型
	auditSencTypeDesc: string // 审计二级类型显示文本
	overseasFlag: number // 是否境外 1是
	auditObject: number //审计对象
	orgType: string //组织方式
	planStart: string //时间计划-开始
	planEnd: string //时间计划-结束

}

// 项目资料清单 API
export const ProjectDataListApi = {
	// 查询项目资料清单列表分页
	// getProjectDataListList: async (params : any) => {
	// 	return await request.post({ url: `/manage/proj-plan-project/page`, params })
	// },

	getProjectDataListList: async (params: any) => {
		return await request.post({ url: `/manage/proj-plan-project/list/by-program-code`, params })
	},

	// 获得项目立项项目清单(项目基础信息表)详情
	getProjectInitiationDetail: async (id: number, code: any) => {
		return await request.get({ url: `/manage/proj-plan-project/detail?id=` + id + `&progCode=` + code })
	},
	// 审计事项信息列表查询--审计取证单专用
	getAuditGroupList: async (id: number) => {
		// return await request.get({ url: `/manage/proj-audit-matter/link-list?projectId=` + id })
		return await request.get({ url: `/manage/proj-audit-matter/link-list-by-userid?projectId=` + id })
	},

	// 项目资料清单填报获取详情接口
	getProjectInitiationMaterialDetail: async (id: number) => {
		return await request.get({ url: `/manage/proj-plan-project/material/detail?id=` + id })
	},
	// 补充审计资料获取接口
	getProjectInitiationMaterialDetail2: async (id: number) => {
		return await request.get({ url: `/manage/proj-plan-project/material/paddingDetail?id=` + id })
	},
	// 补充审计资料审核页获取接口
	getProjectInitiationMaterialDetail3: async (id: number,shouldFilter:any) => {
		return await request.get({ url: `/manage/proj-plan-project/material/paddingFlowDetail?id=${id}&shouldFilter=${shouldFilter}` })
	},

	// 查询项目资料清单详情
	getProjectDataList: async (id: number) => {
		return await request.get({ url: `/manage/proj-plan-project/get?id=` + id })
	},

	// 新增项目资料清单
	createProjectDataList: async (data: ProjectDataListVO) => {
		return await request.post({ url: `/manage/proj-plan-project/create`, data })
	},

	// 修改项目资料清单
	updateProjectDataList: async (data: ProjectDataListVO) => {
		return await request.put({ url: `/manage/proj-plan-project/update`, data })
	},

	// 删除项目资料清单
	deleteProjectDataList: async (id: number) => {
		return await request.delete({ url: `/manage/proj-plan-project/delete?id=` + id })
	},

	// 导出项目资料清单 Excel
	exportProjectDataList: async (params: any) => {
		return await request.download({ url: `/manage/proj-plan-project/export-excel`, params })
	},

	// 创建 项目作业-项目资料清单 信息 录入保存
	enterProjectData: async (data: ProjectDataListVO) => {
		return await request.post({ url: `/manage/proj-resource/create`, data })
	},

	// 导入Excel
	importProjectDataList: async (params: any) => {
		return await request.post({ url: `/manage/proj-resource-detail/import-excel`, params })
	},

	// 新录入接口
	registrationProjectData: async (data: ProjectDataListVO) => {
		return await request.post({ url: `/manage/proj-resource/registration`, data })
	},
	// 新录入接口2
	registrationProjectSupplementalData: async (data: ProjectDataListVO) => {
		return await request.post({ url: `/manage/proj-resource/supplemental`, data })
	},
	// 生成征求意见书
	createOPinionFile: async (projectId: string) => {
		return await request.get({ url: `/manage/audit/report/create-opinion-file?projectId=` + projectId })
	},
	// 检查底稿
	getCheckAuditWork: async (projId: string, matterId: string) => {
		return await request.get({ url: `/manage/audit-work-manuscript/checkAuditWork?projId=` + projId + "&matterId=" + matterId })
	},
	// 生成底稿
	createAuditWorkFile: async (projId: string, matterId: string) => {
		return await request.get({ url: `/manage/audit-work-manuscript/createAuditWorkFile?projId=` + projId + "&matterId=" + matterId })
	},

}
