import request from '@/config/axios'

// 审计文书 VO
export interface WritlibraryVO {
	id ?: number // 主键，唯一标识
	/*文书名称;文书名称 */
	docName : string;
	/*文书编码;文书编码 */
	docNo : string;
	/*功能描述;文书描述 */
	docDesc : string;
	/*文书分类ID;文书分类ID */
	docTypeId : number;
	/*文书分类名称;文书分类名称 */
	docTypeName : string;
	/*状态 1启用 2停用;状态 0草稿 1启用 2停用 */
	status : number;
	/*所属公司ID;所属公司ID */
	companyId : number;
	/*所属公司名称;所属公司名称 */
	companyName : string;
	/*所属模块id */
	ofModuleId : number;
	/*所属模块名称 */
	ofModuleName : string;
}
export interface WritlibraryDetailVO {
	id ?: number // 主键，唯一标识
	/*文书名称;文书名称 */
	docName : string;
	/*文书编码;文书编码 */
	docNo : string;
	/*功能描述;文书描述 */
	docDesc : string;
	/*文书分类ID;文书分类ID */
	docTypeId : number;
	/*文书分类名称;文书分类名称 */
	docTypeName : string;
	/*状态 1启用 2停用;状态 0草稿 1启用 2停用 */
	status : number;
	/*所属公司ID;所属公司ID */
	companyId : number;
	/*所属公司名称;所属公司名称 */
	companyName : string;
	/*所属模块id */
	ofModuleId : number;
	/*所属模块名称 */
	ofModuleName : string;
	tankDocInfoFileReqVOList : Array;
}

// 审计文书 API
export const WritlibraryApi = {
	// 查询审计文书列表分页
	getWritlibraryList: async (params : any) => {
		return await request.get({ url: `/audit/tank-doc-info/page`, params })
	},
	getWritlibraryAll: async () => {
		return await request.get({ url: `/audit/tank-doc-info/getAll` })
	},

	// 切换审计文书信息的启用停用
	changeWritlibrary: async (id : number) => {
		return await request.get({ url: `/audit/tank-doc-info/change?id=` + id })
	},

	// 查询审计文书详情
	getWritlibrary: async (id : number) => {
		return await request.get({ url: `/audit/tank-doc-info/get?id=` + id })
	},

	// 新增审计文书
	createWritlibrary: async (data : WritlibraryVO) => {
		return await request.post({ url: `/audit/tank-doc-info/create`, data })
	},

	// 修改审计文书
	updateWritlibrary: async (data : WritlibraryVO) => {
		return await request.put({ url: `/audit/tank-doc-info/update`, data })
	},

	// 删除审计文书
	deleteWritlibrary: async (id : number) => {
		return await request.delete({ url: `/audit/tank-doc-info/delete?id=` + id })
	},

	// 导出审计文书 Excel
	exportWritlibrary: async (params : any) => {
		return await request.download({ url: `/audit/tank-doc-info/export-excel`, params })
	}
}