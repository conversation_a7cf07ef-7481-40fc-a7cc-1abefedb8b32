import request from '@/config/axios'
// 审计资料清单 API
// 案例库 VO
export interface searchListVO {
    pageNo: number
    pageSize: number
    progName?: number
    auditType?: string
    mattersName?: string
    templateFlag?: number
    materialDesc?: string
}

interface matterVo {
    matterName: string
    mattersName: string
    mattersDesc: string
    stepMethod: string
    id: string
  }
export interface AddVO {
    id?: number
    progName: string
    auditType: number
    status: number
    progInfo: number
    progKeynote: number
    scheduling: string
    auditAsk: number
    tankMattersInfoDOS: matterVo[]
}

export interface fileListVo {
    id:number,	
    version: string,		
    fileName: string,
    creatorName: string,
    createTime: string,
    fileState: string,
    fileUrl: string,
    fileId: number,
}

//  审计方案库 API
export const TankProgrammesApi = {
    // 审计资料清单列表查询
    getTankProgrammesList: async (params: searchListVO) => {
        return await request.get({ url: `/audit/tank-programmes/page`, params })
    },
    createTankProgrammes: async (data: AddVO) => {
        return await request.post({ url: `/audit/tank-programmes/create`, data })
    },
    updateTankProgrammes: async (data: AddVO) => {
        return await request.put({ url: `/audit/tank-programmes/update`, data })
    },
    changeTankProgrammes: async (id: number) => {
        return await request.get({ url: `/audit/tank-programmes/get-change?id=${id}`})
    },
    deleteTankProgrammes: async (id: number) => {
        return await request.delete({ url: `/audit/tank-programmes/delete?id=${id}`})
    },
    exportExcel: async (params: searchListVO) => {
        return await request.download({ url: `/audit/tank-programmes/export-excel`, params })
    },
    copyTankProgramme: async (id: number) => {
        return await request.post({ url: `/audit/tank-programmes/copy-tank-programme?id=${id}` })
    },
    // 所有的审计事项
    getTankTreeType: async () => {
        return await request.get({ url: `/audit/tank-trees-type/get`})
    },
    // 根据审计类型获取事项
    getMatterByAuditType: async (id: number) => {
        return await request.get({ url: `/audit/tank-matters-info/getmatters-by-audit-type?auditType=${id}`})
    },
    // 审计方案详情
    getDetail: async (id: number) => {
        return await request.get({ url: `/audit/tank-programmes/get?id=${id}`})
    },
    // 预览审计方案附件
    getFileData: async (id: number) => {
        return await request.get({ url: `/audit/tank-programmes/get-file?id=${id}`})
    },
}