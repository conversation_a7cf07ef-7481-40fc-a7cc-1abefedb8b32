import request from '@/config/axios'

// 审计类型 VO
export interface AuditProcedureVO {
	id ?: number // 主键，唯一标识
	auditProcedureName : string // 审计类型名称
	projectPhase : number // 是否涉及外审单位
	sort : number //排序
	status : number //状态
}
export interface AuditProcedureDetailVO {
	id : number // 主键，唯一标识
	auditProcedureName : string // 审计类型名称
	projectPhase : number // 是否涉及外审单位
	sort : number //排序
	status : number //状态
	adder : string
	createTime : Date
}
// 配置列表 
export interface ConfiguraVO {
	id ?: number // 主键，唯一标识
	auditProgId : number // 审计程序ID,
	auditProgName : string // 审计程序名称
	ext1 : array //存附件ids
	attTypeId : number // 附件类型ID,
	attTypeName : string // 附件类型名称
}

// 审计类型 API
export const AuditProcedureApi = {
	// 查询审计类型列表分页
	getAuditProcedureList: async (params : any) => {
		return await request.get({ url: `/system/audit-procedure/page`, params })
	},
	getAuditProcedureAll: async () => {
		return await request.get({ url: `/system/audit-procedure/getAll` })
	},

	// 查询审计类型详情
	getAuditProcedure: async (id : number) => {
		return await request.get({ url: `/system/audit-procedure/get?id=` + id })
	},

	// 新增审计类型
	createAuditProcedure: async (data : AuditProcedureVO) => {
		return await request.post({ url: `/system/audit-procedure/create`, data })
	},

	// 修改审计类型
	updateAuditProcedure: async (data : AuditProcedureVO) => {
		return await request.put({ url: `/system/audit-procedure/update`, data })
	},

	// 删除审计类型
	deleteAuditProcedure: async (id : number) => {
		return await request.delete({ url: `/system/audit-procedure/delete?id=` + id })
	},

	// 导出审计类型 Excel
	exportAuditProcedure: async (params : any) => {
		return await request.download({ url: `/system/audit-procedure/export-excel`, params })
	},

	// 查询审计类型列表分页
	getConfiguraList: async (params : any) => {
		return await request.get({ url: `/system/config-prog-atta/list`, params })
	},

	// 新增审计类型
	createConfigura: async (data : ConfiguraVO) => {
		return await request.post({ url: `/system/config-prog-atta/create`, data })
	},
	// 获取项目阶段
	getAllConfigStage: async () => {
		return await request.get({ url: `/system/config-stage/get-all-config-stage` })
	},
}
