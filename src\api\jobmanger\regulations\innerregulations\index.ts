/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-21 13:19:33
 * @Description: 内部规章制度Api
 */
import request from '@/config/axios'

// 内部规章制度 VO
export interface RulesRegulationsVO {
    id?: number // 主键，唯一标识

}
export interface RulesRegulationsDetailVO {
    id: number // 主键，唯一标识

}



// 内部规章制度 API
export const RulesRegulationsApi = {
    // 查询内部规章制度列表分页
    getRulesRegulationsList: async (params: any) => {
        return await request.get({ url: `/audit/tank-internal-rule/page`, params })
    },

    // 查询内部规章制度详情
    getRulesRegulations: async (id: number) => {
        return await request.get({ url: `/audit/tank-internal-rule/get?id=` + id })
    },

    // 新增内部规章制度
    createRulesRegulations: async (data: any) => {
        return await request.post({ url: `/audit/tank-internal-rule/create`, data })
    },

    // 修改内部规章制度
    updateRulesRegulations: async (data: any) => {
        return await request.put({ url: `/audit/tank-internal-rule/update`, data })
    },

    // 删除内部规章制度
    deleteRulesRegulations: async (id: number) => {
        return await request.delete({ url: `/audit/tank-internal-rule/delete?id=` + id })
    },
  
    // 导出内部规章制度 Excel
    exportRulesRegulations: async (params: any) => {
        return await request.download({ url: `/audit/tank-internal-rule/export-excel`, params })
    },

    // 获取内部规章制度分类
    getInnerregulationsList: async (params: any) => {
        return await request.get({ url: `/audit/tank-trees-type/get?type=1`, params })
    },
}
