import request from '@/config/axios'

// 获取主题目录树列表
export function getThemeTreeList() {
  return request.post({
    url: '/model/themeCatalog/themeCatalogTreeData'
  })
}


// 查询单条数据
export function getTopicInfoById(id){
  return request.post({
    url: '/model/themeCatalog/themeCatalogNodeData/' + id
  })
}


// 获取目录下拉选项
export function getCatalog(){
  return request.post({
    url:'/model/themeCatalog/themeMainCatalogData'
  })
}

// 保存主题库
export function saveThemeCatalog(data) {
  return request.post({
    url: '/model/themeCatalog/saveThemeCatalogData',
    data: data
  })
}

// 删除主题
export function deleteThemeCatalog(id) {
  return request.post({
    url: '/model/themeCatalog/deleteThemeCatalogData/' + id
  })
}


// 右侧分页列表数据
export function getPageList(data){
  return request.post({
    url: '/model/themeInfo/modelThemeInfoPageData',
    data: data
  })
}

// 右侧数据保存
export function saveThemeInfo(data) {
  return request.post({
    url: '/model/themeInfo/saveThemeInfoData',
    data: data
  })
}

// 获取主题信息
export function getThemeInfo(id) {
  return request.post({
    url: '/model/themeInfo/themeInfoData/' + id,
  })
}

// 删除主题信息
export function deleteThemeInfo(id) {
  return request.post({
    url: '/model/themeInfo/deleteThemeInfoData/' + id
  })
}

// 获取待关联模型列表
export function getModelListPage(id) {
  return request.post({
    url: '/model/modelInfo/allFixModels/'+ id,
  })
}

//保存主题关联模型数据
export function saveThemeModel(data) {
  return request.post({
    url: '/model/themeInfo/saveThemeRelateModelData',
    data: data
  })
}

//执行主题关联的模型
export function executeThemeModel(data) {
  return request.post({
    url: '/model/themeInfo/executeThemeRelatedModels',
    data: data
  })
}

// 获取主题执行记录分页数据
export function getThemeExecuteRecordPage(data) {
  return request.post({
    url: '/model/themeInfo/themeExecuteRecordPageData',
    data: data
  })
}

