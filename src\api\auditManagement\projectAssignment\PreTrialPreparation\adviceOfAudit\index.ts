import request from '@/config/axios'

// 审计通知书 VO
export interface AdviceOfAuditVO {
	id ?: number // 主键，唯一标识
	infoCode : string
	infoName : string
	infoType : number
	projectNo : string
	projectName : string
	creatorName : string
	createTime : string
	flowStatus : number
	auditObject : string
}
export interface AdviceOfAuditDetailVO {
	id : number // 主键，唯一标识
	infoCode : string
	infoName : string
	infoType : number
	projectNo : string
	projectName : string
	creatorName : string
	createTime : string
	flowStatus : number
	auditObject : string
}

// 审计通知书 API
export const AdviceOfAuditApi = {
	// 查询审计通知书-公告分页
	getAdviceOfAuditAnnouncementList: async (params : any) => {
		return await request.post({ url: `/manage/proj-audit-notice/page-list`, params })
	},

	// 查询审计通知书列表分页
	getAdviceOfAuditList: async (params : any) => {
		return await request.post({ url: `/manage/proj-audit-notice/page`, params })
	},

	// 审计通知书填报获取详情接口
	getProjectInitiationMaterialDetail: async (id : number) => {
		return await request.get({ url: `/manage/proj-audit-notice/material/detail?id=` + id })
	},

	// 查询审计通知书详情
	getAdviceOfAudit: async (id : number) => {
		return await request.get({ url: `/manage/proj-audit-notice/get?id=` + id })
	},

	// 新增审计通知书
	createAdviceOfAudit: async (data : AdviceOfAuditVO) => {
		return await request.post({ url: `/manage/proj-audit-notice/create`, data })
	},

	// 修改审计通知书
	updateAdviceOfAudit: async (data : AdviceOfAuditVO) => {
		return await request.put({ url: `/manage/proj-audit-notice/update`, data })
	},

	// 删除审计通知书
	deleteAdviceOfAudit: async (id : number) => {
		return await request.delete({ url: `/manage/proj-audit-notice/delete?id=` + id })
	},

	// 导出审计通知书 Excel
	exportAdviceOfAudit: async (params : any) => {
		return await request.download({ url: `/manage/proj-audit-notice/export-excel`, params })
	},

	// // 创建 项目作业-审计通知书 信息 录入保存
	// enterProjectData: async (data : AdviceOfAuditVO) => {
	// 	return await request.post({ url: `/manage/proj-resource/create`, data })
	// },
	// 提交审计通知书
	submitAdviceOfAudit: async (data : AdviceOfAuditVO) => {
		return await request.post({ url: `/manage/proj-audit-notice/submit`, data })
	},
}