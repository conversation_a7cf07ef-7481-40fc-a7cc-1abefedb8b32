
import request from '@/config/axios'


// 领导首页 API
export const HomeApi = {
    // 获取通知公告列表
    getNoticePageList: async(params: any) => {
        return await request.get({ url: `/system/notice/page`, params })
    },
    // 工程审计-控制价审计
    getEngineerControlStep: async() => {
        return await request.get({ url: `/manage/engineering/control-step` })
    },
    // 工程审计-基建工程结算审计
    getEngineerSettleStep: async(type: string) => {
        return await request.get({ url: `manage/engineering/settle-step?type=${type}` })
    },

    // 外部审计列表
    getNotCorrectedListOut: async(params: any) => {
        return await request.get({ url: `/system/notice/page`, params })
    },

    // 督办详情
    getNotCorrectedInquire: async(data: any) => {
        return await request.get({ url: `/manage/notice-supervision/indexDetail?id=` + data.id})
    },
    // 审计项目进展
    getStateValue: async(params: any) => {
        return await request.get({ url: `/manage/graph/project/state-value`,params})
    },
    // 审计项目进展
    getCommonMenuSetting: async(id:any) => {
        return await request.get({ url: `/system/common-menu-setting/get?id=` + id})
    },

   
    
}
