import request from '@/config/axios'

// 整改通知 VO
export interface RectificaNoticeVO {
	id ?: number // 主键，唯一标识	
	/*项目名称 */
	projectName : string;
	/*委托书文号 */
	docNum : string;
	/*项目编码 */
	projectNo : string;
	/*审计类别显示文本 */
	auditTypeDesc : string;
	/*组织方式 */
	orgType : string;
	/*下发问题总数 */
	quesCount : string;
	// 已反馈问题数
	correctionCount: string;
	/*审计对象 */
	auditObject : string;
	/*项目年度 */
	auditYear : string;
	/*公司领导 */
	companyLeader : string;
	/*曾任职务 */
	previousWork : string;
	/*审计报告名称 */
	fileName : string;
	/*通知书名称 */
	notticeName : string;
	/*项目类型id */
	auditType : string;
	/*审计对象id */
	auditCompanyId : string;
	/*状态 */
	status : number;
	/*最晚反馈时间 */
	feedbackTime : Record<string, unknown>;
	/* */
	issued : number;
}
export interface RectificaNoticeDetailVO {
	id : number // 主键，唯一标识
	/* */
	projectName : string;
	/*审计类别 */
	auditType : string;
	/*审计类别显示文本 */
	auditTypeDesc : string;
	/*项目年度 */
	auditYear : string;
	/*曾任职务 */
	previouWork : string;
	/*公司领导 */
	companyLeader : string;
	/*要求反馈时间 */
	feedbackTime : Record<string, unknown>;
	/*整改联络人 */
	rectifyContact : string;
	/*发文编号 */
	documentNumber : string;
	/*项目ID */
	projectId : number;
	/*流程id */
	flowId : string;
	/*审计对象 */
	auditObject : string;
	/*流程状态 */
	flowStatus : number;
	/*问题资料清单ID */
	questionMaterialsId : {
		/*id */
		id : number;
		/*问题编码 */
		questionCode : string;
		/*问题类型名称 */
		quesTypeName : string;
		/*问题标题 */
		questionName : string;
		/*问题描述 */
		quesDigest : string;
		/*审计建议 */
		auditSugg : string;
		/*发现人名称 */
		discoverUserName : string;
	}[];
	/* */
	rectificationMaterialsVO : {
		/*整改资料清单ID */
		id : number;
		/*资料名称 */
		profileName : string;
		/*编制人 */
		complier : string;
		/*编制时间 */
		complierTime : Record<string, unknown>;
		/*联系方式 */
		contact : string;
	};
}

// 整改通知 API
export const RectificaNoticeApi = {
	// 查询整改通知列表分页
	getRectificaNoticeList: async (params : any) => {
		return await request.get({ url: `/manage/notice-rectification/get-list`, params })
	},

	// 查询整改方案列表分页
	getRectificaPlanList: async (params : any) => {
		return await request.get({ url: `/manage/notice-rectification/get-notice-list`, params })
	},

	// 查询整改通知详情
	getRectificaNotice: async (id : number) => {
		return await request.get({ url: `/manage/notice-rectification/get-notice-rectification?id=` + id })
	},

	// 发起整改通知
	createRectificaNotice: async (data : RectificaNoticeVO) => {
		return await request.post({ url: `/manage/notice-rectification/save-notice-rectification`, data })
	},

	// 分发整改通知书问题
	distributeRectificaNotice: async (data : RectificaNoticeVO) => {
		return await request.post({ url: `/manage/notice-rectification/distribute`, data })
	},

	// 查询问题分发详情
	getReformingPlan: async (id : number) => {
		return await request.get({ url: `/manage/notice-rectification/get-ques-byid?quesId=` + id })
	},

	//生成整改通知书
	getReformingBook: async (id : number) => {
		return await request.post({ url: `/manage/notice-rectification/generate-documentation?projId=` + id })
	},

	// 查询审计报告正式稿
	getRreportListNotice: async (params : any) => {
		return await request.get({ url: `/manage/project-assign-atta/getreportlist/by-codelist`, params})
	},
}