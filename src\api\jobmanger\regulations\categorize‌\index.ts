import request from '@/config/axios'

// 法律法规分类 VO
export interface CategorizeVO {
	id ?: number // 主键，唯一标识
	/*创建人;创建人 */
	createdBy : string;
	/*创建时间;创建时间 */
	createdTime : Record<string, unknown>;
	/*更新人;更新人 */
	updatedBy : string;
	/*更新时间;更新时间 */
	updatedTime : Record<string, unknown>;
	/*分类名称;分类名称 */
	typeName : string;
	/*分类编码;分类编码 */
	typeCode : string;
	/*分类描述-del;分类描述 */
	typeDesc : string;
	/*所属上级ID;所属上级ID */
	parentId : number;
	/*所属上级名称;所属上级名称 */
	parentName : string;
	/*级别;0为默认，1为0的从属，以此类推 */
	treeLevel : number;
	/*树状所属类型 */
	treeOfType : string;
}
export interface CategorizeDetailVO {
	id ?: number // 主键，唯一标识
	/*创建人;创建人 */
	createdBy : string;
	/*创建时间;创建时间 */
	createdTime : Record<string, unknown>;
	/*更新人;更新人 */
	updatedBy : string;
	/*更新时间;更新时间 */
	updatedTime : Record<string, unknown>;
	/*分类名称;分类名称 */
	typeName : string;
	/*分类编码;分类编码 */
	typeCode : string;
	/*分类描述-del;分类描述 */
	typeDesc : string;
	/*所属上级ID;所属上级ID */
	parentId : number;
	/*所属上级名称;所属上级名称 */
	parentName : string;
	/*级别;0为默认，1为0的从属，以此类推 */
	treeLevel : number;
	/*树状所属类型 */
	treeOfType : string;
}

// 法律法规分类 API
export const CategorizeApi = {
	// 查询法律法规分类列表分页
	getCategorizeList: async (params : any) => {
		return await request.get({ url: `/audit/tank-trees-type/page`, params })
	},
	// 全部法律法规分类
	getCategorizesAll: async (type : number) => {
		return await request.get({ url: `/audit/tank-trees-type/get?type=` + type })
	},

	// 查询事项库详情
	getCategorize: async (id : number) => {
		return await request.get({ url: `/audit/tank-trees-type/get-tree?id=` + id })
	},

	// 新增法律法规分类
	createCategorize: async (data : CategorizeVO) => {
		return await request.post({ url: `/audit/tank-trees-type/create`, data })
	},

	// 修改法律法规分类
	updateCategorize: async (data : CategorizeVO) => {
		return await request.put({ url: `/audit/tank-trees-type/update`, data })
	},

	// 删除法律法规分类
	deleteCategorize: async (id : number) => {
		return await request.delete({ url: `/audit/tank-trees-type/delete?id=` + id })
	},

	// 导出法律法规分类 Excel
	exportCategorize: async (params : any) => {
		return await request.download({ url: `/audit/tank-trees-type/export-excel`, params })
	}
}