import request from '@/config/axios'

// 审计信息全景 
export interface TopNumVO {
	/*项目数 */
    projectNum: number;
    /*分子公司数 */
    companyNum: number;
    /*审计问题数 */
    questionNum: number;
    /*已完成整改问题数 */
    rectificationFinishNum: number;
    /*底稿数 */
    baseBookNum: number;
    /*审计报告数 */
    auditBookNum: number;
    /*审计档案数 */
    projectFinishNum: number;
    /*审计案例数 */
    caseNum: number;
    /*领域数量 */
    domainNum: number;
    /*数据模型数 */
    modelNum: number;
}

// 工程审计-结算 VO
export interface EngineerSettleNumVO {
	 /*项目总数 */
	 projectNum: number;
	 /*基建工程数量 */
	 baseBuildEngineerNum: number;
	 /*技改工程数量 */
	 baseBetterEngineerNum: number;
	 /*报审金额 */
	 reportMoney: number;
	 /*审定金额 */
	 auditMoney: number;
	 /*审减金额 */
	 auditMinusMoney: number;
	 /*平均审减率 */
	 avgAuditMinusRate: number;
	 /*审计费用 */
	 auditFee: number;
	 /*审计完成率 */
	 auditFinishRate: number;
}

// 工程审计-控制 VO
export interface EngineerControlNumVO {
	 /*项目总数 */
	 projectNum: number;
	 /*委托审计单位数 */
	 auditOrgNum: number;
	 /*报审金额 */
	 reportMoney: number;
	 /*审定金额 */
	 auditMoney: number;
	 /*审减金额 */
	 auditMinusMoney: number;
	 /*平均审减率 */
	 avgAuditMinusRate: number;
	 /*审计费用 */
	 auditFee: number;
	 /*审计完成率 */
	 auditFinishRate: number;
}

// 审计数据总览 VO
export interface BottomNumVO {
	/*项目数 */
    projectNum: number;
    /*结项总数 */
    projectFinishNum: number;
    /*问题台账问题数 */
    questionNum: number;
    /*疑点数量 */
    doubtfulNum: number;
    /*底稿数 */
    baseBookNum: number;
    /*审计报告数 */
    auditBookNum: number;
    /*审计方案数 */
    schemeNum: number;
    /*优秀案例数 */
    betterCaseNum: number;
    /*整改台账问题数 */
    rectificationQuestionNum: number;
    /*销号问题数 */
    finishQuestionNum: number;
    /*持续监督问题数 */
    constitutionNumb: number;
    /*本年计划项目 */
    planProjectChildNum: number;
    /*本年启动项目 */
    planProjectParentNum: number;
    /*企业画像 */
    unitNum: number;
    /*数据模型数 */
    modelNum: number;
    /*领域数量 */
    domainNum: number;
}

// 招标控制价 API
export const DemoviewApi = {
	// 获得审计信息全景
	getTopNum: async (params : any) => {
		return await request.get({ url: `/manage/graph/top-num`, params })
	},
	// 获得工程审计-结算
	getEngineerSettleNum: async (params : any) => {
		return await request.get({ url: `/manage/graph/engineer-settle-num`, params })
	},
	// 获得工程审计-控制
	getEngineerControlNum: async (params : any) => {
		return await request.get({ url: `/manage/graph/engineer-control-num`, params })
	},
	// 获得审计数据总览
	getBottomNum: async (params : any) => {
		return await request.get({ url: `/manage/graph/bottom-num`, params })
	},
}