import request from '@/config/axios'

// 整改方案 VO
export interface ReformingPlanVO {
	/*ID;ID */
	id : number;
	/*问题整改方案ID */
	rectificQuesId : number;
	/*整改措施 */
	rectificStep : string;
	/*计划整改完成时间 */
	planEndTime : Record<string, unknown>;
	/*阶段顺序 */
	stepNum : number;
}
export interface ReformingPlanDetailVO {
	/*ID;ID */
	id : number;
	/*问题整改方案ID */
	rectificQuesId : number;
	/*整改措施 */
	rectificStep : string;
	/*计划整改完成时间 */
	planEndTime : Record<string, unknown>;
	/*阶段顺序 */
	stepNum : number;
}

// 整改方案 API
export const ReformingPlanApi = {
	// // 查询整改方案列表分页
	// getReformingPlanList: async (params : any) => {
	// 	return await request.get({ url: `/manage/notice-rectification/get-list`, params })
	// },

	// 查询整改方案汇总详情
	getReformingPlan: async (id : number) => {
		return await request.get({ url: `/manage/notice-rectification/detail?id=` + id })
	},

	// 批量创建 审计整改-审计方案 - 录入问题整改方案-措施-整改详情
	createReformingPlan: async (data : ReformingPlanVO) => {
		return await request.post({ url: `/manage/rectific-ques-detail/batch-create`, data })
	},

	// // 分发整改方案书问题
	// distributeReformingPlan: async (data : ReformingPlanVO) => {
	// 	return await request.post({ url: `/manage/notice-rectification/distribute`, data })
	// },

	// 整改方案生成审批
	flowReformingPlan: async (data : ReformingPlanVO) => {
		return await request.post({ url: `/manage/notice-rectification/sum-start-flow`, data })
	},

	// 查询整改方案汇总详情
	getQuestionReformingPlan: async (id : number) => {
		return await request.get({ url: `/manage/notice-rectification/question/get?id=` + id })
	},

	// 生成整改方案
	createFileReformingPlan: async (id : number) => {
		return await request.get({ url: `/manage/notice-rectification/create-file?noticeId=` + id })
	},
}