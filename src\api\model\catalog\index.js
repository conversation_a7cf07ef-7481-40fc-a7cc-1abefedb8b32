import request from '@/config/axios'

// 保存模型目录节点;
export function saveCatalog(data) {
  return request.post({
    url: '/model/modelCatalogInfo/saveModelCatalogInfoData',
    data: data
  })
}


// 删除
export function deleteCatalogInfo(id) {
  return request.post({
    url: '/model/modelCatalogInfo/deleteModelCatalogInfoData/' + id,
  })
}

// 获de
export function getModelCatalogInfo(data) {
  return request.post({
    url: '/model/modelCatalogInfo/modelCatalogInfoData',
    data: data
  })
}

// 获取模型目录树
export function getModelCatalogTree() {
  return request.post({
    url: '/model/modelCatalogInfo/modelCatalogInfoTreeData',
  })
}

export function getMainTree(){
  return request.post({
    url: '/model/modelCatalogInfo/modelMainCatalogItems'
  })
}


export function getChildOptions(id){
  return request.post({
    url: '/model/modelCatalogInfo/modelSubCatalogItems/' + id
  })
}


