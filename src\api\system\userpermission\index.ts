import request from '@/config/axios'

// 用户数据权限 VO
export interface UserPermissionVO {
  id: number // 主键ID
  userId: number // 用户唯一ID
  holdDeptId: number // 持有的数据权限部门ID
}
export interface RightListDo {
  pageNo: number
  pageSize: number
  userId?: number
  id?: number
  name?: string
  remark?: number
  status?: number
}
interface AddRoleVo {
  userId: number
  roleId: number
}
interface AddDataVo {
  userId: number
  deptId: number
}
interface DelDataVo {
  id: number
}

interface UserByRoleVo {
  pageNo: number
  pageSize: number
  username?: string
  nickname?: string
  mobile?: number
  roleId: number
  status?: number
}
// 用户数据权限 API
export const UserPermissionApi = {
  // 查询用户数据权限分页
  getUserPermissionPage: async (params: any) => {
    return await request.get({ url: `/system/user-permission/page`, params })
  },

  // 查询用户数据权限详情
  getUserPermission: async (id: number) => {
    return await request.get({ url: `/system/user-permission/get?id=` + id })
  },
  // 根据用户id查询所拥有的数据权限部门
  getDeptPermissionByUserId: async (params: RightListDo) => {
    return await request.get({ url: `/system/user-permission/deptPermissionByUserId`, params })
  },
  // 根据用户id查询所拥有的角色信息
  getRolePermissionByUserId: async (params: RightListDo) => {
    return await request.get({ url: `/system/user-permission/rolePermissionByUserId`, params })
  },
  // 按角色维度 已授权用户列表查询
  getUserByRole: async (params: UserByRoleVo) => {
    return await request.get({ url: `/system/role/getUserByRole`, params })
  },
  // 按角色维度 已授权用户-新增用户列表查询
  getUserNoPermsion: async (params: UserByRoleVo) => {
    return await request.get({ url: `/system/role/getUserNoPermsion`, params })
  },
  // 获取用户已经分配的组织的id集合 用于树形回显
  getUserPermissionList: async (id: number) => {
    return await request.get({ url: `/system/user-permission/getUserPermissionList?id=` + id })
  },
  // 给角色分配用户
  setUserPermsionByrole: async (data: AddRoleVo[]) => {
    return await request.post({ url: `/system/role/setUserPermsionByrole`, data })
  },
  // // 按角色维度 删除用户
  delUserPermsionByrole: async (data: AddRoleVo[]) => {
    return await request.post({ url: `/system/role/delUserPermsionByrole`, data })
  },
  //新增分配角色列表查询
  getNoRolePermissionByUserId: async (params: RightListDo) => {
    return await request.get({ url: `/system/user-permission/noPermissionByUserId`, params })
  },
  // 按角色维度列表查询
  getUserPageByRolePermission: async (params: any) => {
    return await request.get({ url: `/system/user-permission/userPageByRolePermission`, params })
  },
  // 按组织维度列表查询
  getUserPageByDeptPermission: async (params: any) => {
    return await request.get({ url: `/system/user-permission/userPageByDeptPermission`, params })
  },
  // 新增用户数据权限
  createUserPermission: async (data: UserPermissionVO) => {
    return await request.post({ url: `/system/user-permission/create`, data })
  },
  setPermissionByRole: async (data: AddRoleVo[]) => {
    return await request.post({ url: `/system/user-permission/setPermissionByRole`, data })
  },
  setPermissionByDept: async (data: AddDataVo[]) => {
    return await request.post({ url: `/system/user-permission/setPermissionByDept`, data })
  },

  deletePermissionByRole: async (data: AddRoleVo[]) => {
    return await request.post({ url: `/system/user-permission/deletePermissionByRole`, data })
  },
  deletUserPermissionData: async (data: DelDataVo[]) => {
    return await request.post({ url: `/system/user-permission/deletUserPermissionData`, data })
  },
  // 修改用户数据权限
  updateUserPermission: async (data: UserPermissionVO) => {
    return await request.put({ url: `/system/user-permission/update`, data })
  },

  // 删除用户数据权限
  deleteUserPermission: async (id: number) => {
    return await request.delete({ url: `/system/user-permission/delete?id=` + id })
  },

  // 导出用户数据权限 Excel
  exportUserPermission: async (params) => {
    return await request.download({ url: `/system/user-permission/export-excel`, params })
  }
}
