import request from '@/config/axios'
import { getElement } from 'element-plus/es/utils'

// 企业基本信息 VO
export interface CompanyBasicInfoVO {
  id?: number
  code: number //状态码
  success: boolean //访问成功状态
  msg: string //操作状态
  data?: [] //返回值数组
  eid: string //企业eid
  entName: string //企业名称
  creditNo: string //企业信用码
  status: string //企业状态
  legalPerson: string //法人
  establishDate: string //成立日期
  regCapi: string //注册资本
  actualCapi: string //实缴资本
  brief: string //企业简介
  address: string //注册地址
  industryCode: string //行业编码
  industryName: string //国民经济行业名称
  checkDate: string //核准日期
  typeName: string //企业类型名称
  termStart: string //营业日期自
  termEnd: string //营业日期至
  orgNo: string //组织机构代码
  historyName: string //历史名称
  longitude: number //经度（百度）
  latitude: number //维度（百度）
  regOrg: string //登记机关
  scope: string //经营范围
  term: string //经营期限
  insureNum: string //社保人数
  regNo: string //注册机构号
  website: string //网址
  email: string //邮箱
  phone: string //联系电话
  tags?: [] //标签
  entTypeOri: string //企业类型
  province: string //所在省
}

// 股东信息
export interface OrganizationInfoVO {
	code:	number	//状态码
	success:	boolean	//访问成功状态
	msg:	string	//操作状态
	data?: []	//返回值数组
	total:	number	///总条数
	list?: []	//List列表（暂时不用）
	rows?: []	//记录列表
	eid:	string	//企业eid
	partnerId:	string	//股东id
	partnerName:string	//股东名称
	partnerType:	string	//股东类型
	shouldCapi:	string	//应缴出资额
	shouldType:	string	//应缴出资方式
	isHistory:	number	//是否历史（0-历史，1-非历史）
	isHistoryDesc:	string	//是否历史
	shouldCurrency:	string	//应缴币种
	realCapi:	string	//实缴出资额
	realType:	string	//实缴出资方式
	realCurrency:	string	//实缴币种
	percent:	string	//股权占比
	stockType:	string	//股份类型
  // 主要人员（高管）
  managerId:	string	//人员id
  managerName:	string	//人员姓名
  managerPosition:	string	//人员职位
  managerType:	string	//人员类型
}

export interface RisklitigationVO {
  caseNo:	string	//案号
  caseReason:	string	//案由
  startDate:	string	//立案日期  
  court:	string	//法院
  litigantName:	string	//当事人名称
  caseStatus:	string	//案件状态

  eid:	string	//企业eid
  punishId:	string	//行政处罚id
  docNo:	string	//决定书文号
  illegalType:	string	//违法行为类型
  punishOrg:	string	//决定机关
  punishDate:	string	//处罚决定日期
  pubDate:	string	//公示日期
  punishContent:	string	//行政处罚内容
  punishBase:	string	//处罚依据
  illegalFact:	string	//主要违法事实
  isHistory:	number	//是否历史（1-是，0-否）
  isHistoryDesc:	string	//是否历史

  mortNo:	string	//登记编号
  mortDate:	string	//登记日期
  mortOrg:	string	//登记机关
  debtType:	string	//被担保债权种类
  debtAmount:	string	//被担保债权数额
  debtTerm:	string	//债务人履行债务的期限
}

// 员工年龄 VO
export interface EmployeeVO {
  ageStage: string;
  /*35岁及以下 */
  underThreeFive: number;
  /*36-50岁 */
  threeFiveWithFive: number;
  /*50岁以上 */
  overFive: number;
  /*合计 */
  allAgeCount: number;
  /*35岁及以下 */
  underThreeFiveRate: Record<string, unknown>;
  /*36-50岁 */
  threeFiveWithFiveRate: Record<string, unknown>;
  /*50岁以上 */
  overFiveRate: Record<string, unknown>;
  /*合计 */
  allAgeRate: Record<string, unknown>;
}

// 企业基本信息 API
export const CompanyBasicInfoApi = {
  // 查询企业画像基本信息
  requestCompanyBasicInfo: async (data: any) => {
    return await request.post({ url: `/model/zealmon/request`, data })
  },
  // 查询企业画像基本信息
  cacheCompanyBasicInfo: async (data) => {
    return await request.post({ url: `/model/zealmon/request/cache`, data })
  },

  // 查询员工年龄
  getEmployee: async (data: any) => {
    return await request.post({ url: `/message/iam/employee`, data })
  },
}
