import request from '@/config/axios'

// 整改结果录入 VO
export interface ReformingResultsVO {
	/*Id */
	id : number;
	/*项目编码 */
	projectCode : string;
	/*问题编号 */
	questionCode : string;
	/*审计对象 */
	auditObject : string;
	/*审计年度 */
	projectYear : string;
	/*问题整改分类 */
	quesRectificType : number;
	/*主体责任人部门 */
	responsibDeptName : string;
	/*计划完成整改时间 */
	planPassTime : Record<string, unknown>;
	/*问题类型 */
	quesTypeName : string;
	/*问题标题 */
	questionName : string;
	/*问题描述 */
	quesDigest : string;
	/*审计意见 */
	auditSugg : string;
	/*问题责任人 */
	issuedPersonName : string;
	/*下发时间 */
	issuedTime : Record<string, unknown>;
}
export interface ReformingResultsDetailVO {
	/*Id */
	id : number;
	/*项目编码 */
	projectCode : string;
	/*问题编号 */
	questionCode : string;
	/*审计对象 */
	auditObject : string;
	/*审计年度 */
	projectYear : string;
	/*问题整改分类 */
	quesRectificType : number;
	/*主体责任人部门 */
	responsibDeptName : string;
	/*计划完成整改时间 */
	planPassTime : Record<string, unknown>;
	/*问题类型 */
	quesTypeName : string;
	/*问题标题 */
	questionName : string;
	/*问题描述 */
	quesDigest : string;
	/*审计意见 */
	auditSugg : string;
	/*问题责任人 */
	issuedPersonName : string;
	/*下发时间 */
	issuedTime : Record<string, unknown>;
}

// 整改结果录入 API
export const ReformingResultsApi = {
	// 查询整改结果录入列表分页
	getReformingResultsList: async (params : any) => {
		return await request.get({ url: `/manage/rectific-result/page`, params })
	},

	// 查询整改结果录入详情
	getReformingResults: async (id ?: number, detailId ?: number) => {
		return await request.get({ url: `/manage/rectific-result/audit-detail?id=` + id + `&detailId=` + detailId })
	},

	// 创建审计整改-整改反馈-整改结果录入详情
	createReformingResults: async (data : ReformingResultsVO) => {
		return await request.post({ url: `/manage/rectific-result/create`, data })
	},

	// 修改整改结果录入
	updateReformingResults: async (data : ReformingResultsVO) => {
		return await request.put({ url: `/manage/rectific-result/update`, data })
	},
}
