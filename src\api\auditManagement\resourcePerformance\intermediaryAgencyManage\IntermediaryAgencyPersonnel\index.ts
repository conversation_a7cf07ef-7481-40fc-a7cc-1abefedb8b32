import request from '@/config/axios'
interface SeachVo {
    pageNo: number
    pageSize: number
    personName?: string
    userLoginName?: string
    orgName?: string
    resourceOrgId: number
    personQualification?: string
}
export interface AddPersonVo {
    id?: number
    personName: string
    resourceOrgId: number
    personSex: number
    personBirthday: string
    personPhone: number
    personEmail: string
    personQualification: string
    personExpertise: string
}

// 中介机构人员管理 API
export const AgencyPersonalApi = {
    //中介机构库-人员分页-查看详情
    getPersonDetail: async (id: number) => {
        return await request.get({ url: `/manage/org-person-manage/get?id=${id}` })
    },
    // 中介机构人员管理-人员列表查询
    getPersonList: async (params: SeachVo) => {
        return await request.get({ url: `/manage/org-person-manage/page`, params })
    },
    // 中介机构人员管理-新增中介机构人员
    createPerson: async (data: AddPersonVo) => {
        return await request.post({ url: `/manage/org-person-manage/create`, data })
    },
    // 中介机构人员管理-编辑保存中介机构人员
    updatePerson: async (data: AddPersonVo) => {
        return await request.put({ url: `/manage/org-person-manage/update`, data })
    },
    // 中介机构人员管理-删除中介机构人员
    deletePerson: async (id: number) => {
        return await request.delete({ url: `/manage/org-person-manage/delete/?id=${id}` })
    },

}