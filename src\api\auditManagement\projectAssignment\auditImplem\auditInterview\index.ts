import request from '@/config/axios'

// 审计访谈
export interface AddVO {
  projectId: number
  fileList?: []
}

// 审计通知书 API
export const AuditInterviewApi = {
  // 审计访谈-录入-保存
  saveFilelist: async (data: AddVO) => {
    return await request.post({ url: `/manage/audit-interview/creat`, data })
  },
  // 获取当前用户的审计角色
  getAuditRoleName: async (params: any) => {
    return await request.get({ url: `/manage/audit-interview/get-user-proj-role`, params})
  },
  getAuditTypeListByType: async () => {
    return await request.get({ url: `audit/tank-trees-type/get?type=4`})
  },

}
