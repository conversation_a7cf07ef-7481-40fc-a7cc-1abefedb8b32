/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-15 20:05:56
 * @Description: 整改约谈记录单
 */
import request from '@/config/axios'

// 记录单 VO
// export interface RectificaNoticeVO {
	
// }
// export interface RectificaNoticeDetailVO {
	
// }

// 记录单 API
export const InterviewSheetApi = {
	// 列表
    getInterviewSheetList: async(params: any) => {
		return await request.get({ url: `/manage/notice-supervision/supervisionPage`, params })
	},
	// 单列表导出
	getInterviewSheetDerive: async(params: any) => {
		return await request.download({ url: `/manage/notice-supervision/exportExcelSupervision`, params })
	},
    // 生成记录单
    getInterviewSheetOne: async (id: number | string) => {
		return await request.get({ url: `/manage/notice-supervision/initVisitRecord?id=` + id })
	},
    // 上传记录单
    getInterviewSheetUpdate: async (data: any) => {
		return await request.post({ url: `/manage/notice-supervision/bindSupervision`, data })
	},
	// 下载记录单
	getInterviewSheetDownload: async (id: number) => {
		return await request.get({ url: `/infra/file-info/downloadFile/` + id })
	},
    // 查看记录单
    getInterviewSheetViwe: async(params: any) => {
		return await request.get({ url: ``, params })
	},
    
}