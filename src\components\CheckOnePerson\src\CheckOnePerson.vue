<template>
  <Dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="65%"
    :scroll="true"
    maxHeight="72vh"
    setScrollTop="2vh"
  >
    <ContentWrap class="common-card-search">
      <!-- 搜索工作栏 -->
      <el-form
        ref="queryFormRef"
        :inline="true"
        :model="queryParams"
        class="-mb-15px common-search-form"
        label-width="68px"
      >
        <el-form-item label="账号" prop="username">
          <el-input
            v-model="queryParams.username"
            placeholder="请输入账号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-200px"
          />
        </el-form-item>
        <el-form-item label="姓名" prop="nickname">
          <el-input
            v-model="queryParams.nickname"
            placeholder="请输入姓名"
            clearable
            @keyup.enter="handleQuery"
            class="!w-200px"
          />
        </el-form-item>
        <el-form-item label="手机号码" prop="mobile">
          <el-input
            v-model="queryParams.mobile"
            placeholder="请输入手机号码"
            clearable
            @keyup.enter="handleQuery"
            class="!w-200px"
          />
        </el-form-item>
        <el-form-item label="人员类型" prop="userType">
          <el-select
            v-model="queryParams.userType"
            @change="handleQuery"
            placeholder="请选择职务"
            clearable
            class="!w-200px"
          >
            <el-option
              v-for="dict in typeList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属组织" prop="deptId">
          <el-tree-select
            v-model="queryParams.deptId"
            ref="treeRef"
            clearable
            placeholder="请选择所属组织"
            :data="deptList"
            check-strictly
            :expand-on-click-node="false"
            :check-on-click-node="true"
            :default-expand-all="false"
            highlight-current
            node-key="id"
            @node-click="handleNodeClick"
            :load="loadNode"
            @change="deptChange"
            :default-expanded-keys="defaultExpandedKeys"
            :filter-node-method="filterNode"
            lazy
            class="!w-200px"
          >
            <template #default="{ data: { name } }">{{ name }}</template>
          </el-tree-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="用户状态" clearable class="!w-200px">
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="right-search-btn">
        <el-button type="primary" @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />重置
        </el-button>
      </div>
    </ContentWrap>
    <ContentWrap>
      <el-table border v-loading="loading" :data="list" @selection-change="handleSelectionChange">
        <el-table-column label="单选" align="center" width="80" fixed>
          <template #default="{ row }">
            <el-radio
              v-model="selectedRowId"
              :value="row.id"
              @change="handleClick(row)"
              class="custom-radio"
            />
          </template>
        </el-table-column>
        <el-table-column label="账号" align="center" prop="username" :show-overflow-tooltip="true" />
        <el-table-column label="用户姓名" align="center" prop="nickname" :show-overflow-tooltip="true" />
        <el-table-column label="手机号码" align="center" prop="mobile" :show-overflow-tooltip="true" min-width="100"/>
        <el-table-column
          label="所属公司"
          align="left"
          prop="unitName"
          :show-overflow-tooltip="true"
          min-width="200"
        />
        <el-table-column
          label="所属部门"
          align="left"
          key="deptName"
          prop="deptName"
          min-width="160"
          :show-overflow-tooltip="true"
        />
        <!-- <el-table-column label="部门" align="center" prop="deptName" width="120" /> -->
        <!-- <el-table-column label="岗位" align="center" prop="postName" width="120" /> -->
        <el-table-column label="用户状态" key="status" align="center" width="88">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SYSTEM_USER_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        v-model:limit="queryParams.pageSize"
        v-model:page="queryParams.pageNo"
        :total="total"
        @pagination="getList"
      />
    </ContentWrap>

    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as TenantApi from '@/api/system/tenant'
import * as UserApi from '@/api/system/user'
import { CommonStatusEnum } from '@/utils/constants'
import { number } from 'vue-types'
import * as DeptApi from '@/api/system/dept'
import { defaultProps, handleLazyTree } from '@/utils/tree'
import type Node from 'element-plus/es/components/tree/src/model/node'
defineOptions({ name: 'AddUser' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const selectedRowId = ref<number>()
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
interface tableVo {
  id: number
  username: string
  nickname: string
  mobile: number
  status: number
}
const list = ref<tableVo[]>([]) // 列表的数据
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  username: undefined,
  nickname: undefined,
  mobile: undefined,
  status: undefined,
  deptId: undefined as any,
  deptName: undefined,
  userType: 2
})
const queryFormRef = ref() // 搜索的表单
const formRef = ref() // 表单 Ref
/** 查询角色列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await getPersonType(queryParams.value.userType)
    list.value = data.list || []
    total.value = data.total || 0
  } finally {
    loading.value = false
  }
}
const getPersonType = (type: number) => {
  if (type === 2) {
    return UserApi.getUserPage(queryParams.value)
  } else if (type === 0) {
    return UserApi.getObjPerson(queryParams.value)
  } else if (type === 1) {
    return UserApi.getObjPerson(queryParams.value)
  } else {
    return UserApi.getUserPage(queryParams.value)
  }
}
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}
const props = defineProps({
  deptId: {
    default: null,
    type: Number
  }
})
const personRow = ref<tableVo>()
const handleClick = (row: tableVo) => {
  selectedRowId.value = row.id
  personRow.value = row
}
/** 打开弹窗 */
const open = async (id?: number, row?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('选择')
  //   formType.value = type
  resetForm()
  await getTree(0)

  // 修改时，设置数据
  if (id) {
    let list = [
      {
        id: id,
        masterOrgId: row.deptId,
        parentId: 0,
        name: row.deptName
      }
    ]
    deptList.value = []
    deptList.value.push(...handleLazyTree(list, 'id', 'parentId'))
    queryParams.value.deptId = id
    formLoading.value = true
    try {
      getList()
    } finally {
      formLoading.value = false
    }
  } else {
    queryParams.value.deptId = undefined as any
    formLoading.value = true
    try {
      getList()
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
interface AddRoleVo {
  code: string
  name: string
  remark: string
  id: number
}
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const multipleSelection = ref<AddRoleVo[]>([])
const handleSelectionChange = (val: AddRoleVo[]) => {
  multipleSelection.value = val
}
const submitForm = async () => {
  if (!selectedRowId.value) {
    message.warning('请选择用户')
    return
  }

  formLoading.value = true
  try {
    emit('success', personRow.value, queryParams.value.userType)
    // message.success(t('common.createSuccess'))
    resetForm()
    selectedRowId.value = undefined
    personRow.value = undefined
    dialogVisible.value = false
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    username: undefined,
    nickname: undefined,
    mobile: undefined,
    status: undefined,
    deptId: undefined,
    deptName: undefined,
    userType: 2
  }
  queryFormRef.value?.resetFields()
}
interface DeptNode {
  id: number
  masterOrgId?: number | string
  name: string
  parentId: number | string
  children?: DeptNode[]
  isLeaf?: boolean
}
const deptList = ref<DeptNode[]>([]) // 树形结构
const defaultExpandedKeys = ref<(string | number)[]>([])
const getTree = async (id) => {
  deptList.value = []
  const res = await DeptApi.getSimpleDeptList(id)
  deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
  console.log(deptList.value)

  defaultExpandedKeys.value = deptList.value.map((node) => node.id)
}
const handleNodeClick = (node, nodeData) => {
  // 如果是父节点，选中它
  // queryParams.value.deptId = node.id
  // getList()
}
const typeList = [
  {
    label: '组织人员',
    value: 2
  },
  {
    label: '审计人员',
    value: 0
  },
  {
    label: '借调人员',
    value: 1
  }
]
const filterNode = (name: string, data: DeptNode) => {
  if (!name) return true
  return data.name.includes(name)
}
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}
const loadNode = async (node: Node, resolve: (data: any[]) => void) => {
  try {
    const nodeId = node.data.id
    if (nodeId == undefined || nodeId == null) {
      return
    }
    const res = await DeptApi.getSimpleDeptList(nodeId)
    const children = handleLazyTree(res, 'id', 'parentId', 'children')
    resolve(children)
  } catch (error) {
    resolve([])
  }
}
const deptChange = async () => {
  if (deptList.value?.length === 1) {
    await getTree(0)
  }
  getList()
}
</script>
<style scoped>
.custom-radio .el-radio__label {
  display: none; /* 隐藏单选按钮的文本 */
}
</style>
