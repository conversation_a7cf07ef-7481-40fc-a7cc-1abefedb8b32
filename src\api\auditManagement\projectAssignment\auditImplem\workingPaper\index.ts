import request from '@/config/axios'

// 审计工作底稿 VO
export interface AdviceOfAuditVO {
  id?: number // 主键，唯一标识
  infoCode: string
  infoName: string
  infoType: number
  projectNo: string
  projectName: string
  creatorName: string
  createTime: string
  flowStatus: number
  auditObject: string
}

// 审计工作底稿 API
export const WorkingPaperApi = {
  // 获得项目作业-审计工作底稿分页
  getWorkingPaperList: async (params: any) => {
    return await request.get({ url: `/manage/audit-work-manuscript/page`, params })
  },
  // 审计工作底稿关联其他材料
  getWorkingManuscript: async (id: any) => {
    return await request.get({ url: `/manage/audit-work-manuscript/get?id=${id}` })
  },
  // 查询已关联关联其他材料
  getMaterialByMattersid: async (id: any) => {
    return await request.get({ url: `/manage/audit-work-manuscript/get-material-by-mattersid?id=${id}` })
  },
  // 审计工作底稿是否关联其他材料
  getMaterialCount: async (id: any) => {
    return await request.get({ url: `/manage/rele-material/getmaterialcount?id=${id}` })
  },
  //底稿提交接口
	workStartFlow: async (data : any) => {
		return await request.post({ url: `/manage/audit-work-manuscript/start-flow`, data })
	},
  //获取
	getFileList: async (code:string) => {
		return await request.get({ url: `/system/config-prog-atta/file-list?code=${code}` })
	},
  //获取附件
	getOtherFileList: async (params: any) => {
		return await request.get({ url: `/manage/project-assign-atta/page`,params })
	},
  //获取是否关联
	getmaterialcount: async (ids: any) => {
		return await request.get({ url: `/manage/rele-material/getmaterialcount?id=${ids}` })
	},
  //删除附件
	deleteFile: async (id: string) => {
		return await request.delete({ url: `/manage/project-assign-atta/delete?id=${id}` })
	},

  //项目清单取证单
  projResource: async (fileType:string,projectId:any) => {
    return await request.get({ url: `/manage/project-assign-atta/getlist/by-codelist?fileType=${fileType}&projectId=${projectId}` })
  },
  //项目清单取证单2
  linkProject: async (projectId:string) => {
    return await request.get({ url: `/manage/manage-cert/link-project/page?projectId=${projectId}` })
  },
  //
  releMaterial: async (data:any) => {
    return await request.post({ url: `/manage/rele-material/create`,data })
  },
  // 审计工作底稿-提交（单个）
  submitBatchWork: async (data: any) => {
    return await request.post({ url: `/manage/audit-work-manuscript/submitBatch`, data  })
  },
  //流程中查询详情接口
  getAuditworkfile: async (instanceId: string) => {
    return await request.get({ url: `/manage/audit-work-manuscript/get-auditworkfile?instanceId=${instanceId}`  })
  },
	// 导出项目资料清单 Excel
	exportWork: async (params : any) => {
		return await request.download({ url: `/manage/audit-work-manuscript/export-excel`, params })
	},
	// 流程接口
	showWork: async (id : any) => {
		return await request.get({ url: `/manage/audit-work-manuscript/show?id=${id}` })
	},
}
