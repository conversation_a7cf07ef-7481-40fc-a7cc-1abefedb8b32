import request from '@/config/axios'



// 整改台账 API
export const reformingStandingBookApi = {
	// 查询整改台账列表分页
	getRectificationLedgerList: async (data : any) => {
		return await request.post({ url: `/manage/notice-rectification/page/rectification-ledger`, data })
	},
	// 导出整改台账
	getRectificationLedger: async (params : any) => {
		return await request.download({ url: `/manage/notice-rectification/page/rectification-ledger/export`, params })
	},

}
