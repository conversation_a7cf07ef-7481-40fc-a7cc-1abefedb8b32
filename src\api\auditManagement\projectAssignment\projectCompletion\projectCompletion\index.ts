import request from '@/config/axios'
export interface saveVO {
    realCloseoutTime: string
    status: number
    dataList: []
} 
export interface searchListVO {
	pageNo: number
	pageSize: number
	projectName?: string
	orgType?: number
	auditObject?: number
	auditType?: number
	auditYear?: string
	projStage?: number
	overseasFlag?: number
}
// 项目结项
export const ProjectCompletionApi = {
    // 项目结项-结项-弹窗查询
    getProCompletionDetail: async (id: number) => {
        return await request.get({ url: `/manage/end-group-evaluate/getEndGroupEvaluateList?projectId=${id}` })
    },
    saveCompletion: async (data: saveVO) => {
        return await request.post({ url: '/manage/end-group-evaluate/closeout', data })
    },
    // 项目结项列表查询
    getCompletiontList: async (data: searchListVO) => {
		return await request.post({ url: `/manage/proj-plan-project/settlementPage`, data })
	},
}