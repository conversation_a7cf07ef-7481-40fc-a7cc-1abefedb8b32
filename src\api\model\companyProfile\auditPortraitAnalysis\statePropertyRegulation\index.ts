/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-29 09:33:33
 * @Description: 国资监管API
 */

import request from '@/config/axios'

// 审计报告初稿 VO
export interface AuditProblemsVO {
	id ?: number,
	projectId : string, //项目ID
	questionName : string,// 问题标题
	quesDigest : string,// 问题摘要
	auditSugg : string,// 审计建议
	quesTypeId : number,// 问题类型
	discoverUserId : number,// 发现人id
	abarbeitungFlag : number,// 是否整改
	handOverFlag : number,// 是否移交
}
export interface AuditProblemsDetailVO {
	id: number, 
    projectId: string, //项目ID
    questionCode: number,//问题编号id
    questionName: string,// 问题标题
    quesDigest: string,// 问题摘要
    auditSugg: string,// 审计建议
    quesTypeId: number,// 问题类型id
    quesTypeName: string //问题类型
    discoverUserId: number,// 发现人id
    discoverUserName: string, //发现人
    abarbeitungFlag: number,// 是否整改
    handOverFlag: number,// 是否移交
    createdBy: undefined, //创建人
    createdTime: undefined, //创建时间
    updatedBy: undefined, //更新人
    updatedTime: Date, //更新时间
}


// 国资监管 API
export const StateAssetsApi = {
	// 查询国资监管列表分页
	StateAssetsApiList: async (params: any) => {
		return await request.get({ url: ``, params })
	},
	// 查询应付款余额构成数据
	StateAssetsApiEcharts: async (params: any) => {
		return await request.get({ url: ``, params })
	},
}