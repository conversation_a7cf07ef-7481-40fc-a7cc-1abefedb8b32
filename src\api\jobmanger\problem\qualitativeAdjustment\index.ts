/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-13 14:27:30
 * @Description: 问题定性调整API
 */
import request from '@/config/axios'

// 问题定调整 API
export const QualitativeAdjustmentApi = {
	// 查询问题定性调整列表分页
	getQualitativeAnalysisList: async (params : any) => {
		// return await request.get({ url: `/audit/tank-ques-qualitation/get-problemadjustments-page`, params })
		return await request.get({ url: `/manage/proj-await-question/page`, params })
	},

	// 导出问题定性调整
	exportQualitativeAnalysis: async (params : any) => {
		return await request.download({ url: `/audit/tank-ques-qualitation/export-excel-problemadjustments`, params })
	},

    // 调整问题定性
    SelectApi: async (data : any) => {
		return await request.post({ url: `/manage/proj-await-question/update-awit-question-type`, data})
	},

	// 问题整改归纳
	updateQualitative: async (data : any) => {
		return await request.put({ url: `/manage/proj-await-question/update`, data })
	},
}

//调整定性分类
export const SelectApi = (url : string, data: any) => {
	return request.post({ url, data })
}

