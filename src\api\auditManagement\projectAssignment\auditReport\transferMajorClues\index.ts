import request from '@/config/axios'


export interface searchListVO {
  pageNo: number
  pageSize: number
  projectName?: string
  orgType?: number
  auditObject?: number
  auditType?: number
  auditYear?: string
  projStage?: number
  overseasFlag?: number
}
// 重大线索 API
export const transferMajorCluesApi = {
	// 查询重大线索列表分页
	getTransferMajorCluesList: async (params : searchListVO) => {
		return await request.get({ url: `/manage/assign-import-clue-move/clueMovePage`, params })
	},
	// 重大线索保存提交
	assigImportClueMove: async (data : any) => {
		return await request.post({ url: `/manage/assign-import-clue-move/saveOrUpdate`, data })
	},
	// 重大线索查询
	getAssigImportClueMove: async (id : any) => {
		return await request.get({ url: `/manage/assign-import-clue-move/get?id=`+ id })
	},



}
