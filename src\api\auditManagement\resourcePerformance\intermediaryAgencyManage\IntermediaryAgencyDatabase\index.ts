import request from '@/config/axios'
interface SeachVo {
	auditProjectType: number | undefined
	orgName: string | undefined
}
export interface AddOrgManageVo {
	id?: number
	orgName: string
	orgCode: string
	orgInstitutionsCode: string
	auditProjectType: number
	auditProjectTypeDesc: string

	shortlistedTime: string
	enforcementTaxRate: string
	serviceRange: string
	belongCity: string
	serviceStatus: number
	serviceStatusDesc: string
	chargingStandards: string
	orgSetting: string
	orgPersonSize: string
	cpaNumber: number
	aqcNumber: string
	contactName: string
	contactDuties: string
	contactPhone: string
	contactEmail: string
	companyLogo?: number

}
interface OrgPersonManageVo {
	pageNo: number
	pageSize: number
	resourceOrgId: number
}
// 中介机构库管理 API
export const AgencyDatabaseApi = {
	getAgencyDatabaseList: async (params: SeachVo) => {
		return await request.get({ url: `/manage/org-manage/optionList`, params })
	},
	// 获取中介机构基本信息
	getOrgManageDetail: async (id: number) => {
		return await request.get({ url: `/manage/org-manage/get/?id=${id}` })
	},
	// 获取左侧中介机构列表
	getOrgPersonList: async (params: any) => {
		return await request.get({ url: `/manage/org-manage/page`, params })
	},
	// 创建中介机构
	createOrgManage: async (data: AddOrgManageVo) => {
		return await request.post({ url: `/manage/org-manage/create`, data })
	},
	// 中介机构库-人员分页
	getOrgPersonManageList: async (params: OrgPersonManageVo) => {
		return await request.get({ url: `/manage/org-person-manage/page`, params })
	},
	// 中介机构库-机构选聘列表
	getInstitutionSelectionList: async (params: any) => {
		return await request.get({ url: `/manage/org-manage/page`, params })
	},
	// 中介机构库-机构选聘列表-选聘详情
	getInstitutionSelectionDetail: async (id: number) => {
		return await request.get({ url: `/manage/org-manage/get/?id=${id}`, })
	},
	// 中介机构库-机构选聘列表-选聘保存
	updateInstitutionSelection: async (data: AddOrgManageVo) => {
		return await request.put({ url: `/manage/org-manage/update`, data })
	},
	// 中介机构库-机构选聘列表-推出
	exitInstitutionSelection: async (id: number) => {
		return await request.get({ url: `/manage/org-manage/exit/?id=${id}` })
	},

}