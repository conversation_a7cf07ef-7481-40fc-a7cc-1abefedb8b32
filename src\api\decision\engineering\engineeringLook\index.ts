import request from '@/config/axios'

// 招标控制价-看板 
export interface EngineeringLookVO {
	/*开展项目数 */
    projectNum: number;
    /*合同数 */
    contractNum: number;
    /*工程数 */
    enNum: number;
    /*已报审工程数-结算 */
    settleEnNum: number;
    /*结算审计完成数量-结算 */
    settleAuditedNum: number;
    /*已报审工程数-控制 */
    controlEnNum: number;
    /*控制价审计完成数量-控制 */
    controlAuditedNum: number;
}

// 招标控制价 API
export const EngineeringLookApi = {
	// 获得看板头部数字
	getHomeNum: async (params : any) => {
		return await request.get({ url: `/manage/engineering/homeNum`, params })
	},

	// 获得审计项目-审减
	getSettleMinusBoard: async (params : any) => {
		return await request.get({ url: `/manage/engineering/settle/minusBoard`, params })
	},

    // 获得结算-审计效率
	getSettleEfficiencyBoard: async (params : any) => {
		return await request.get({ url: `/manage/engineering/settle/efficiencyBoard`, params })
	},

    // 获得结算-审减原因统计
	getSettleQuestionBoard: async (params : any) => {
		return await request.get({ url: `/manage/engineering/settle/questionBoard`, params })
	},

    // 获得结算-中介审计完成率
	getSettleFinishNumBoard: async (params : any) => {
		return await request.get({ url: `/manage/engineering/settle/finishNumBoard`, params })
	},

    // 获得控制价设计-审减
	getControlMinusBoard: async (params : any) => {
		return await request.get({ url: `/manage/engineering/control/minusBoard`, params })
	},

    // 获得招标控制价设计-审计效率
	getControlEfficiencyBoard: async (params : any) => {
		return await request.get({ url: `/manage/engineering/control/efficiencyBoard`, params })
	},

    // 获得招标控制价设计-审减原因统计
	getControlQuestionBoard: async (params : any) => {
		return await request.get({ url: `/manage/engineering/control/questionBoard`, params })
	},

    // 获得招标控制价设计-审计项目完成项目数
	getControlFinishNumBoard: async (params : any) => {
		return await request.get({ url: `/manage/engineering/control/finishNumBoard`, params })
	},
}