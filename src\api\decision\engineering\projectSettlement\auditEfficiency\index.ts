import request from '@/config/axios'

// 审计效率VO
export interface AuditEfficiencyVO{
	/*一审单位（中介机构id */
	id?: string;
	/*一审单位(中介机构名称) */
	auditorName: string;
	/*委托基建结算数量(项) */
	entrustedSettlements: number;
	/*审定基建结算数量(项) */
	auditedSettlements: number;
	/*基建结算审计完成率 */
	auditCompletionRate: Record<string, unknown>;
	/*初审平均耗时（天） */
	initialAuditDuration: Record<string, unknown>;
	/*终审平均耗时（天） */
	finalAuditDuration: Record<string, unknown>;
	/*出具纸版审计报告平均耗时（天） */
	reportDuration: Record<string, unknown>;
}
export interface AuditEfficiencyDetailsVO {
	id : number // 主键，唯一标识
	/*工程名称 */
	engineeringName: string;
	/*工程概况 */
	engineeringOverview: string;
	/*审计单位(中介机构名称) */
	auditorName: string;
	/*投资单位 */
	investorName: string;
	/*管理单位 */
	managerName: string;
	/*施工单位 */
	constructorName: string;
	/*初审平均耗时（天） */
	initialAuditDuration: Record<string, unknown>;
	/*终审平均耗时（天） */
	finalAuditDuration: Record<string, unknown>;
	/*出具纸版审计报告平均耗时（天） */
	reportDuration: Record<string, unknown>;
}

// 审计效率 API
export const AuditEfficiencyApi = {
	// 查询审计效率列表分页
	getAuditEfficiencyList: async (params : any) => {
		return await request.get({ url: `/manage/en-settle/get-settle-efficiency`, params })
	},

	// 审计效率列表 Excel
	exportAuditEfficiency: async (params : any) => {
		return await request.download({ url: `/manage/en-settle/export-settle-efficiency-excel`, params })
	},

	// 查询审计效率合计
	getAuditEfficiencyTotal: async (params : any) => {
		return await request.get({ url: `/manage/en-settle/get-settle-efficiency-total`, params })
	},

	// 查询审计效率详情
	getAuditEfficiencyDetails: async (params : any) => {
		return await request.get({ url: `/manage/en-settle/get-settle-efficiency-engineering`, params })
	},

	// 详情列表的合计
	getAuditEfficiencyDetailsTotal: async (params : any) => {
		return await request.get({ url: `/manage/en-settle/get-settle-efficiency-engineering-total`, params })
	},

	// 审计效率详情 Excel
	exportAuditEfficiencyDetails: async (params : any) => {
		return await request.download({ url: `manage/en-settle/export-settle-efficiency-engineering-excel`, params })
	},
	
}