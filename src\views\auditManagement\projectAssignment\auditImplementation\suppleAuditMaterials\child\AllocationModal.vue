
<template>
  <Dialog :title="dialogTitle" :scroll="true" v-model="dialogVisible" width="35%" maxHeight="50vh">
    <el-row :gutter="16">
      <el-col >
        <el-card class="cardHeight">
          <el-table border :data="userList" :stripe="true" :show-overflow-tooltip="true" @selection-change="handleSelectionChange">
            <el-table-column type="selection" :selectable="selectable" width="55" />
            <el-table-column label="#" width="50" align="center">
              <template
                #default="{ $index }"
              >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
            </el-table-column>
            <el-table-column label="部门" align="center" prop="deptName" min-width="120" />
            <el-table-column label="姓名" align="center" prop="userName" />
          </el-table>
          <el-row :gutter="16">
            <el-col :span="24" class="text-right mt-16px"><el-button @click="handleOther" type="primary" :disabled="formLoading">选择其他人员</el-button></el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">保存</el-button>
      <el-button @click="dialogVisible = false">取消</el-button>
    </template>
  </Dialog>

  <!-- 文件上传 -->
  <FileForm ref="formImgRef" @success="handleUploadSuccess" :type="fileType" :limit="fileLimit" />
  <!-- <CheckPersonList ref="checkPersonRef" @success="checkSuccess" /> -->
  <!-- <ConfirmUnit ref="confirmUnitRef" />
  <PlanFillingStatus ref="planFillingStatus" />
  <AdjustModal ref="adjustModal" />-->
</template>
<script setup lang="ts">
// import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
// import { AuditRoleApi, AuditRoleVO } from '@/api/basicData/auditRole'
import {
  ProjectApprovalApi
} from '@/api/auditManagement/reviewManagement/projectApproval'
import FileForm from '@/views/infra/file/FileForm.vue'
	// import { CheckPersonList } from '@/components/CheckPersonList'
// import { fa } from 'element-plus/es/locale'
// import ConfirmUnit from './ConfirmUnit.vue'
// import PlanFillingStatus from './PlanFillingStatus.vue'
// import AdjustModal from './AdjustModal.vue'
/** 审计角色 表单 */
defineOptions({ name: 'AllocationModal' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
// const formType = ref('') // 表单的类型：create - 新增；update - 修改
const UpFileList = ref()
const formData = ref({
  id: undefined,
  auditRoleName: undefined,
  parentId: undefined,
  sort: undefined,
  status: undefined,
  projectPhase: undefined,
  date: undefined,
  content: undefined,
  wbflfgList: [],
  nbgzzdList: [],
  sjmxList: []
})
const listFile = ref()
interface deptVO {
  deptId: number
  deptName: string
  reportName: string
  reportId: number
  checkStatus: boolean
}
const userList = ref([])
const formRules = reactive({
  auditRoleName: [{ required: true, message: '不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  parentId: [{ required: true, message: '父级编号不能为空', trigger: 'change' }]
})
// const checkPersonRef = ref()
// 选择人员
const handleOther = () => {
  // checkPersonRef.value.open()
    dialogVisible.value = false
    emit('other')

}
const checkSuccess = (perArr) => {
  // 检查并添加人员
  perArr.value.forEach((item) => {
    if (!userList.value.some((user) => user.id === item.id)) {
      userList.value.push(item)
    }
  })
}
const formRef = ref() // 表单 Ref
const adjustModal = ref()
const showPlanAdjust = () => {
  adjustModal.value.open(0)
}
const activeName = ref('0')
const handleClick = () => {}
const planFillingStatus = ref()
const showPlanStatus = () => {
  planFillingStatus.value.open('detail', -1)
}
const formImgRef = ref()
const fileType = ref('img')
const fileLimit = ref(1)
// 上传方法
const handleImport = (type: string) => {
  fileType.value = type
  fileLimit.value = type === 'file' ? 5 : 1
  formImgRef.value.open()
}
const handleUploadSuccess = (fileList) => {
  let fileArr =
    fileList && fileList.length > 0
      ? fileList.map((item) => {
          return {
            url: item.response.data,
            name: item.name
          }
        })
      : []
  if (fileType.value === 'file') {
    formData.value.attachments = formData.value.attachments.concat(...fileArr)
  } else if (fileType.value === 'img') {
    formData.value.showImg = fileArr
    formRef.value.validateField('showImg')
  }
}
const handleDelete = async (type: string, index: number = 0) => {
  await message.delConfirm()
  formData.value[type].splice(index, 1)
  await message.success(t('common.delSuccess'))
}

/** 打开弹窗 */
const open = async ( id: string) => {
  dialogVisible.value = true
  dialogTitle.value = t('分配')
  // formType.value = type
  // resetForm()
  // 修改时，设置数据
  // formLoading.value = true
  console.log(id);
  const data =await ProjectApprovalApi.getWelAuditGroupList(id)
  userList.value=data
  return
  
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// const dialogListTitle = ref('') // 选择弹窗的标题
// const dialogListVisible = ref(false) // 选择弹窗的是否展示

const openWbList = () => {
  // dialogListVisible.value = true
}

const handleWbDelete = (val) => {}

const handleWbView = (val) => {}

const openNbList = () => {}
const handleNbDelete = () => {}
const handleNbView = () => {}

const openMxList = () => {}
const handleMxDelete = () => {}
const handleMxView = () => {}
const detailData = ref({
  auditRoleCode: 0
})
	const handleSelectionChange = (val : []) => {
    console.log(val);
    UpFileList.value = val
    
	}
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // console.log(upData);
  
  if (UpFileList.value.length<1) {
    message.error('请选择需要添加的用户')
    return
  }
    dialogVisible.value = false
    emit('success',UpFileList)
  // 校验表单
  // await formRef.value.validate()
  // 提交请求
  // formLoading.value = true
  // try {
  //   const data = formData.value 
  //   if (formType.value === 'create') {
  //     await AuditRoleApi.createAuditRole(data)
  //     message.success(t('common.createSuccess'))
  //   } else {
  //     await AuditRoleApi.updateAuditRole(data)
  //     message.success(t('common.updateSuccess'))
  //   }
  //   // 发送操作成功的事件
  // } finally {
  //   formLoading.value = false
  // }
}
const handleConfirmUnit = () => {}
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    auditRoleName: undefined,
    parentId: undefined,
    sort: undefined,
    status: undefined,
    projectPhase: undefined,
    date: undefined,
    content: undefined,
    wbflfgList: [
      {
        name: '1'
      }
    ],
    nbgzzdList: [],
    sjmxList: []
  }
  formRef.value?.resetFields()
}
</script>
<style lang="scss" scoped>
.title {
  font-weight: 900;
  font-size: 16px;
  margin-bottom: 16px;
  margin-top: 16px;
  display: flex;
  align-items: center;
}
</style>
