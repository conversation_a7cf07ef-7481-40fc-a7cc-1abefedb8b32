<template>
  <el-row>
    <el-col :span="24" :xs="24">
      <!-- 查询 -->
      <ContentWrap class="common-card-search">
        <el-form
          class="-mb-15px common-search-form"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="76px"
        >
          <el-form-item label="项目名称" prop="projectName">
            <el-input
              v-model="queryParams.projectName"
              placeholder="请输入项目名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="组织方式" prop="orgType">
            <el-select
              v-model="queryParams.orgType"
              placeholder="请选择组织方式"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="dict in getIntDictOptions('organization_type')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="审计对象" prop="auditObject">
            <el-input
              v-model="queryParams.auditObject"
              placeholder="请输入审计对象"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="审计类型" prop="auditType">
            <el-select
              v-model="queryParams.auditType"
              placeholder="请选择审计类型"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="dict in typeOptions"
                :key="dict.id"
                :label="dict.auditTypeName"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="项目年度" prop="auditYear">
            <el-date-picker
              v-model="queryParams.auditYear"
              type="year"
              value-format="YYYY"
              class="!w-200px"
              placeholder="请选择项目年度"
            />
          </el-form-item>
          <el-form-item label="项目阶段" prop="projStage">
            <el-select
              v-model="queryParams.projStage"
              placeholder="请选择项目阶段"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="dict in getIntDictOptions('proj_parent_project_stage')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="是否境外" prop="overseasFlag">
            <el-select
              v-model="queryParams.overseasFlag"
              placeholder="请选择是否境外"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="dict in getIntDictOptions('common_is_or_status')"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

        </el-form>
		<div class="right-search-btn">
			<el-button  type="primary"  @click="handleQuery">
					<Icon icon="ep:search" class="mr-5px" />搜索
				</el-button>
				<el-button @click="resetQuery">
					<Icon icon="ep:refresh" class="mr-5px" />重置
				</el-button>
		</div>
      </ContentWrap>
      <ContentWrap>
        <el-table border v-loading="loading" :data="list">
          <el-table-column label="#" width="50" align="center">
            <template
              #default="{ $index }"
            >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
          </el-table-column>
          <el-table-column
            label="项目年度"
            align="center"
            prop="auditYear"
            :show-overflow-tooltip="true"
            min-width="88"
          />
          <el-table-column
            label="项目编码"
            align="left"
            prop="projectNo"
            :show-overflow-tooltip="true"
            min-width="180"
          />
          <el-table-column label="项目名称" align="left" min-width="260" :show-overflow-tooltip="true">
            <template #default="{row}">
              <span
                class="click-pointer"
                @click="showProjectDetail(row.id)"
              >{{row.projectName}}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="单位数量" align="center" prop="mobile" min-width="120" /> -->
          <el-table-column label="是否境外" key="overseasFlag" align="center" min-width="88">
            <template #default="scope">
              <dict-tag :type="'common_is_or_status'" :value="scope.row.overseasFlag" />
            </template>
          </el-table-column>
          <el-table-column label="审计类型" align="left" prop="auditTypeDesc" min-width="120" />
          <el-table-column label="审计二级类型" align="left" prop="auditSencTypeDesc" min-width="120" />
          <el-table-column label="组织方式" key="orgType" align="center" min-width='120'>
            <template #default="{row}">
              <span>{{getDictLabel('organization_type',row.orgType)}}</span>
            </template>
          </el-table-column>
           <el-table-column label="审计对象" align="left" prop="auditObject" min-width="240" :show-overflow-tooltip="true"/>
          <el-table-column
            label="时间计划"
            key="timeSchedule"
            align="center"
            prop="timeSchedule"
            min-width="260"
            />

          <el-table-column label="项目进度" align="center" min-width="160">
            <template #default="{row}">
              <el-progress  :percentage="row?.processPercent ?? 0" />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" :width="120" fixed="right" v-if="queryParams.projectId">
            <template #default="scope">
              <el-button type="primary" link @click="handleShowMessage(scope.row.id)">录入</el-button>
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
  </el-row>
  <DetailMessage ref="detailMessageRef" />
  <InterviewEnter ref="mettingEnterRef" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions, getDictLabel } from '@/utils/dict'
import DetailMessage from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/message/DetailMessage.vue'
import InterviewEnter from './InterviewEnter.vue'
import { ProjectInitiationApi } from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectInitiation'
import { AuditTypeApi } from '@/api/basicData/auditType'
import { formatTime } from '@/utils'
defineOptions({ name: 'auditInterview' })
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数
const { query } = useRoute() // 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  projectName: undefined,
  orgType: undefined,
  auditObject: undefined,
  auditType: undefined,
  auditYear: undefined,
  projStage: undefined,
  overseasFlag: undefined,
  projectId: query.id as unknown as number
})
const queryFormRef = ref() // 搜索的表单
const detailMessageRef = ref()
const showProjectDetail = (id: number) => {
  detailMessageRef.value.open(id)
}
const getProgress = (row: any) => {
  if(row.programCount === 0 || row.allProgramCount===0 || isNaN(row.programCount) || isNaN(row.allProgramCount)) {
    return 0
  }else{
    return ((row.programCount/row.allProgramCount) * 100 ).toFixed(2)
  }
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ProjectInitiationApi.getProjPlanProjectList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}
const mettingEnterRef = ref()
const handleShowMessage = (id: number) => {
  mettingEnterRef.value.open(id)
}
const typeOptions = ref([])
/** 初始化 */
onMounted(async () => {
  getList()
  typeOptions.value = await AuditTypeApi.getAuditTypeFirst(0)
})
</script>
 