import request from '@/config/axios'

// 外部法律法规 VO
export interface RegulationsVO {
	id ?: number // 主键，唯一标识
	docNum : string // 文号
	publishMain : string //发布主体
	lawName : string //法律法规名称
	lawStatus : number //法律法规状态 0启用中 1已停用
	effeDate : string //生效日期
	publishDate : string //发布日期
}
export interface RegulationsDetailVO {
	id ?: number // 主键，唯一标识
	docNum : string // 文号
	publishMain : string //发布主体
	lawName : string //法律法规名称
	lawStatus : number //法律法规状态 0启用中 1已停用
	effeDate : string //生效日期
	publishDate : string //发布日期
	adder : string
	createTime : Date
}

// 外部法律法规 API
export const RegulationsApi = {
	// 查询外部法律法规列表分页
	getRegulationsList: async (params : any) => {
		return await request.get({ url: `/audit/tank-ext-law-regulat/page`, params })
	},
	getRegulationsAll: async () => {
		return await request.get({ url: `/audit/tank-ext-law-regulat/getAll` })
	},

	// 查询外部法律法规详情
	getRegulations: async (id : number) => {
		return await request.get({ url: `/audit/tank-ext-law-regulat/get?id=` + id })
	},

	// 新增外部法律法规
	createRegulations: async (data : RegulationsVO) => {
		return await request.post({ url: `/audit/tank-ext-law-regulat/create`, data })
	},

	// 修改外部法律法规
	updateRegulations: async (data : RegulationsVO) => {
		return await request.put({ url: `/audit/tank-ext-law-regulat/update`, data })
	},
	
	// 修改外部法律法规附件
	updateRegulationsFile: async (data : RegulationsVO) => {
		return await request.put({ url: `/audit/tank-ext-law-regulat/update-file`, data })
	},
	

	// 删除外部法律法规
	deleteRegulations: async (id : number) => {
		return await request.delete({ url: `/audit/tank-ext-law-regulat/delete?id=` + id })
	},

	// 导出外部法律法规 Excel
	exportRegulations: async (params : any) => {
		return await request.download({ url: `/audit/tank-ext-law-regulat/export-excel`, params })
	},

	// 获取法律法规分类数据
	ListRegulations: async (num : number | string) => {
		return await request.get({ url: `/audit/tank-trees-type/get?type=` + num })
	}
	
}