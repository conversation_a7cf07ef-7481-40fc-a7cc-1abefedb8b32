/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-14 14:36:51
 * @Description: 审计报告初稿
 */
import request from '@/config/axios'

// 审计报告初稿 VO
export interface AuditProblemsVO {
	id ?: number,
	projectId : string, //项目ID
	questionName : string,// 问题标题
	quesDigest : string,// 问题摘要
	auditSugg : string,// 审计建议
	quesTypeId : number,// 问题类型
	discoverUserId : number,// 发现人id
	abarbeitungFlag : number,// 是否整改
	handOverFlag : number,// 是否移交
}
export interface AuditProblemsDetailVO {
	id: number, 
    projectId: string, //项目ID
    questionCode: number,//问题编号id
    questionName: string,// 问题标题
    quesDigest: string,// 问题摘要
    auditSugg: string,// 审计建议
    quesTypeId: number,// 问题类型id
    quesTypeName: string //问题类型
    discoverUserId: number,// 发现人id
    discoverUserName: string, //发现人
    abarbeitungFlag: number,// 是否整改
    handOverFlag: number,// 是否移交
    createdBy: undefined, //创建人
    createdTime: undefined, //创建时间
    updatedBy: undefined, //更新人
    updatedTime: Date, //更新时间
}


// 审计初稿 API
export const PreliminaryAuditApi = {
	// 查询审计初稿列表分页
	getPreliminaryAuditList: async (params: any) => {
		return await request.get({ url: `/manage/audit/report/page`, params })
	},
	// 新增审计初稿
	getPreliminaryAuditCreate: async (data : AuditProblemsVO) => {
		return await request.post({ url: `/manage/audit/report/create`, data })
	},
	// 获取审计初稿详情
	getPreliminaryAuditDetails: async (id : number) => {
		return await request.get({ url: `/manage/audit/report/show?id=` + id })
	},
    // 获取文书模板
	getText: async (params) => {
		return await request.get({url: `/audit/tank-doc-info/page`, params})
	},
    // 审计提交接口
	getPreliminaryAuditPut: async (data: any) => {
		return await request.post({ url: `/manage/audit/report/submit`, data })
	},
	// 附件上传
	getUpdateAuditPut: async (data) => {
		return await request.post({ url: `/manage/project-assign-atta/create`, data })
	},
	// 获取projectId
	getProjId: async (id : any) => {
		return await request.get({ url: `/manage/audit/report/show/`+id })
	},
	// 审计质量联合控制小组-新增
	getQualityTeamCreate: async (data : any) => {
		return await request.post({ url: `/manage/audit/group/user/add`, data })
	},
	// 审计质量联合控制小组-删除
	getQualityTeamDelete: async (id : any) => {
		return await request.delete({ url: `/manage/audit/group/user/`+id })
	},
	// 审计质量联合控制小组- 列表
	getQualityTeamList: async (params: any) => {
		return await request.get({ url: `/manage/audit/group/user/list`, params })
	},
	// 审计质量联合控制小组-修改
	getQualityTeamEdit: async (data : any) => {
		return await request.put({ url: `/manage/audit/group/user/add`, data })
	},
	// 获取审计资料清单
	getProfileList: async (params) => {
		return await request.get({ url: `/manage/project-assign-atta/page`, params })
	},
	// 获取审计资料清单new
	getProfileListNew: async (params) => {
		return await request.get({ url: `/manage/project-assign-atta/list/by-programcode`, params })
	},
    

	// 修改问题清单详情
	getListOfProblemsUpdate: async (data : AuditProblemsDetailVO) => {
		return await request.put({ url: `/manage/proj-assign-question/update`, data })
	},
	// 删除问题清单
	getListOfProblemsDelete: async (id : number) => {
		return await request.delete({ url: `/manage/proj-assign-question/delete?id=` + id })
	},
    // 数据导入
    getListOfProblemsLead: async (params : any) => {
		return await request.post({ url: `/manage/proj-assign-question/import-excel`,params })
	},
    // 数据导出
    getListOfProblemsDownload: async(params : any) => {
        return await request.get({url: `/manage/proj-assign-question/export-excel`, params})
    },
    // 下载导入模板接口
    getListOfProblemsTemplate: async() => {
        // return await request.download({ url: ``})
    },

    // 问题类型数据
    getListOfProblemsType: async() => {
        // return await request.get({url: `/audit/tank-ques-qualitation/get-all-classification`})
		return await request.get({url: `/audit/tank-trees-type/get?type=5`})
    },

    // 查询问题定性库列表分页(新增时的问题标题)
	getQualitativeTitleList: async (params : any) => {
		return await request.get({ url: `/audit/tank-ques-qualitation/page`, params })
	},

	// 删除审计资料清单
	deleteChatRole: async(id: number) => {
		return await request.delete({url: `/manage/project-assign-atta/delete?id=` + id })
	}
}