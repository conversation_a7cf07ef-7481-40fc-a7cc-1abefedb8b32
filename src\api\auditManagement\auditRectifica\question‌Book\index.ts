/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-10-17 13:31:22
 * @Description: 未整改问题台账
 */
import request from '@/config/axios'

// 记录单 VO
// export interface RectificaNoticeVO {
	
// }
// export interface RectificaNoticeDetailVO {
	
// }

// 未整改问题台账 API
export const NotCorrectedApi = {
	// 内部审计列表
    getNotCorrectedListIn: async(params: any) => {
		return await request.get({ url: `/manage/notice-supervision/indexPage`, params })
	},

	// 外部审计列表
    getNotCorrectedListOut: async(params: any) => {
		return await request.get({ url: `/manage/supervision-out-audit-record/page`, params })
	},

	// 督办详情
	getNotCorrectedInquire: async(data: any) => {
		return await request.get({ url: `/manage/notice-supervision/indexDetail?id=` + data.id})
	},

    // 督办发起
    getNotCorrectedDetails: async(data: any) => {
		return await request.post({ url: `/manage/notice-supervision/batchDetail`, data})
	},

	// 是否发起约谈
	getNotCorrectedChat: async(id: number) => {
		return await request.get({ url: `/manage/notice-supervision/supervisionChat?id=` + id })
	},

	// 录入成果之后流程的详情
	getNotCorrectedaChievement: async(params: any) => {
		return await request.get({ url: `/manage/notice-supervision/detail`, params })
	},

	// 查询整改成果详情
	getNotCorrectedRectificationDetails: async(data: any) => {
		return await request.post({ url: `/manage/notice-supervision/historyDetail`, data})
	},

	// 查询整改成果数组
	getNotCorrectedRectificationList: async(id: number) => {
		return await request.get({ url: `/manage/notice-supervision/historyList?id=` + id,})
	},

    // 审核人提交保存
    getNotCorrectedSubmitForm: async(data: any) => {
		return await request.post({ url: `/manage/notice-supervision/saveFill`, data })
		// return await request.post({ url: `/manage/notice-supervision/saveDetail`, data})
	},
	
	// 联络人分发保存/
	getNotCorrectedDistribute: async(data: any) => {
		return await request.post({ url: `/manage/notice-supervision/saveIssue`, data})
	},

	// 审核人提交复核保存
    getNotrecheckSubmitForm: async(data: any) => {
		return await request.post({ url: `/manage/notice-supervision/saveSupervision`, data })
	},

	// 督办联络人汇总-驳回
	getNotrecheckTurnDown: async(id: number) => {
		return await request.get({ url: `/manage/notice-supervision/rejected?id=` + id })
	},


    // 整改成果录入
    getNotCorrectedaChieve: async(data: any) => {
		// return await request.post({ url: `/manage/rectific-result-entry-detail/create`, data })
		// return await request.post({ url: `/manage/notice-supervision/saveFill`, data })
		return await request.post({ url: `/manage/notice-supervision/saveDetail`, data})
	},

	// 批量发起督办
	getNotrecheckBatch: async(data: any) => {
		return await request.post({ url: ``, data})
	},

	// 外部审计模板下载：公用接口
	getNotrechecksTemplate: async(type: string) => {
        return await request.get({ url: `/infra/file/getTemplate?templateType=${type}` })
    },

	// 外部审计批量导入
	getNotrechecksInto: async(type: string) => {
        return await request.get({ url: `/manage/supervision-out-audit-record/import-excel` })
    },
    
}