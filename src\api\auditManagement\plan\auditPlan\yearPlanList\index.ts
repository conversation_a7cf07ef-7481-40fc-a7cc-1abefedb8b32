import request from '@/config/axios'

//年度计划调整

export interface SearchVo {
    pageNo: number
    pageSize: number
    planName?: string
    planNo?: number
    editDeptId?: number
    creatorName?: string
    planYear?: string
    planStatus?: number
    createTime?: Array<string>
}
export interface fileVo {
    id: number
    fileType: number
    fileName: string
    creatorName: string
    createTime: string
}
export interface YearPlanVo {
    id?: number
    projectName: string
    auditType: number
    projectAttr: number
    investmentAmount: string
    auditScope: string
    orgType: number
    timeScheduleArr: []
    editDeptName: string
    yearPlanProjectRemark: string
}


export interface importDeptFileVo {
    deptPlanId: number
    id: number
    fileType: number
}
export interface SaveVo {
    id?: number
    planYear: ''
    editDeptName: ''
    editDeptId: number
    creatorName: ''
    planNo: number
    planName: ''
    endTime: undefined
    overallRequire: undefined
    editPrinciple: undefined
    mainContent: undefined
    reportRequest: undefined
    saveType: number
    fileList: Array<fileVo>
}

// 年度计划编制api
export const YearPlanListApi = {
    getYearPlanList: async (params: SearchVo) => {
        return await request.get({ url: `/manage/year-plan-report-dept-temp/getPlanNoticePage`, params })
    },
    getDetailById: async (id: number) => {
        return await request.get({ url: `/manage/year-plan-report-dept-temp/get?id=${id}` })

    },
    getProjectListById: async (id: number) => {
        return await request.get({ url: `/manage/year-plan-project-temp/projectList?id=${id}` })
    },
    getFileListById: async (id: number) => {
        return await request.get({ url: `/manage/year-plan-file-temp/checkFileList?deptPlanId=${id}` })
    },
    // 项目清单-批量删除
    batchDeleteProject: async (data: Array<number>) => {
        return await request.delete({ url: `/manage/year-plan-project-temp/delete`, data })
    },
    // 附件上传
    bindFileToDeptPlan: async (data: importDeptFileVo) => {
        return await request.post({ url: `/manage/year-plan-file-temp/bindFileToDeptPlan`, data })
    },
    // 删除附件
    deleteByBusinessIdList: async (id: number) => {
        return await request.delete({ url: `/manage/year-plan-file-temp/deleteByBusinessIdList?id=${id}` })
    },
    //年度计划编制-调整-项目清单-新增
    createProjectList: async (data: YearPlanVo) => {
        return await request.post({ url: `/manage/year-plan-project-temp/create`, data })
    },
    saveOrUpdate: async (data: SaveVo) => {
        return await request.post({ url: `/manage/year-plan-report-dept-temp/saveOrUpdate`, data })
    },
    updateFile: async (data: any) => {
		return await request.upload({ url: `/manage/year-plan-project-temp/import-excel`, data })
	},
    exportExcel: async(params : any) => {
        return await request.download({url: `/manage/year-plan-report-dept-temp/export-excel`, params})
    },

}

