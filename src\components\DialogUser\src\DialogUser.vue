<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="60%" :scroll="true">
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form ref="queryFormRef" :inline="true" :model="queryParams" class="-mb-15px" label-width="68px">
        <el-form-item label="账号" prop="username">
          <el-input v-model="queryParams.username" placeholder="请输入账号" clearable @keyup.enter="handleQuery"
            class="!w-200px" />
        </el-form-item>
        <el-form-item label="姓名" prop="nickname">
          <el-input v-model="queryParams.nickname" placeholder="请输入姓名" clearable @keyup.enter="handleQuery"
            class="!w-200px" />
        </el-form-item>
        <el-form-item label="手机号码" prop="mobile">
          <el-input v-model="queryParams.mobile" placeholder="请输入手机号码" clearable @keyup.enter="handleQuery"
            class="!w-200px" />
        </el-form-item>
        <el-form-item label="所属组织" prop="deptId">
          <el-tree-select v-model="queryParams.deptId" ref="treeRef" clearable placeholder="请选择所属组织" :data="deptList"
            check-strictly :expand-on-click-node="false" :check-on-click-node="true" :default-expand-all="false"
            highlight-current node-key="id" @node-click="handleNodeClick" :load="loadNode" @change="getList"
            :default-expanded-keys="defaultExpandedKeys" :filter-node-method="filterNode" lazy class="!w-200px">
            <template #default="{ data: { name } }">{{ name }}</template>
          </el-tree-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="用户状态" clearable class="!w-200px">
            <el-option v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_STATUS)" :key="dict.value"
              :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery">
            <Icon class="mr-5px" icon="ep:search" />查询
          </el-button>
          <el-button @click="resetQuery">
            <Icon class="mr-5px" icon="ep:refresh" />重置
          </el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>
    <ContentWrap>
      <el-table border v-loading="loading" :data="list" @selection-change="handleSelectionChange">
        <el-table-column label="单选" align="center" width="80" fixed>
          <template #default="{ row }">
            <el-radio v-model="selectedRowId" :disabled="row.useFlag == 0 ? false : true" :value="row.id"
              @change="handleClick(row)" class="custom-radio" />
          </template>
        </el-table-column>
        <el-table-column label="账号" align="center" prop="username" :show-overflow-tooltip="true" />
        <el-table-column label="用户姓名" align="center" prop="nickname" :show-overflow-tooltip="true" />
        <el-table-column label="手机号码" align="center" prop="mobile" :show-overflow-tooltip="true" />
        <el-table-column label="所属部门" align="center" key="deptName" prop="deptName" :show-overflow-tooltip="true" />
        <el-table-column label="用户状态" key="status" align="center" width="100">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.SYSTEM_USER_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNo" :total="total"
        @pagination="getList" />
    </ContentWrap>

    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as UserApi from '@/api/system/user'
import * as DeptApi from '@/api/system/dept'
import { handleLazyTree } from '@/utils/tree'
import type Node from 'element-plus/es/components/tree/src/model/node'
defineOptions({ name: 'DialogUser' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const selectedRowId = ref<number>()
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
interface tableVo {
  id: number
  username: string
  nickname: string
  mobile: number
  status: number
}
const list = ref<tableVo[]>([]) // 列表的数据
const queryParams = ref({
  pageNo: 1,
  pageSize: 10,
  username: undefined,
  nickname: undefined,
  mobile: undefined,
  status: undefined,
  deptId: undefined
})
const queryFormRef = ref() // 搜索的表单
/** 查询角色列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await UserApi.getEmployeePage(queryParams.value)
    list.value = data.list || []
    total.value = data.total || 0
  } finally {
    loading.value = false
  }
}
const handleQuery = () => {
  queryParams.value.pageNo = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}
const personRow = ref<tableVo>()
const handleClick = (row: tableVo) => {
  selectedRowId.value = row.id
  personRow.value = row
}
/** 打开弹窗 */
const open = async (id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('选择')
  resetForm()
  await getTree(0)
  // 修改时，设置数据
  if (id) {
    queryParams.value.deptId = id
    formLoading.value = true
    try {
      getList()
    } finally {
      formLoading.value = false
    }
  } else {
    queryParams.value.deptId = undefined as any
    formLoading.value = true
    try {
      getList()
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
interface AddRoleVo {
  code: string
  name: string
  remark: string
  id: number
}
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const multipleSelection = ref<AddRoleVo[]>([])
const handleSelectionChange = (val: AddRoleVo[]) => {
  multipleSelection.value = val
}
const submitForm = async () => {
  if (!selectedRowId.value) {
    message.warning('请选择用户')
    return
  }
  formLoading.value = true
  try {
    emit('success', personRow.value)
    resetForm()
    selectedRowId.value = undefined
    personRow.value = undefined
    dialogVisible.value = false
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    username: undefined,
    nickname: undefined,
    mobile: undefined,
    status: undefined,
    deptId: undefined
  }
  queryFormRef.value?.resetFields()
}
interface DeptNode {
  id: number
  masterOrgId?: number | string
  name: string
  parentId: number | string
  children?: DeptNode[]
  isLeaf?: boolean
}
const deptList = ref<DeptNode[]>([]) // 树形结构
const defaultExpandedKeys = ref<(string | number)[]>([])
const getTree = async (id) => {
  deptList.value = []
  const res = await DeptApi.getSimpleDeptList(id)
  deptList.value.push(...handleLazyTree(res, 'id', 'parentId'))
  defaultExpandedKeys.value = deptList.value.map((node) => node.id)
}
const handleNodeClick = (node, nodeData) => {
  // 如果是父节点，选中它
  // queryParams.value.deptId = node.id
  // getList()
}
const filterNode = (name: string, data: DeptNode) => {
  if (!name) return true
  return data.name.includes(name)
}
const loadNode = async (node: Node, resolve: (data: any[]) => void) => {
  try {
    const nodeId = node.data.id
    if (nodeId == undefined || nodeId == null) {
      return
    }
    const res = await DeptApi.getSimpleDeptList(nodeId)
    const children = handleLazyTree(res, 'id', 'parentId', 'children')
    resolve(children)
  } catch (error) {
    resolve([])
  }
}

</script>
<style scoped>
.custom-radio .el-radio__label {
  display: none;
  /* 隐藏单选按钮的文本 */
}
</style>