/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-12-09 14:03:45
 * @Description: 审计费 API
 */
import request from '@/config/axios'

// 审计费VO
export interface AuditFeeVO{
	id?: string;
	 /*投资单位 */
     investmentUnit: string;
     /*审定结算数量（项） */
     settlementCount: number;
     /*报审结算总金额（万元） */
     reportedAmount: Record<string, unknown>;
     /*审定结算总金额（万元） */
     approvedAmount: Record<string, unknown>;
     /*累计审减值（万元） */
     reductionValue: Record<string, unknown>;
     /*平均审减率 */
     avgReductionRate: Record<string, unknown>;
     /*甲方应付审计费（元） */
     partyAAuditFee: Record<string, unknown>;
     /*乙方应付审计费（元） */
     partyBAuditFee: Record<string, unknown>;
}
export interface AuditFeeDetailsVO {
	id : number // 主键，唯一标识
    /*工程名称 */
    projectName: string;
    /*工程概况 */
    projectOverview: string;
    /*建设单位 */
    constructionUnit: string;
    /*施工单位 */
    constructionCompany: string;
    /*审计单位 */
    auditUnit: string;
    /*施工单位提交的工程预算或报价金额 */
    constructionCompanyAudit: Record<string, unknown>;
    /*管理单位（代建单位）对施工单位报审值进行初步审核后的金额 */
    managementUnitFirstAudit: Record<string, unknown>;
    /*管理单位（代建单位）审减值（万元） */
    managementAuditReductionValue: Record<string, unknown>;
    /*单项工程的结算审定金额 */
    singleItemSettlementAudit: Record<string, unknown>;
    /*单项审减值 */
    singleItemAuditReduction: Record<string, unknown>;
    /*审减率 */
    auditReductionRate: Record<string, unknown>;
    /*甲方应付审计费 */
    partyAAuditFee: Record<string, unknown>;
    /*乙方应付审计费 */
    partyBAuditFee: Record<string, unknown>;
    /*结算审计发现的主要问题 */
    settlementAuditMainIssues: string;
    /*备注 */
    remarks: string;
}

// 审计费 API
export const AuditFeeApi = {
	// 查询审计费列表分页
	getAuditFeeList: async (params : any) => {
		return await request.get({ url: `/manage/en-settle/get-settlement-audit-fees`, params })
	},

    // 查询审计费合计
	getAuditFeeTotal: async (params : any) => {
		return await request.get({ url: `/manage/en-settle/get-settlement-audit-fees-count`, params })
	},

	// 审计费列表导出 Excel
	exportAuditFee: async (params : any) => {
		return await request.download({ url: `/manage/en-settle/export-settlement-audit-fees-excel`, params })
	},

    // 查询审计费详情
	getAuditFeeDetails: async (params : any) => {
		return await request.get({ url: `/manage/en-settle/get-settlement-audit-fees-detail`, params })
	},

	// 详情列表的合计
	getAuditFeeDetailsTotal: async (params : any) => {
		return await request.get({ url: `/manage/en-settle/get-settlement-audit-fees-detail-count`, params })
	},

	// 审计效率详情 Excel
	exportAuditFeeDetails: async (params : any) => {
		return await request.download({ url: `/manage/en-settle/export-settlement-audit-fees-detail-excel`, params })
	},
}