
<template>
  <div>
    <el-row :gutter="16">
      <el-col :span="24">
        <div class="detail-title common-border-left-blue">
          <span>项目基本信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="项目名称">{{ detailData.projectName }}</el-descriptions-item>
          <el-descriptions-item label="项目编号">{{ detailData.projectNo }}</el-descriptions-item>
          <el-descriptions-item label="审计类型">{{ detailData.auditTypeDesc}}</el-descriptions-item>
          <el-descriptions-item label="立项年度">{{ detailData.projectYear }}</el-descriptions-item>
          <el-descriptions-item label="组织方式">
            <dict-tag :type="DICT_TYPE.ORGANIZATION_TYPE" :value="detailData.orgType" />
          </el-descriptions-item>
          <el-descriptions-item label="时间计划">{{detailData.timeSchedule }}</el-descriptions-item>
          <el-descriptions-item label="审计期间">{{ detailData.auditPeriod}}</el-descriptions-item>
          <el-descriptions-item label="开展事项">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.specialFlag" />
          </el-descriptions-item>
          <el-descriptions-item label="整体报告">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.overallReportFlag" />
          </el-descriptions-item>
          <el-descriptions-item label="公司领导">{{ detailData.companyLeader}}</el-descriptions-item>
          <el-descriptions-item label="曾任职务">{{ detailData.previouWork}}</el-descriptions-item>
          <el-descriptions-item label="发文编号">{{ detailData.docNum}}</el-descriptions-item>
          <el-descriptions-item label="是否境外">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.overseasFlag" />
          </el-descriptions-item>
          <el-descriptions-item label="是否重要">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.significantFlag" />
          </el-descriptions-item>
          <el-descriptions-item label="实施单位">{{ detailData.implementDeptName}}</el-descriptions-item>
          <el-descriptions-item label="立项依据" :span="4">{{ detailData.projectGist}}</el-descriptions-item>
        </el-descriptions>
        <div class="detail-title common-border-left-blue">
          <span>审计对象信息</span>
        </div>
        <el-table
          border
          :data="detailData.auditTargetList"
          :stripe="true"
          :show-overflow-tooltip="true"
        >
          <el-table-column label="#" width="50" align="center">
            <template
              #default="{ $index }"
            >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
          </el-table-column>
          <el-table-column label="项目编号" align="left" prop="projectNo" min-width="180" />
          <el-table-column label="项目名称" align="left" prop="projectName" min-width="260" />
          <el-table-column label="审计对象" align="left" prop="auditTarget" min-width="180" />
          <el-table-column label="组长" align="center" prop="groupLeader" min-width="120" />
          <el-table-column label="项目阶段" key="projStage" align="center" min-width="100">
            <template #default="scope">
              <dict-tag :type="'proj_parent_project_stage'" :value="scope.row.projStage" />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" :width="120" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="showTeamDetail('',scope.row.id)">查看小组信息</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="detail-title common-border-left-blue">
          <span>审计团队信息</span>
        </div>
        <el-table
          border
          :data="detailData.auditGroupList"
          :stripe="true"
          :show-overflow-tooltip="true"
        >
          <el-table-column label="#" type="index" width="50" />
          <el-table-column label="审计角色" align="center" prop="roleName" min-width="120" />
          <el-table-column label="项目成员" align="left" prop="userName" min-width="180" />
          <el-table-column label="身份类型" align="center" prop="identityName" min-width="120" />
          <el-table-column label="审计体系" align="center" prop="auditSystemName" min-width="120" />
          <el-table-column label="审计事项" align="left" prop="auditMatterName" min-width="180" />
        </el-table>
        <div class="mb-10px mt-10px flex_center">
          <div class="flex_center fles_interval title common-border-left-blue">审计取证单</div>
          <!-- <div>
            <el-button type="primary" plain @click="handleImportFile('all')">附件上传</el-button>
          </div>-->
        </div>
        <el-table border :data="linkList" :stripe="true" :show-overflow-tooltip="true">
          <el-table-column label="#" type="index" width="50" />
          <el-table-column label="审计取证单名称" align="left" :width="200">
            <template #default="{row}">
              <span
                class="click-pointer"
                @click="showList([{
                id: row.id
              }])"
              >{{row.projectName}}</span>
            </template>
          </el-table-column>
          <el-table-column label="编制人" align="center" prop="auditUserName" min-width="100" />
          <el-table-column label="资料清单" align="left" prop="materialName" min-width="200" />
          <el-table-column label="资料类型" align="center" prop="resourceName" />
          <el-table-column label="上传人" align="center" prop="uploadUser" />
          <el-table-column label="上传时间" align="center" prop="uploadTime" min-width="120">
            <template #default="{row}">
              <span>{{ formatTime(row.uploadTime, 'yyyy-MM-dd') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" :width="200" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="handleDownload(scope.row?.fileUrl,scope.row?.fileName)"
              >下载</el-button>
              <el-button
                type="primary"
                link
                @click="handleShowMessage(scope.row.fileName, 'view', scope.row.fileId)"
              >预览</el-button>
              <el-button
                type="primary"
                link
                v-if="isApprove"
                @click="handleShowMessage(scope.row.fileName, 'EDIT', scope.row.fileId)"
              >编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <!-- <el-row>
      <el-col :span="24" class="text-right mt-8px">
        <el-button @click="submitForm(0)" type="primary" :disabled="formLoading">保存</el-button>
      </el-col>
    </el-row>-->
  </div>
  <TeamDetail ref="teamDetailRef" />
  <DialogFlie ref="dialogFlieRef" />
  <FileForm
    ref="formImgRef"
    @success="handleUploadSuccess"
    :type="fileType"
    :limit="fileLimit"
    :showRadioByInterface="true"
    :radioListByInterface="fileTypeList"
  />
  <SheetList ref="sheetListRef" />
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { AuditRoleApi, AuditRoleVO } from '@/api/basicData/auditRole'
import { DialogFlie } from '@/components/DialogFlie'
import FileForm from '@/views/infra/file/FileForm.vue'
import SheetList from './SheetList.vue'
import TeamDetail from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/TeamDetail.vue'
import { ProjectDataListApi } from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectDataList'
import { formatTime } from '@/utils'
import { CommonApi } from '@/api/common'
import download from '@/utils/download'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { ProjectInitiationApi } from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectInitiation'
import { AuditEvidenceSheetApi } from '@/api/auditManagement/projectAssignment/auditImplem/auditEvidenceSheet'
import { link } from 'fs'
import { propTypes } from '@/utils/propTypes'
const { wsCache } = useCache()
defineOptions({ name: 'BatchSubmit' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const detailData = ref({
  id: 0,
  auditRoleName: undefined,
  parentId: undefined,
  sort: undefined,
  status: undefined,
  projectPhase: undefined,
  date: undefined,
  content: undefined,
  ids: []
})
const { query } = useRoute() // 查询参数
const queryId = query.id as unknown as number
const props = defineProps({
  id: propTypes.number.def(undefined),
  formVariables: {
    default: () => ({}),
    type: Object
  },
  isApprove: propTypes.bool.def(true)
})
const list = ref()
const formRules = reactive({
  auditRoleName: [{ required: true, message: '不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  parentId: [{ required: true, message: '父级编号不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

const teamDetailRef = ref()
const showTeamDetail = (parId?: number | string, sonId?: number | string) => {
  teamDetailRef.value.open(parId, sonId)
}
const formImgRef = ref()
const fileType = ref('img')
const fileLimit = ref(1)
// 上传方法
const handleImport = (type: string) => {
  fileType.value = type
  fileLimit.value = type === 'file' ? 5 : 1
  formImgRef.value.open()
}
const formData = ref()
const handleUploadSuccess = async (fileList, radioType) => {
  let fileArr =
    fileList && fileList.length > 0
      ? fileList.map((item) => {
          return {
            // url: item.response.data,
            fileName: item.name,
            fileTypeName: fileMap.get(radioType),
            fileType: radioType,
            fileUrl: item.response.data.fileUrl,
            fileId: item.response.data.id,
            deptId: wsCache.get(CACHE_KEY.USER).user.deptId,
            deptName: wsCache.get(CACHE_KEY.USER).user.deptName,
            creatorName: item.response.data.creatorName,
            createTime: item.response.data.createTime,
            projectId: detailData.value.id
            // mainId: formData.value.auditNoticeRespVO.id
          }
        })
      : []
  await CommonApi.createCommon(fileArr[0])
  await getInfo()
  // formData.value.projectAttaVOList = formData.value.projectAttaVOList.concat(...fileArr)
}
const handleDelete = async (type: string, index: number = 0) => {
  await message.delConfirm()
  formData.value[type].splice(index, 1)
  await message.success(t('common.delSuccess'))
}
interface fileType {
  attTypeCode: string
  label: string
  value: string
  attTypeName: string
}
const fileTypeList = ref<fileType[]>([])
const fileMap = new Map<string, string>()
const getInfo = async () => {
  detailData.value = await ProjectDataListApi.getProjectInitiationDetail(detailData.value.id, 'Q')
}
const linkList = ref([])
const getLinkList = async () => {
  let data = {
    ids: detailData.value.ids
  }
  linkList.value = await AuditEvidenceSheetApi.getEvidenceDetailList(data)
}
const sheetListRef = ref()
const showList = (ids) => {
  sheetListRef.value.open(ids)
}
/** 打开弹窗 */
const open = async () => {
  detailData.value.id = props.id || queryId
  resetForm()
  // 修改时，设置数据
  formLoading.value = true
  try {
    await getInfo()
    fileTypeList.value = await ProjectInitiationApi.getFileType('Q')
    fileTypeList.value.forEach((item) => {
      item.label = item.attTypeName
      item.value = item.attTypeCode
      fileMap.set(item.attTypeCode, item.label)
    })
    detailData.value.ids = props.formVariables?.ids ?? []
    await getLinkList()
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
const handleDownload = (url: string, name: string) => {
  download.downFileByFileUrl(url, name)
}
onMounted(async () => {
  await open()
})

/** 提交表单 */
const emit = defineEmits(['success', 'handleWatchStatus']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async (type: number) => {
  if (type === 0) {
    dialogVisible.value = false
    message.success('保存成功')
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = {
      ids: detailData.value.ids,
      projectId: detailData.value.id
    }
    await AuditEvidenceSheetApi.batchSubmit(data)
    message.success('提交成功')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
const handleConfirmUnit = () => {}
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    auditRoleName: undefined,
    parentId: undefined,
    sort: undefined,
    status: undefined,
    projectPhase: undefined,
    date: undefined,
    content: undefined,
    wbflfgList: [
      {
        name: '1'
      }
    ],
    nbgzzdList: [],
    sjmxList: []
  }
  formRef.value?.resetFields()
}
const dialogFlieRef = ref()
const handleShowMessage = (title: string, flietype: string, id: number) => {
  dialogFlieRef.value.open(title, flietype, id)
}
const handleImportFile = (type: string) => {
  formImgRef.value.open()
  fileType.value = type
}
</script>
<style lang="scss" scoped>
.title {
  font-weight: 900;
  font-size: 16px;
  margin-bottom: 16px;
  margin-top: 16px;
  display: flex;
  align-items: center;
}
:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
  width: 80px;
}
</style>
