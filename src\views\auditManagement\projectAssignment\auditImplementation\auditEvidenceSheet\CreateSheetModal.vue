
<template>
  <Dialog
    :title="dialogTitle"
    :scroll="true"
    v-model="dialogVisible"
    width="85%"
    maxHeight="72vh"
    setScrollTop="2vh"
    :loading="formLoading"
  >
    <el-row :gutter="16">
      <el-col :span="24">
        <div class="detail-title common-border-left-blue">
          <span>项目基本信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="项目名称">{{ detailData.projectName }}</el-descriptions-item>
          <el-descriptions-item label="项目编号">{{ detailData.projectNo }}</el-descriptions-item>
          <el-descriptions-item label="审计类型">{{ detailData.auditTypeDesc}}</el-descriptions-item>
          <el-descriptions-item label="立项年度">{{ detailData.projectYear }}</el-descriptions-item>
          <el-descriptions-item label="组织方式">
            <dict-tag :type="DICT_TYPE.ORGANIZATION_TYPE" :value="detailData.orgType" />
          </el-descriptions-item>
          <el-descriptions-item label="时间计划">{{detailData.timeSchedule }}</el-descriptions-item>
          <el-descriptions-item label="审计期间">{{ detailData.auditPeriod}}</el-descriptions-item>
          <el-descriptions-item label="开展事项">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.specialFlag" />
          </el-descriptions-item>
          <el-descriptions-item label="整体报告">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.overallReportFlag" />
          </el-descriptions-item> 
          <el-descriptions-item label="公司领导">{{ detailData.companyLeader}}</el-descriptions-item>
          <el-descriptions-item label="曾任职务">{{ detailData.previouWork}}</el-descriptions-item>
          <el-descriptions-item label="发文编号">{{ detailData.docNum}}</el-descriptions-item>
          <el-descriptions-item label="是否境外">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.overseasFlag" />
          </el-descriptions-item>
          <el-descriptions-item label="是否重要">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.significantFlag" />
          </el-descriptions-item>
          <el-descriptions-item label="实施单位">{{ detailData.implementDeptName}}</el-descriptions-item>
          <el-descriptions-item label="立项依据" :span="4">{{ detailData.projectGist}}</el-descriptions-item>
        </el-descriptions>
        <!-- <div class="detail-title common-border-left-blue">
          <span>审计对象信息</span>
        </div> -->
        <!-- <el-row>
          <el-col :span="24" class="text-right">
            <el-button type="primary" plain @click="showTeamDetail">查看小组信息</el-button>
          </el-col>
        </el-row>-->
        <!-- <el-table
          border
          :data="detailData.auditTargetList"
          :stripe="true"
          :show-overflow-tooltip="true"
        >
          <el-table-column label="#" width="50" align="center">
            <template
              #default="{ $index }"
            >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
          </el-table-column>
          <el-table-column label="项目编号" align="left" prop="projectNo" min-width="180" />
          <el-table-column label="项目名称" align="left" prop="projectName" min-width="260" />
          <el-table-column label="审计对象" align="left" prop="auditTarget" min-width="180" />
          <el-table-column label="组长" align="center" prop="groupLeader" min-width="120" />
          <el-table-column label="项目阶段" key="projStage" align="center" min-width="100">
            <template #default="scope">
              <dict-tag :type="'proj_parent_project_stage'" :value="scope.row.projStage" />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" :width="120" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="showTeamDetail('',scope.row.id)">查看小组信息</el-button>
            </template>
          </el-table-column>
        </el-table> -->

        <div class="detail-title common-border-left-blue">
          <span>审计事项信息</span>
        </div>
        <el-table :data="auditGroupList" :stripe="true" :show-overflow-tooltip="true">
          <el-table-column label="#" width="50" align="center">
            <template
              #default="{ $index }"
            >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
          </el-table-column>
          <el-table-column label="审计角色" align="center" prop="roleName" min-width="120" />
          <el-table-column label="项目成员" align="center" prop="userName" min-width="120" />
          <el-table-column label="身份类型" align="center" prop="identityName" min-width="120" />
          <el-table-column label="审计体系" align="center" prop="auditSystemName"  min-width="120"/>
          <el-table-column label="审计事项" align="left" prop="auditMatterName" min-width="180" />
          <el-table-column label="操作" align="center" :width="120" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="handleShowForm(scope.row)">生成取证单</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
    </template>
  </Dialog>

  <TeamDetail ref="teamDetailRef" />
  <EvidenceForm ref="evidenceFormRef" @success="getListDetail" />
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { AuditRoleApi, AuditRoleVO } from '@/api/basicData/auditRole'
import FileForm from '@/views/infra/file/FileForm.vue'

import { formatTime } from '@/utils'
import TeamDetail from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/TeamDetail.vue'
import EvidenceForm from './EvidenceForm.vue'
import { ProjectDataListApi } from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectDataList'
defineOptions({ name: 'SubmitModal' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const detailData = ref({
  id: undefined,
  projectName: undefined,
  projectNo: undefined,
  auditTypeDesc: undefined,
  projectYear: undefined,
  orgType: undefined,
  timeSchedule: undefined,
  auditPeriod: undefined,
  specialFlag: undefined,
  overallReportFlag: undefined,
  companyLeader: undefined,
  previouWork: undefined,
  docNum: undefined,
  overseasFlag: undefined,
  significantFlag: undefined,
  implementDeptName: undefined,
  projectGist: undefined,
  auditTargetList: [],
  projAuditMatterList: []
})
const list = ref([{}])

const formRef = ref() // 表单 Ref

const teamDetailRef = ref()
const showTeamDetail = (parId?: number | string, sonId?: number | string) => {
  teamDetailRef.value.open(parId, sonId)
}
const activeName = ref('0')
const handleClick = () => {}
const planFillingStatus = ref()
const showPlanStatus = () => {
  planFillingStatus.value.open('detail', -1)
}
const evidenceFormRef = ref()
const handleShowForm = (row: any) => {
  evidenceFormRef.value.open(detailData.value, row)
}
const formImgRef = ref()
const fileType = ref('img')
const fileLimit = ref(1)
// 上传方法

const props = defineProps({
  mainId: {
    default: null,
    type: Number
  }
})
/** 打开弹窗 */
const idData = ref()
const open = async (id?: number) => {
  idData.value = id
  dialogVisible.value = true
  dialogTitle.value = t('新增')
  await getDetil()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
const auditGroupList = ref([])
// 修改时，设置数据
const getDetil = async () => {
  if (idData.value) {
    formLoading.value = true
    try {
      detailData.value = await ProjectDataListApi.getProjectInitiationDetail(idData.value, 'Q')
      auditGroupList.value = await ProjectDataListApi.getAuditGroupList(idData.value)
    } finally {
      formLoading.value = false
    }
  }
}
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
// 生成取证单之后调用列表刷新数据
const getListDetail = () => {
  console.log(':)))00000')
  emit('success')
}

/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = detailData.value as unknown as AuditRoleVO
    if (formType.value === 'create') {
      await AuditRoleApi.createAuditRole(data)
      message.success(t('common.createSuccess'))
    } else {
      await AuditRoleApi.updateAuditRole(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
</script>
<style lang="scss" scoped>
.title {
  font-weight: 900;
  font-size: 16px;
  margin-bottom: 16px;
  margin-top: 16px;
  display: flex;
  align-items: center;
}
:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
  width: 80px;
}
</style>
