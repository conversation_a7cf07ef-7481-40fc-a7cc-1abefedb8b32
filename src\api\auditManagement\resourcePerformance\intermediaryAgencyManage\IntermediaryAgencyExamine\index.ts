import request from '@/config/axios'

export interface saveEvaluationVo {
    evaluationYear: string | undefined
    evaluationMonth: string | undefined
    serviceProjectNum: number | undefined

    totalScore: number
    mattersList: Array<ScoreVo>
}
export interface saveHalfYearEvaluationVo {
    avgScore: number
    serviceNum: number
    monthDetailList: []
    creditRating: string
    evaluationYear: string | Date
    monthEvaluationScore?: number,
    monthEvaluationWeight?: number,
    monthEvaluationFinalScore?: number,
    yearDebriefingScore?: number,
    yearDebriefingWeight?: number,
    yearDebriefingFinalScore?: number,
    totalScore?: number
}
export interface ScoreVo {
    parentName: string
    rowSpan?: number
    mattersName: string
    evaluationMatters: string
    matterScore: number
}
export interface searchEvaluationListVo {
    orgName: string | undefined
    resourceOrgId: number | undefined
    // startDate: string| undefined
    // endDate: string| undefined
    timeDuration: []
}
interface searchVo {
    pageNo?: number
    pageSize?: number
    resourceOrgId: number | undefined
    evaluationYear: string
}

// 中介机构考核管理 API 
export const AgencyExamineApi = {
    //中介机构考核管理-查看低分图标
    getLowScoreInfo: async (id: number) => {
        return await request.get({ url: `/manage/org-evaluation-month-detail/lowScoreInfo?id=${id}` })
    },
    // 拉入黑名单
    setInBlack: async (id: number) => {
        return await request.get({ url: `/manage/org-manage/black?id=${id}` })
    },
    // 中介机构考核管理-月度评价-查询评价项目
    getMonthMatters: async (id: number) => {
        return await request.get({ url: `/manage/org-evaluation-month-matters/getMonthMatters?id=${id}` })
    },
    //机构id开始结束月份查询月度评价列表
    getHalfYearMatters: async (data: searchEvaluationListVo) => {
        return await request.post({ url: `/manage/org-evaluation-month-detail/selectMonthEvaluation`, data })
    },
    //  根据机构id获取年度评价列表
    getYearMatters: async (id: number) => {
        return await request.get({ url: `/manage/org-evaluation-month-matters/getMonthMatters?id=${id}` })
    },
    // 中介机构考核管理-月度评价-保存评价
    saveEvaluation: async (data: saveEvaluationVo) => {
        return await request.post({ url: `/manage/org-evaluation-month-matters/evaluation`, data })
    },
    // 中介机构考核管理-半年评价-保存评价
    saveHalfYearEvaluation: async (data: saveHalfYearEvaluationVo) => {
        return await request.post({ url: `/manage/org-evaluation-half-year/evaluation`, data })
    },
    // 中介机构考核管理-年度评价-保存评价
    saveYearEvaluation: async (data: saveHalfYearEvaluationVo) => {
        return await request.post({ url: `/manage/org-evaluation-year/evaluation`, data })
    },
    // 总评价--获取月度评价
    getMonthEvalutionList: async (params: searchVo) => {
        return await request.get({ url: `/manage/org-evaluation-month/list`, params })
    },
    // 总评价--获取半年评价
    getHalfYearEvalutionList: async (params: searchVo) => {
        return await request.get({ url: `/manage/org-evaluation-half-year/list`, params })
    },
    // 总评价--获取年度评价
    getYearEvalutionList: async (params: searchVo) => {
        return await request.get({ url: `/manage/org-evaluation-year/list`, params })
    },
    // 中介机构考核查询列表
    getEvalutionList: async (params: searchVo) => {
        return await request.get({ url: `/manage/org-evaluation-year/assessmentPage`, params })
    },
}