import request from '@/config/axios'

// 招标控制价 
export interface BiddingControlPriceVO {
	/*ID;ID */
	id: string;
	/*项目ID */
	epsprojid: string;
	/*项目名称 */
	projectName: string;
	/*工程名称 */
	name: string;
	/*投资单位 */
	proOwnersName: string;
	/*管理单位 */
	company: string;
	/*工程概况 */
	xmgk: string;
	/*中介机构名称 */
	zjjgname: string;
	/*中介机构ID */
	zjjgid: string;
	/*系统向中介机构发出新任务时间（报审日期） */
	sjbsenddate: Record<string, unknown>;
	/*中介机构领取审计定案表时间 */
	sjbsendhdbdate: Record<string, unknown>;
	/*审计报告送达时间 */
	sjbgQrsj: Record<string, unknown>;
	/*中介机构出具审计报告天数(纸版审计报告耗（天）) */
	zjjgSjbgDays: number;
	/*中介机构出具初审意见天数(初审意见天数（天）) */
	zjjgCsyjDays: number;
	/*审计定案表领取至审计报告送达间隔天数(审计定案表（天）) */
	zjjgHdbsjbgDays: number;
	/*施工单位原报结算值(控制价报审值（元）) */
	settlementamount: number;
	/*中介机构最终审定结算值(控制价审定值（元）) */
	jtsjJsmoney: number;
	/*中介机构审减值(审减值（元）（元）) */
	zjjgSjzj: number;
	/*审增值（元） */
	zjjgSjzjD: number;
	/*审计调整率 */
	sjtzl: number;
	/*审计费（元） */
	zjjgSjfy: number;
	/*编制单位（控制价编制单位） */
	bzdw: string;
}

// 招标控制价-审计效率 VO
export interface AuditEfficiencysVO {
	/*中介机构名称(审计单位) */
	zjjgname: string;
	/*中介机构ID */
	zjjgid: string;
	/*委托控制审计数量 */
	trustAuditNum: number;
	/*委托控制审计数量 */
	auditNum: number;
	/*审计完成率 */
	auditFinishRate: string;
	/*纸版审计报告耗时(天) */
	zjjgSjbgDays: number;
	/*初审平均耗时(天) */
	zjjgCsyjDays: number;
	/*最终平均耗时(天) */
	zjjgHdbsjbgDays: number;
}

// 招标控制价-审计费 VO
export interface AuditFeesVO {
	/*中介机构ID */
	zjjgid: string;
	/*中介机构名称(审计单位) */
	zjjgname: string;
	/*审定控制价数量 */
	auditNum: number;
	/*审定控制价总金额 */
	auditFeeTotal: number;
	/*报审控制价总金额 */
	settleMenAmountTotal: number;
	/*审计费（元） */
	zjjgSjfyTotal: number;
	/* */
	sjtzltotal: string;
}

// 招标控制价-审减原因 VO
export interface ReasonsVO {
	/*控制价审计发现问题 */
	question: string;
	/*出现该问题的工程招标控制价数量 */
	count: number;
}

// 招标控制价 API
export const BiddingControlPriceApi = {
	// 获得工程-控制-工程分页
	getBiddingControlPriceList: async (params : any) => {
		return await request.get({ url: `/manage/control/engineering/page`, params })
	},
	// 获得工程-控制-审计效率分页
	getAuditEfficiencysList: async (params : any) => {
		return await request.get({ url: `/manage/control/page`, params })
	},
	// 工程-控制-审计效率-导出
	exportAuditEfficiencys: async (params : any) => {
		return await request.download({ url: `/manage/control/export-efficiency`, params })
	},
	// 获得工程-控制-审计费用分页
	getAuditFeesList: async (params : any) => {
		return await request.get({ url: `/manage/control/fee/page`, params })
	},
	// 工程-控制-审计费用-导出
	exportAuditFees: async (params : any) => {
		return await request.download({ url: `/manage/control/export-fee`, params })
	},
	// 获得工程-控制-审减原因分页
	getReasonsList: async (params : any) => {
		return await request.get({ url: `/manage/control/question/page`, params })
	},
	// 工程-控制-审减原因导出
	exportReasons: async (params : any) => {
		return await request.download({ url: `/manage/control/export-question`, params })
	}
}