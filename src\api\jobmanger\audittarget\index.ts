import request from '@/config/axios'

// 对象库 VO
export interface ObjectVO {
	id ?: number // 主键，唯一标识
	parentCompanyId: string, //公司父级
	companyName : string // 公司名称
	companyId : string //公司名称id
	legalPerson : string //公司法人
	companyType : number //公司类别id
	comNature : string //公司性质id
	registerCapital : string //注册资金(万元）
	address: string//注册地址
	registerTime: string//注册日期
	linkTel: string//联系电话
	chairmanId: number//董事长id
	chairmanName: string//董事长名称
	managerId: number//总经理id
	managerName: string//总经理名称
	leaders: string//公司领导
	basicInfo: string//基本情况
	licenseScope: string//营业执照范围
	mainBusi: string//主要开展业务情况
	strategy: string//战略定位
	mainResp: string//主责主业
	masteryNew: string//专精特新
	tankObjLeaderHisDOList: [], //历任领导
	tankObjFinancialSumDOList: [], //财务摘要
	tankObjectFileRespVOList: [], //附件说明
}
export interface ObjectDetailVO {
	id: number // 主键，唯一标识
	parentCompanyId: string, //公司父级
	companyName : string // 公司名称
	companyId : string //公司名称id
	legalPerson : string //公司法人
	companyType : number //公司类别id
	comNature : string //公司性质id
	registerCapital : string //注册资金(万元）
	address: string//注册地址
	registerTime: string//注册日期
	linkTel: string//联系电话
	chairmanId: number//董事长id
	chairmanName: string//董事长名称
	managerId: number//总经理id
	managerName: string//总经理名称
	leaders: string//公司领导
	basicInfo: string//基本情况
	licenseScope: string//营业执照范围
	mainBusi: string//主要开展业务情况
	strategy: string//战略定位
	mainResp: string//主责主业
	masteryNew: string//专精特新
	createdTime: Date, //创建时间
	updatedTime: Date, //更新时间
	tankObjLeaderHisDOList: [], //历任领导
	tankObjFinancialSumDOList: [], //财务摘要
	tankObjectFileRespVOList: [], //附件说明
}

// 对象库 API
export const ObjectbaseApi = {
	// 查询部门树结构（根据当前登陆人）
	getTreeUeser:async (id: number) => {
		return await request.get({ url: `/system/dept/get-dept-by-userid?id=` + id })
	},

	// 查询案例库列表分页
	getCasebaseList: async (params : any) => {
		return await request.get({ url: `/audit/tank-object/page`, params })
	},
	// // 获得所有模版
	// getSelectionList: async (params : any) => {
	// 	return await request.get({ url: `/audit/tank-programmes/page`, params })
	// },

	// 查询对象库详情
	getCasebase: async (id : number) => {
		return await request.get({ url: `/audit/tank-object/get?id=` + id })
	},

	// 新增对象库
	createObjectbase: async (data : ObjectVO) => {
		return await request.post({ url: `/audit/tank-object/create`, data })
	},

	// 修改对象库
	updateObjectbase: async (data : ObjectDetailVO) => {
		return await request.put({ url: `/audit/tank-object/update`, data })
	},

	// 删除对象库
	deleteObjectbase: async (id : number) => {
		return await request.delete({ url: `/audit/tank-object/delete?id=` + id })
	},

	// 导出对象库 Excel
	exportObjectbase: async (params : any) => {
		return await request.download({ url: `/audit/tank-object/export-excel`, params })
	},

	// 查询案例库列表分页
	getItemList: async (params : any) => {
		return await request.get({ url: `/manage/proj-plan-project/auditProjPage`, params })
	},

	// 审计对象关联项目的历史审计信息
	getItemDetail: async(id: number) =>{
		return await request.get({ url: `/audit/tank-object/getHis?projId=` + id})
	},
	// 获取当前登录部门
	getDeptByUserid: async() =>{
		return await request.get({ url: `/system/dept/get-dept-by-userid`})
	},
}
