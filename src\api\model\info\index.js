import request from '@/config/axios'


// 获取模型分页
export function getModelInfoPage(queryParams) {
  return request.post({
    url: '/model/modelInfo/modelAbstractInfoData',
    data: queryParams
  })
}


// 保存模型
export function saveModelInfo(data) {
  return request.post({
    url: '/model/modelInfo/saveModelInfoData',
    data: data
  })
}


// 删除
export function deleteModelInfo(id) {
  return request.post({
    url: '/model/modelInfo/deleteModelInfoData/' + id,
  })
}

//
// 获de
export function getModelInfo(id) {
  return request.post({
    url: '/model/modelInfo/modelInfoData/' + id,
  })
}

// 获取可被引用的SQL模型
export function getSqlModelInfoPage(data) {
  return request.post({
    url: '/model/modelInfo/canBeQuotedSqlModels',
    data: data
  })
}

//保存SQL模型引用记录
export function saveQuoteRecord(id) {
  return request.post({
    url: '/model/modelInfo/saveSqlModelQuoteRecord/' + id,
  })
}


// sql执行
export function executeSqlModel(data) {
  return request.post({
    url: '/model/modelInfo/sqlExecuteResult',
    data: data
  })
}

// 列表页查询sql结果
export function getSqlExecuteResult(id,params) {
  return request.get({
    url: '/model/modelInfo/sqlModelQueryResult/' + id,
    params: params
  })
}

// 导出sql模型数据
export function exportSqlModelData(id) {
  return request.download({
    url: '/model/modelInfo/exportSqlModelQueryResults/' + id
  })
}

// 获取单位下拉列表
export function getCompanyList(id) {
  return request.get({
    url: '/system/dept/simple-list?id=' + id,
  })
}


// 模型执行配置保存
export function saveModelExecuteConfig(data) {
  return request.post({
    url: '/model/modelInfo/executeModelProcedure',
    data: data
  })
}

/**
 * 保存模型使用记录
 * @param id
 * @returns {*}
 */
export function saveModelUseRecord(id) {
  return request.post({
    url: `/model/modelInfo/saveModelUseRecord/${id}`
  })
}

/**
 * 获取全部已发布模型
 * @returns {Promise<any>}
 */
export function allPublishedModels() {
  return request.post({
    url: '/model/modelInfo/allPublishedModels'
  })
}

