import request from '@/config/axios'

// 看板-数字 VO
export interface TopNumVO {
	/*项目总数 */
    projectNum: number;
    /*执行中项目总数 */
    runProjectNum: number;
	runProjectNumRate: number;
    /*已完成项目总数 */
    finishProjectNum: number;
	finishProjectNumRate: number;
    /*已超期项目总数 */
    timeOutProjectNum: number;
	timeOutProjectNumRate: number;
    /*已归档项目总数 */
    archivedProjectNum: number;
	archivedProjectNumRate: number;
}

// 项目类型分布 VO
export interface TypeNumVO {
	/* */
    typeId: number;
    /*类型名称 */
    typeName: string;
    /*类型对应数量 */
    num: number;
}

// 组织方式分布 VO
export interface OrgNumVO {
	/*类型ID */
	typeId: number;
	/*类型对应数量 */
	num: number;
}

// 项目状态分布 VO
export interface StateNumVO {
	/*状态 */
	state: number;
	/*类型对应数量 */
	num: number;
	/*占比 */
	rate: number;
}

// 现场审计文档统计 VO
export interface WordNumVO {
	/*审计报告数量 */
	reportWordNum: number;
	/*审计工作底稿数量 */
	baseWordNum: number;
	/*审计方案数量 */
	schemaNum: number;
	/*审计取证单的数量 */
	certNum: number;
}

// 项目看板 API
export const ItemApi = {
	// 看板-数字
	getTopNum: async (params : any) => {
		return await request.get({ url: `/manage/graph/project/top-num`, params })
	},
	// 项目类型分布
	getTypeNum: async (params : any) => {
		return await request.get({ url: `/manage/graph/project/type-num`, params })
	},
	// 组织方式分布
	getOrgNum: async (params : any) => {
		return await request.get({ url: `/manage/graph/project/org-num`, params })
	},
	// 项目状态分布
	getStateNum: async (params : any) => {
		return await request.get({ url: `/manage/graph/project/state-num`, params })
	},
	// 现场审计文档统计
	getWordNum: async (params : any) => {
		return await request.get({ url: `/manage/graph/project/word-num`, params })
	},
}