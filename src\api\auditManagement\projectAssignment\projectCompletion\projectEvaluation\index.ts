import request from '@/config/axios'
export interface serrchVo {
    pageNo: number
    pageSize: number
    projectName?: string
    orgType?: number
    auditObject?: number
    auditType?: number
    auditYear?: string
    projStage?: number
    overseasFlag?: number
}
export interface saveVO {
    evalUserIdList: []
    evalTime: string
    saveType: number
    bodyList: []
}
// 项目评价
export const ProjectEvaluationApi = {
    // 项目评价-弹窗查询
    getProjectEvaluationList: async (params: serrchVo) => {
        return await request.post({ url: `/manage/end-person-evaluate-score/projectEvaluation`, params })
    },
    getProjectEvaluationDetail: async (id: number) => {
        return await request.get({ url: `/manage/end-person-evaluate-score/projectEvaluationDetail?id=${id}` })
    },
    saveEval: async (data: saveVO) => {
        return await request.post({ url: `/manage/end-person-evaluate-score/saveEval`, data })
    },

}