import request from '@/config/axios'

// 事项库 VO
export interface ItemLibraryVO {
	id ?: number // 主键，唯一标识
	/* */
	matterName : string;
	/* */
	matterCode : string;
	/* */
	matterType : number;
	/* */
	riskLevelId : string;
	/* */
	riskLevelName : string;
	/* */
	auditAccord : string;
	/* */
	mattersName : string;
	/* */
	auditForm : string;
	/* */
	mattersDesc : string;
	/* */
	controlMeasures : string;
	/* */
	stepMethod : string;
	/* */
	mainQues : string;
	/* */
	auditSugg : string;
	status: number;
	lawsAndRegulations : Record<string, unknown>[];
	internalRegulations : Record<string, unknown>[];
	auditData : Record<string, unknown>[];
	auditModel : Record<string, unknown>[];
}
export interface ItemLibraryDetailVO {
	id ?: number // 主键，唯一标识
	/* */
	matterName : string;
	/* */
	matterCode : string;
	/* */
	matterType : number;
	/* */
	riskLevelId : string;
	/* */
	riskLevelName : string;
	/* */
	auditAccord : string;
	/* */
	mattersName : string;
	/* */
	auditForm : string;
	/* */
	mattersDesc : string;
	/* */
	controlMeasures : string;
	/* */
	stepMethod : string;
	/* */
	mainQues : string;
	/* */
	auditSugg : string;
	status: number;
	lawsAndRegulations : Record<string, unknown>[];
	internalRegulations : Record<string, unknown>[];
	auditData : Record<string, unknown>[];
	auditModel : Record<string, unknown>[];
}

// 事项库 API
export const ItemLibraryApi = {
	// 查询事项库列表分页
	getItemLibraryList: async (params : any) => {
		return await request.get({ url: `/audit/tank-matters-info/page`, params })
	},
	// 根据审计类型获得审计事项库分页数据
	getItemLibraryTypeList: async (params : any) => {
		return await request.get({ url: `/audit/tank-matters-info/page-by-audit-type`, params })
	},

	// 获得所有事项
	getMatterslist: async () => {
		// return await request.get({ url: `/audit/tank-matters-info/get-matterslist` })
		return await request.get({ url: `/audit/tank-trees-type/get?type=4` })
	},

	// 修改事项状态
	changeItemLibraryList: async (params : any) => {
		return await request.get({ url: `/audit/tank-matters-info/modify-matter-status`, params })
	},

	// 添加审计事项和审计分类关联关系
	addItemLibrary: async (data : ItemLibraryVO) => {
		return await request.post({ url: `/audit/tank-matters-info/add-audit-classification`, data })
	},

	// 根据事项ID获得风险点
	getRiskPoints: async (id : number) => {
		return await request.get({ url: `/audit/tank-matters-info/get-risk-points?matterId=` + id })
	},

	// 查询事项库详情
	getItemLibrary: async (id : number) => {
		return await request.get({ url: `/audit/tank-matters-info/get?id=` + id })
	},

	// 新增事项库
	createItemLibrary: async (data : ItemLibraryVO) => {
		return await request.post({ url: `/audit/tank-matters-info/create`, data })
	},

	// 修改事项库
	updateItemLibrary: async (data : ItemLibraryVO) => {
		return await request.put({ url: `/audit/tank-matters-info/update`, data })
	},

	// 删除事项库
	deleteItemLibrary: async (id : number) => {
		return await request.delete({ url: `/audit/tank-matters-info/delete?id=` + id })
	},

	// 删除审计类型和审计事项关联关系
	deleteAuditItemLibrary: async (id : number) => {
		return await request.get({ url: `/audit/tank-matters-info/delete-audit-classification?id=` + id })
	},

	// 导出事项库 Excel
	exportItemLibrary: async (params : any) => {
		return await request.download({ url: `/audit/tank-matters-info/export-excel`, params })
	},
	// 导出审计类型 Excel
	exportItemLibraryType: async (params : any) => {
		return await request.download({ url: `/audit/tank-matters-info/export-excel-bymattertype`, params })
	},
}