import request from '@/config/axios'

// 审计人员管理 VO
export interface AuditorManageVO {
	id ?: number // 主键，唯一标识	
	/*创建人姓名 */
	creatorName : string; 
	/*创建时间;创建时间 */
	createTime : Record<string, unknown>;
	/*更新人姓名 */
	updaterName : string;
	/*姓名 */
	userName : string;
	/*人员id */
	userId : number;
	/*所属机构id */
	ofIntermId : number;
	/*所属机构名称 */
	ofIntermName : string;
	/*职务id */
	dutyId : number;
	/*职务名称 */
	dutyName : string;
	/*生日 */
	brithday : Record<string, unknown>;
	/*年龄（根据生日计算 */
	age : number;
	/*性别 0女 1男 */
	gender : number;
	/*手机号 */
	tel : string;
	/*邮箱 */
	email : string;
	/*从事审计工作年限 */
	workYear : number;
	/*学历id */
	educationId : number;
	/*学历 */
	education : string;
	/*毕业院校 */
	university : string;
	/*专业id */
	majorId : number;
	/*专业名称 */
	majorName : string;
	/*职业资格ids */
	professTitleId : Record<string, unknown>[];
	/*职业资格名称 */
	professTitleName : Record<string, unknown>[];
	/*职称id */
	certId : number;
	/*职称名称 */
	certName : string;
	/*专业特长 */
	speciality : string;
	/*用户类型 0审计人员 1借调人员 */
	userType : number;
	/*参与项目数 */
	involvedProjectNum : number;
}
interface batchAddVO {
	deptId: Array<number>
	userType: number
}
export interface AuditorManageDetailVO {
	id : number // 主键，唯一标识
	/*创建人姓名 */
	creatorName : string;
	/*创建时间;创建时间 */
	createTime : Record<string, unknown>;
	/*更新人姓名 */
	updaterName : string;
	/*姓名 */
	userName : string;
	/*人员id */
	userId : number;
	/*所属机构id */
	ofIntermId : number;
	/*所属机构名称 */
	ofIntermName : string;
	/*职务id */
	dutyId : number;
	/*职务名称 */
	dutyName : string;
	/*生日 */
	brithday : Record<string, unknown>;
	/*年龄（根据生日计算 */
	age : number;
	/*性别 0女 1男 */
	gender : number;
	/*手机号 */
	tel : string;
	/*邮箱 */
	email : string;
	/*从事审计工作年限 */
	workYear : number;
	/*学历id */
	educationId : number;
	/*学历 */
	education : string;
	/*毕业院校 */
	university : string;
	/*专业id */
	majorId : number;
	/*专业名称 */
	majorName : string;
	/*职业资格ids */
	professTitleId : Record<string, unknown>[];
	/*职业资格名称 */
	professTitleName : Record<string, unknown>[];
	/*职称id */
	certId : number;
	/*职称名称 */
	certName : string;
	/*专业特长 */
	speciality : string;
	/*用户类型 0审计人员 1借调人员 */
	userType : number;
	/*参与项目数 */
	involvedProjectNum : number;
}

// 审计人员管理 API
export const AuditorManageApi = {
	// 查询审计人员管理列表分页
	getAuditorManageList: async (params : any) => {
		return await request.get({ url: `/manage/merits-audit-user/page`, params })
	},
	getAuditorManageAll: async () => {
		return await request.get({ url: `/manage/merits-audit-user/getAll` })
	},

	// 查询审计人员管理详情
	getAuditorManage: async (id : number) => {
		return await request.get({ url: `/manage/merits-audit-user/get?id=` + id })
	},

	// 新增审计人员管理
	createAuditorManage: async (data : AuditorManageVO) => {
		return await request.post({ url: `/manage/merits-audit-user/create`, data })
	},
	// 批量新增审计人员管理
	batchCeateAuditorManage: async (data : batchAddVO) => {
		return await request.post({ url: `/manage/merits-audit-user/create-list`, data })
	},


	// 修改审计人员管理
	updateAuditorManage: async (data : AuditorManageVO) => {
		return await request.put({ url: `/manage/merits-audit-user/update`, data })
	},

	// 删除审计人员管理
	deleteAuditorManage: async (id : number) => {
		return await request.delete({ url: `/manage/merits-audit-user/delete?id=` + id })
	},

	// 导出审计人员管理 Excel
	exportAuditorManage: async (params : any) => {
		return await request.download({ url: `/manage/merits-audit-user/export-excel`, params })
	},
	
	// 根据审计人员获得参与项目
	getAuditorManageProjectList: async (params : any) => {
		return await request.get({ url: `/manage/merits-audit-user/get-project-audituser`, params })
	},
}
