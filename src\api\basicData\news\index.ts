import request from '@/config/axios'

// 新闻管理 VO
export interface NewsVO {
  title: string // 新闻标题
  id: number // ID
  newsType: number // 新闻类型id
  keyWords: string // 关键字
  releaser: string // 发布人
  status: number // 状态
  releaseEnterprise: number // 发布单位
  approveFlag: number // 是否审批
  content: string // 新闻内容
  attachments: string // 附件
  showImg: string // 展示图片

}

// 新闻管理 API
export const NewsApi = {
  // 查询新闻管理分页
  getNewsPage: async (params: any) => {
    return await request.get({ url: `/system/news/page`, params })
  },

  // 查询新闻管理详情
  getNews: async (id: number) => {
    return await request.get({ url: `/system/news/get?id=` + id })
  },

  // 新增新闻管理
  createNews: async (data: NewsVO) => {
    return await request.post({ url: `/system/news/create`, data })
  },

  // 修改新闻管理
  updateNews: async (data: NewsVO) => {
    return await request.put({ url: `/system/news/update`, data })
  },

  // 删除新闻管理
  deleteNews: async (id: number) => {
    return await request.delete({ url: `/system/news/delete?id=` + id })
  },

  // 导出新闻管理 Excel
  exportNews: async (params) => {
    return await request.download({ url: `/system/news/export-excel`, params })
  }
}
