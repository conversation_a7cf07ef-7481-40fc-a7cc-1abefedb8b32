<script lang="ts" setup>
	defineOptions({ name: '<PERSON>alog<PERSON><PERSON>' })
	const message = useMessage() // 消息弹窗
	const loading = ref(true)
	const frameRef = ref<HTMLElement | null>(null)
	const fileUrl = ref()
	const dialogVisible = ref(false) // 弹窗的是否展示
	const dialogTitle = ref('') // 弹窗的标题
	const open = async (title : string, flietype : string, id ?: number) => {
		if (id) {
			dialogVisible.value = true
			dialogTitle.value = title//t('action.' + type)
			try {
				if (flietype === 'EDIT' || flietype === 'edit') {
					await (fileUrl.value = import.meta.env.VITE_FLIE_URL_EDIT + id)// 在线编辑
				} else {
					await (fileUrl.value = import.meta.env.VITE_FLIE_URL_VIEW + id) // 在线预览
				}
			} finally {
			}
		} else {
			message.error('操作失败，缺失文件id！')
		}
	}

	defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
<template>
	<Dialog :title="dialogTitle" v-model="dialogVisible" :draggable="false" :scroll="true" maxHeight="80vh" width="75%" setScrollTop="2vh">
		<IFrame :src="fileUrl" />
	</Dialog>
</template>