import request from '@/config/axios'

// 附件 VO
export interface CommonVO {
	id?: number // 主键，唯一标识
	fileName: string
	fileType: string
	fileTypeName: string
	deptId: number
	deptName: string
	fileId: number
	progId: number
} 
export const processKey = {
	evidenceSheet: 'audit_evidence_sheet',
	auditProgrammes: 'project_audit_scheme',
	auditProgrammesAdjust: 'audit_implementation_adjustments',
	projectAuditScheme: 'project_audit_scheme', // 审计方案 流程唯一标识
	projectNoticeFlow: 'project_notice_flow', // 审计通知书  流程唯一标识
	noticeRectification: 'notice_rectification', // 整改通知  流程唯一标识
	auditSumGenerationProcess: 'audit_sum_generation_process', // 审计整改-整改方案  联络人汇总生成流程
	auditZgResult: 'audit_zg_result', // 整改结果录入
	auditRectificationReport: 'audit_rectification_report', // 整改报告

} 
// 附件 API
export const CommonApi = {
	// 附件预览接口
	getOneFileByMainIdAndProjectId: async (params: any) => {
		return await request.get({ url: `/manage/project-assign-atta/getOneFileByMainIdAndProjectId`, params })
	},
	// 创建项目作业-附件通用
	createCommon: async (data: CommonVO) => {
		return await request.post({ url: `/manage/project-assign-atta/create`, data })
	},
	// 流程开始前获取下一节点审批人
	getBeforeProcessPerson: async (data: any) => {
		return await request.post({ url: `/bpm/task/createBeforeUser`, data })
	},
	// 通用获取流程下一节点信息
	getCommonProcessDetail: async (params: any) => {
		return await request.get({ url: `/manage/proj-plan-project/show-next-approval`, params })
	},


}