<template>
  <Dialog :title="dialogTitle" :scroll="true" v-model="dialogVisible" width="85%" maxHeight="70vh">
    <el-row :gutter="16">
      <el-col :span="24">
        <div class="detail-title common-border-left-blue">
          <span>项目基本信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="项目名称">{{ formData.projectName }}</el-descriptions-item>
          <el-descriptions-item label="项目编号">{{ formData.projectNo }}</el-descriptions-item>
          <el-descriptions-item label="审计类型">{{ formData.auditTypeDesc }}</el-descriptions-item>
          <el-descriptions-item label="立项年度">{{ formData.projectYear }}</el-descriptions-item>
          <el-descriptions-item label="组织方式">
            <dict-tag :type="DICT_TYPE.ORGANIZATION_TYPE" :value="formData.orgType" />
          </el-descriptions-item>
          <el-descriptions-item label="时间计划">{{ formData.timeSchedule }}</el-descriptions-item>
          <el-descriptions-item label="审计期间">{{ formData.auditPeriod }}</el-descriptions-item>
          <el-descriptions-item label="开展事项">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="formData.specialFlag" />
          </el-descriptions-item>
          <el-descriptions-item label="整体报告">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="formData.overallReportFlag" />
          </el-descriptions-item>
          <el-descriptions-item label="公司领导">{{ formData.companyLeader }}</el-descriptions-item>
          <el-descriptions-item label="曾任职务">{{ formData.previouWork }}</el-descriptions-item>
          <el-descriptions-item label="发文编号">{{ formData.docNum }}</el-descriptions-item>
          <el-descriptions-item label="是否境外">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="formData.overseasFlag" />
          </el-descriptions-item>
          <el-descriptions-item label="是否重要">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="formData.significantFlag" />
          </el-descriptions-item>
          <el-descriptions-item label="实施单位">{{
            formData.implementDeptName
          }}</el-descriptions-item>
          <el-descriptions-item label="立项依据" :span="4">{{
            formData.projectGist
          }}</el-descriptions-item>
        </el-descriptions>
        <div class="detail-title common-border-left-blue">
          <span>审计对象信息</span>
        </div>
        <!-- <el-row>
          <el-col :span="24" class="text-right">
            <el-button type="primary" plain @click="showTeamDetail(formData.id,'')">查看小组信息</el-button>
          </el-col>
        </el-row> -->
        <el-table
          border
          :data="formData.auditTargetList"
          :stripe="true"
          :show-overflow-tooltip="true"
        >
          <el-table-column label="#" width="50" align="center">
            <template #default="{ $index }">{{
              (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
            }}</template>
          </el-table-column>
          <el-table-column label="项目编号" align="left" prop="projectNo" min-width="180" />
          <el-table-column label="项目名称" align="left" prop="projectName" min-width="260" />
          <el-table-column label="审计对象" align="left" prop="auditTarget" min-width="180" />
          <el-table-column label="组长" align="center" prop="groupLeader" min-width="120" />
          <el-table-column label="项目状态" align="center" prop="projectStatus" min-width="88" />
          <el-table-column label="操作" align="center" :width="180" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="showTeamDetail('', scope.row.id)"
                >小组信息</el-button
              >
              <!-- <el-button type="danger" link @click="handleProfileDelete(scope.$index)">删除</el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <div class="detail-title common-border-left-blue">
          <span>审计事项信息</span>
        </div>
        <el-table :data="auditGroupList" :stripe="true" :show-overflow-tooltip="true">
          <el-table-column label="#" width="50" align="center">
            <template #default="{ $index }">{{
              (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
            }}</template>
          </el-table-column>
          <el-table-column label="审计角色" align="center" prop="roleName" min-width="120" />
          <el-table-column label="项目成员" align="center" prop="userName" min-width="120" />
          <el-table-column label="身份类型" align="center" prop="identityName" min-width="120" />
          <el-table-column label="审计体系" align="center" prop="auditSystemName" min-width="120" />
          <el-table-column label="审计事项" align="left" prop="auditMatterName" min-width="180" />
          <el-table-column label="操作" align="center" :width="160" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link @click="showRelateModal(row)">关联</el-button>
              <el-button type="primary" link @click="handleShowForm(row)">生成底稿</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <template #footer>
      <el-button @click="offDialog">关闭</el-button>
    </template>
  </Dialog>

  <!-- <Dialog :title="dialogListTitle" v-model="dialogListVisible" width="70%">

  </Dialog>-->
  <!-- 文件上传 -->
  <!-- <FileForm ref="formImgRef" @success="handleUploadSuccess" :type="fileType" :limit="fileLimit" />
  <ConfirmUnit ref="confirmUnitRef" />
  <PlanFillingStatus ref="planFillingStatus" />
  <AdjustModal ref="adjustModal" />-->
  <RelatedModal ref="relatedModalRef" @success="relatedModalRefSuccess" />
  <TeamDetail ref="teamDetailRef" />
  <!-- <EvidenceForm ref="evidenceFormRef" /> -->
  <DialogFlie ref="dialogFlieRef" />
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { AuditRoleApi, AuditRoleVO } from '@/api/basicData/auditRole'
import { ElMessageBox } from 'element-plus'
// import { formatDate } from '@/utils/formatTime'
// import { formatTime } from '@/utils'
import RelatedModal from './child/RelatedModal.vue'
import { WorkingPaperApi } from '@/api/auditManagement/projectAssignment/auditImplem/workingPaper'
import { ProjectDataListApi } from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectDataList'
// import FileForm from '@/views/infra/file/FileForm.vue'
import { DialogFlie } from '@/components/DialogFlie'
// import ConfirmUnit from './ConfirmUnit.vue'
// import PlanFillingStatus from './PlanFillingStatus.vue'
// import AdjustModal from './AdjustModal.vue'
import TeamDetail from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/TeamDetail.vue'
// import EvidenceForm from './EvidenceForm.vue'
/** 审计角色 表单 */
defineOptions({ name: 'CreateWorkPaperModal' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  endTime: undefined,
  relevantRequ: undefined,
  projectName: undefined,
  projectNo: undefined,
  auditTypeDesc: undefined,
  projectYear: undefined,
  orgType: undefined,
  planStart: undefined,
  planEnd: undefined,
  auditPeriod: undefined,
  specialFlag: undefined,
  overallReportFlag: undefined,
  companyLeader: undefined,
  previouWork: undefined,
  docNum: undefined,
  overseasFlag: undefined,
  significantFlag: undefined,
  implementDeptName: undefined,
  projectGist: undefined,
  mattersMaterialList: [],
  auditTargetList: [],
  auditGroupList: [],
  projectAssignAttaRespVOList: []
})
const list = ref([{}])
const formRules = reactive({
  auditRoleName: [{ required: true, message: '不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  parentId: [{ required: true, message: '父级编号不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref
const adjustModal = ref()
const showPlanAdjust = () => {
  adjustModal.value.open(0)
}
// 查看小组信息
const teamDetailRef = ref()
const showTeamDetail = (parId?: number | string, sonId?: number | string) => {
  console.log(sonId)

  teamDetailRef.value.open(parId, sonId)
}
const activeName = ref('0')
const relatedModalRefSuccess = () => {
  message.success('关联成功')
  open(formData.value.id)
}
const planFillingStatus = ref()
const showPlanStatus = () => {
  planFillingStatus.value.open('detail', -1)
}
const relatedModalRef = ref()
const showRelateModal = (obj: any) => {
  console.log(obj)
  relatedModalRef.value.open(obj)
}
// 关闭
const offDialog = () => {
  emit('success')
  dialogVisible.value = false
}
const evidenceFormRef = ref()
const handleShowForm = async (row: object) => {
  const projId = row.projectId
  const matterId = row.id
  const data = await WorkingPaperApi.getMaterialCount(matterId)
  if (!data) {
    ElMessageBox.alert('请先关联取证单或证明材料', '提示', {
      // if you want to disable its autofocus
      // autofocus: false,
      confirmButtonText: '确定',
      callback: () => {
        showRelateModal(row)
      }
    })
    return
  }
  const isFlag = await ProjectDataListApi.getCheckAuditWork(projId, matterId)
  console.log(isFlag)
  let text = '请确认是否生成底稿文件!'
  if (isFlag) {
    text = '您该事项下已经存在底稿信息,请确认是否生成新的底稿!'
  }
  ElMessageBox.confirm(text, '确认生成底稿', {
    confirmButtonText: '确认生成底稿',
    cancelButtonText: '取 消'
  })
    .then(() => {
      message.success('生成成功')
      getCheckAuditWork(projId, matterId)
    })
    .catch(() => console.info('操作取消'))
  return
  // getCheckAuditWork(projId,matterId)
  // evidenceFormRef.value.open()
}
const dialogFlieRef = ref()
const getCheckAuditWork = async (projId: string, matterId: string) => {
  const data = await ProjectDataListApi.createAuditWorkFile(projId, matterId)
  console.log(data)

  dialogFlieRef.value.open(data.fileName, 'VIEW', data.id)
}
const formImgRef = ref()
const fileType = ref('img')
const fileLimit = ref(1)
// 上传方法
const handleImport = (type: string) => {
  fileType.value = type
  fileLimit.value = type === 'file' ? 5 : 1
  formImgRef.value.open()
}
const handleUploadSuccess = (fileList) => {
  let fileArr =
    fileList && fileList.length > 0
      ? fileList.map((item) => {
          return {
            url: item.response.data,
            name: item.name
          }
        })
      : []
  if (fileType.value === 'file') {
    formData.value.attachments = formData.value.attachments.concat(...fileArr)
  } else if (fileType.value === 'img') {
    formData.value.showImg = fileArr
    formRef.value.validateField('showImg')
  }
}
const handleDelete = async (type: string, index: number = 0) => {
  await message.delConfirm()
  formData.value[type].splice(index, 1)
  await message.success(t('common.delSuccess'))
}

const auditGroupList = ref([])
/** 打开弹窗 */
const open = async (id: string) => {
  dialogVisible.value = true
  dialogTitle.value = t('新增')
  // formType.value = type
  // resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ProjectDataListApi.getProjectInitiationDetail(id, '')
      auditGroupList.value = await ProjectDataListApi.getAuditGroupList(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// const dialogListTitle = ref('') // 选择弹窗的标题
// const dialogListVisible = ref(false) // 选择弹窗的是否展示

const detailData = ref({
  auditRoleCode: 0
})
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AuditRoleVO
    if (formType.value === 'create') {
      await AuditRoleApi.createAuditRole(data)
      message.success(t('common.createSuccess'))
    } else {
      await AuditRoleApi.updateAuditRole(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
const handleConfirmUnit = () => {}
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    auditRoleName: undefined,
    parentId: undefined,
    sort: undefined,
    status: undefined,
    projectPhase: undefined,
    date: undefined,
    content: undefined,
    wbflfgList: [
      {
        name: '1'
      }
    ],
    nbgzzdList: [],
    sjmxList: []
  }
  formRef.value?.resetFields()
}
</script>
<style lang="scss" scoped>
.title {
  font-weight: 900;
  font-size: 16px;
  margin-bottom: 16px;
  margin-top: 16px;
  display: flex;
  align-items: center;
}

:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
  width: 80px;
}
</style>
