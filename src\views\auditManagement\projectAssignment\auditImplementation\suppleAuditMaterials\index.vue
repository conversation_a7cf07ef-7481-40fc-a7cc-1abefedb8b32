<template>
  <el-row>
    <el-col :span="24" :xs="24">
      <!-- 查询 -->
      <ContentWrap class="common-card-search">
        <el-form
          class="-mb-15px common-search-form"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="76px"
        >
          <el-form-item label="项目名称" prop="projectName">
            <el-input
              v-model="queryParams.projectName"
              placeholder="请输入项目名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="组织方式" prop="orgType">
            <el-select
              v-model="queryParams.orgType"
              placeholder="请选择组织方式"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.ORGANIZATION_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="审计对象" prop="auditObject">
            <el-input
              v-model="queryParams.auditObject"
              placeholder="请输入审计对象"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="审计类型" prop="auditType">
            <el-select
              v-model="queryParams.auditType"
              placeholder="请选择审计类型"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="dict in auditTypeOptions"
                :key="dict.id"
                :label="dict.auditTypeName"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="项目年度" prop="auditYear">
            <el-date-picker
              v-model="queryParams.auditYear"
              type="year"
              class="!w-200px"
              placeholder="请选择项目年度"
            />
          </el-form-item>
          <el-form-item label="项目进度" prop="username">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择项目进度"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_IS_OR_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="是否境外" prop="overseasFlag">
            <el-select
              v-model="queryParams.overseasFlag"
              placeholder="请选择是否境外"
              clearable
              class="!w-200px"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_IS_OR_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="right-search-btn">
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" />搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" />重置
          </el-button>
        </div>
      </ContentWrap>
      <ContentWrap>
        <el-table border v-loading="loading" :data="list">
          <el-table-column label="#" width="50" align="center">
            <template #default="{ $index }">
              {{
              (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
              }}
            </template>
          </el-table-column>
          <el-table-column
            label="项目年度"
            align="center"
            prop="auditYear"
            :show-overflow-tooltip="true"
            min-width="88"
          />
          <el-table-column
            label="项目编码"
            align="left"
            prop="projectNo"
            :show-overflow-tooltip="true"
            min-width="180"
          />

          <el-table-column label="项目名称" align="left" min-width="260" :show-overflow-tooltip="true">
            <template #default="{ row }">
              <span class="click-pointer" @click="showProjectDetail(row.id)">
                {{
                row.projectName
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="是否境外" align="center" prop="overseasFlag" min-width="88">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="scope.row.overseasFlag" />
            </template>
          </el-table-column>
          <el-table-column label="审计类型" align="left" prop="auditTypeDesc" min-width="120" />
          <el-table-column label="审计二级类型" align="center" prop="auditSencTypeDesc" min-width="120" />
          <el-table-column label="组织方式" align="center" prop="orgType" min-width="120" >
				<template #default="scope">
					<dict-tag :type="DICT_TYPE.ORGANIZATION_TYPE" :value="scope.row.orgType" />
				</template>
			</el-table-column>
          <el-table-column
            label="审计对象"
            align="left"
            prop="auditObject"
            min-width="240"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="时间计划" align="center" prop="planStart" min-width="260">
            <template #default="{row}">
              <span>{{row.timeSchedule }}</span>
            </template>
          </el-table-column>
          <el-table-column label="项目进度" align="center" min-width="160">
            <template #default="{ row }">
              <el-progress :percentage="row?.processPercent ?? 0" />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" :width="120" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                link
                v-if="query.id"
                @click="handleShowMessage(scope.row.id)"
              >录入</el-button>
							<el-button type="primary" link @click="handleDetail(scope.row.id)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
  </el-row>

  <!-- 添加或修改用户对话框 -->
  <!-- <ProjectInitiationApproval ref="formRef" @success="getList" />
  <ChoiceInitiationModal ref="choiceRef" />
  <DetailMessage ref="messageRef" />-->
  <!-- <PlanListDetail ref="detailRef" />
  <PlanListEdit ref="editRef" />-->
  <!-- <CreateAudit ref="createAuditRef" /> -->
  <!-- <ProframmesApproval ref="createAuditRef" />
  <EditMatter ref="editMatterRef" />-->
  <DetailMessage ref="detailMessageRef" />
  <MaterialsEnter ref="materialsEnterRef" />
  <Detail ref="detailRef" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { checkPermi } from '@/utils/permission'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { formatTime } from '@/utils'
import { CommonStatusEnum } from '@/utils/constants'
import {
  ProjectInitiationApi,
  searchListVO
} from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectInitiation'

import { AuditTypeApi } from '@/api/basicData/auditType'
import DetailMessage from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/message/DetailMessage.vue'
import MaterialsEnter from './MaterialsEnter.vue'
// import ProjectInitiationApproval from './ProjectInitiationApproval.vue'
// import ChoiceInitiationModal from './ChoiceInitiationModal.vue'
// import DetailMessage from './message/DetailMessage.vue'
// import PreparationForm from './PreparationForm.vue'
// import PreparationApproval from './PreparationApproval.vue'
// import PlanListDetail from './PlanListDetail.vue'
// import PlanListEdit from './PlanListEdit.vue'
// import CreateAudit from './CreateAudit.vue'
// import EditMatter from './child/EditMatter.vue'
// import ProframmesApproval from './ProframmesApproval.vue'
import Detail from './Detail.vue'
defineOptions({ name: 'SuppleAuditMaterials' })
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  username: undefined,
  mobile: undefined,
  status: undefined,
  deptId: undefined,
  projectId: undefined
})
const queryFormRef = ref() // 搜索的表单
const { query } = useRoute()
const choiceRef = ref()
const handleChoice = () => {
  choiceRef.value.open()
}
const filter = (type: any, list: any) => {
  let label = type
  list?.map((ele) => {
    if (ele.value == type) {
      label = ele.label
    }
  })
  return label
}
const getProgress = (row: any) => {
  if (
    row.programCount === 0 ||
    row.allProgramCount === 0 ||
    isNaN(row.programCount) ||
    isNaN(row.allProgramCount)
  ) {
    return 0
  } else {
    return ((row.programCount / row.allProgramCount) * 100).toFixed(2)
  }
}
// 查看
const detailRef = ref()
const handleDetail = (id: number) => {
  detailRef.value.open(id)
}
const detailMessageRef = ref()
const showProjectDetail = (id: number) => {
  detailMessageRef.value.open(id)
}
const editMatterRef = ref()
const handleEditMatterRef = (id: number) => {
  editMatterRef.value.open()
}
const auditTypeOptions = ref({}) //审计类型
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const dataType = await AuditTypeApi.getAuditTypeList({ parentId: 0, pagenum: 999999 })
    auditTypeOptions.value = dataType.list
    const data = await ProjectInitiationApi.getProjPlanProjectList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
const processReviewRef = ref()
const handleCreate = () => {
  processReviewRef.value.open()
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
const editRef = ref()
const handleEdit = () => {
  editRef.value.open()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}
const materialsEnterRef = ref()
const handleShowMessage = (id: number) => {
  materialsEnterRef.value.open(id)
}
/** 处理部门被点击 */
const handleDeptNodeClick = async (row) => {
  queryParams.deptId = row.id
  await getList()
}

/** 添加/修改操作 */
const formRef = ref()
// const detailRef = ref()
const openForm = (type: string, id?: number) => {
  if (type === 'detail') {
    detailRef.value.open()
  } else {
    formRef.value.open(type, id)
  }
}

/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}

/** 修改用户状态 */
const handleStatusChange = async (row: UserApi.UserVO) => {
  try {
    // 修改状态的二次确认
    const text = row.status === CommonStatusEnum.ENABLE ? '启用' : '停用'
    await message.confirm('确认要"' + text + '""' + row.username + '"用户吗?')
    // 发起修改状态
    await UserApi.updateUserStatus(row.id, row.status)
    // 刷新列表
    await getList()
  } catch {
    // 取消后，进行恢复按钮
    row.status =
      row.status === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE
  }
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await UserApi.exportUser(queryParams)
    download.excel(data, '用户数据.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await UserApi.deleteUser(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 重置密码 */
const handleResetPwd = async (row: UserApi.UserVO) => {
  try {
    // 重置的二次确认
    const result = await message.prompt(
      '请输入"' + row.username + '"的新密码',
      t('common.reminder')
    )
    const password = result.value
    // 发起重置
    await UserApi.resetUserPwd(row.id, password)
    message.success('修改成功，新密码是：' + password)
  } catch {}
}

/** 分配角色 */
const assignRoleFormRef = ref()
const handleRole = (row: UserApi.UserVO) => {
  assignRoleFormRef.value.open(row)
}

/** 初始化 */
onMounted(() => {
  if (query.id) {
    queryParams.projectId = query.id
  }
  getList()
})
</script>
