import request from '@/config/axios'


// 查询单位树
export function getUnitTree() {
  return request.post({
    url: '/model/chartCommonCondition/modelOrganizationTreeData'
  })
}

// 获取指标下拉选项
export function getIndicatorList(modelId) {
  return request.post({
    url: '/model/chartCommonCondition/modelIndexSelectData/' + modelId,
  })
}

// 获取tab1图表数据
export function getTab1ChartData(data) {
  return request.post({
    url: '/model/contractPerformanceRisk/companyDimensionChartData',
    data: data
  })
}

// 获取tab1表格数据
export function getTab1TableData(data) {
  return request.post({
    url: '/model/contractPerformanceRisk/companyDimensionTableData',
    data: data
  })
}

// 保存导出参数
export function saveExportParams(data) {
  return request.post({
    url: '/model/contractPerformanceRisk/saveExportParameter',
    data: data
  })
}

// tab1 数据导出
export function exportTab1Data(key) {
  return request.download({
    url: '/model/contractPerformanceRisk/exportContractPartyDimensionTableData/' + key,
  })
}

// tab2
// 获取单位下拉选项
export function getCompanyList() {
  return request.post({
    url: '/model/chartCommonCondition/modelOrganizationSelectData',
  })
}

// 获取tab2图表数据
export function getTab2ChartData(data) {
  return request.post({
    url: '/model/contractPerformanceRisk/supplierDimensionChartData',
    data: data
  })
}

// 获取tab2表格数据
export function getTab2TableData(data) {
  return request.post({
    url: '/model/contractPerformanceRisk/supplierDimensionTableData',
    data: data
  })
}

// tab2 数据导出
export function exportTab2Data(key) {
  return request.download({
    url: '/model/contractPerformanceRisk/exportSupplierDimensionTableData/' + key,
  })
}

// 下钻
export function getDrillData(data) {
  return request.post({
    url: '/model/contractPerformanceRisk/drillDetailPageData',
    data: data
  })
}


// 明细数据导出
export function exportDrillData(key) {
  return request.download({
    url: '/model/contractPerformanceRisk/exportDetailData/' + key,
  })
}
