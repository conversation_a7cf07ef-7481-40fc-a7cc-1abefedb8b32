<template>
  <div>
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="110px">
      <el-row>
        <el-col :span="24" class="text-right">
          <el-form-item label="截止日期" prop="endTime">
            <el-date-picker
              v-model="formData.endTime"
              value-format="x"
              type="date"
              disabled
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="相关要求" prop="relevantRequ">
            <el-input
              v-model="formData.relevantRequ"
              placeholder="请输入相关要求"
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <div class="mb-10px mt-10px flex_center">
        <div class="flex_center fles_interval title common-border-left-blue"> 资料清单填报 </div>
        <div>
          <el-button type="primary" plain @click="handleTemplateDownload">模板下载</el-button>
          <el-button type="primary" plain @click="handleDataImport">数据导入</el-button>
          <!-- <el-button type="primary" plain :disabled="multiple" @click="handleProfileBatchDelete"
          >批量删除</el-button
        > -->
          <el-button type="primary" plain @click="handleProfileAdd">新增</el-button>
          <el-button type="primary" plain @click="allocationHandleUser">批量分配</el-button>
        </div>
      </div>
      <el-table
        :data="formData.materialList"
        row-key="id"
        ref="multipleTableRef"
        @selection-change="handleSelectionChange"
        :stripe="true"
        :show-overflow-tooltip="true"
      >
        <el-table-column type="selection" width="40" />
        <el-table-column label="#" type="index" width="50" />
        <el-table-column label="资料名称" align="center" prop="materialName" min-width="120">
          <template #default="scope">
            <el-form-item
              :prop="'materialList.' + scope.$index + '.materialName'"
              :rules="formRules.materialName"
              label-width="0px"
              style="margin-bottom: 0"
            >
              <el-input v-model="scope.row.materialName" placeholder="请输入资料名称" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="资料类型" align="center" prop="resourceCode">
          <template #default="scope">
            <el-form-item
              :prop="'materialList.' + scope.$index + '.resourceCode'"
              :rules="formRules.resourceCode"
              label-width="0px"
              style="margin-bottom: 0"
            >
              <!-- wel_check_file_type -->
              <el-select v-model="scope.row.resourceCode" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="dict in getIntDictOptions('wel_check_file_type')"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
              <!-- <el-input v-model="scope.row.resourceName" placeholder="请输入资料类型" /> -->
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="是否有模版" align="center" prop="templateFlag" min-width="100">
          <template #default="scope">
            <el-form-item
              :prop="'materialList.' + scope.$index + '.templateFlag'"
              :rules="formRules.templateFlag"
              label-width="0px"
              style="margin-bottom: 0"
            >
              <el-select
                v-model="scope.row.templateFlag"
                placeholder="是否有模版"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_IS_OR_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="模版名称" align="center" prop="fileName" min-width="100">
          <template #default="{ row }">
            <el-button
              type="primary"
              link
              v-if="row.fileName"
              @click="handleShowMessage(row.fileName, 'VIEW', row.templateId)"
              >{{ row.fileName }}</el-button
            >
            <div v-else>无</div>
          </template>
        </el-table-column>
        <el-table-column label="相关联系人" align="center" prop="linkUserName" min-width="100">
          <template #default="scope">
            <el-form-item
              :prop="'materialList.' + scope.$index + '.linkUserName'"
              :rules="formRules.linkUserName"
              label-width="0px"
              style="margin-bottom: 0"
            >
            {{ scope.row.linkUserName }}
              <!-- <el-input v-model="scope.row.linkUserName" placeholder="请输入相关联系人" /> -->
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="电话" align="center" prop="tel">
          <template #default="scope">
            <el-form-item
              :prop="'materialList.' + scope.$index + '.tel'"
              :rules="formRules.tel"
              label-width="0px"
              style="margin-bottom: 0"
            >
            {{ scope.row.tel }}
              <!-- <el-input v-model="scope.row." placeholder="请输入电话" /> -->
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" :width="160" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="allocationHandleUserOne(scope.row)"
              >分配</el-button
            >
            <el-button
              v-show="scope.row.templateFlag == 1 && !scope.row.templateId"
              type="primary"
              link
              @click="handleProfileUpload('all', 0, scope.$index)"
              >上传</el-button
            >
            <el-button type="danger" link @click="handleProfileDelete(scope.$index)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="detail-title common-border-left-blue">
        <span>项目基本信息</span>
      </div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="项目名称">{{ detailData.projectName }}</el-descriptions-item>
        <el-descriptions-item label="项目编号">{{ detailData.projectNo }}</el-descriptions-item>
        <el-descriptions-item label="审计类型">{{ detailData.auditTypeDesc }}</el-descriptions-item>
        <el-descriptions-item label="立项年度">{{ detailData.projectYear }}</el-descriptions-item>
        <el-descriptions-item label="组织方式">
          <dict-tag :type="DICT_TYPE.ORGANIZATION_TYPE" :value="detailData.orgType" />
        </el-descriptions-item>
        <el-descriptions-item label="时间计划">{{ detailData.timeSchedule }}</el-descriptions-item>
        <el-descriptions-item label="审计期间">{{ detailData.auditPeriod }}</el-descriptions-item>
        <el-descriptions-item label="开展事项">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.specialFlag" />
        </el-descriptions-item>
        <el-descriptions-item label="整体报告">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.overallReportFlag" />
        </el-descriptions-item>
        <el-descriptions-item label="公司领导">{{ detailData.companyLeader }}</el-descriptions-item>
        <el-descriptions-item label="曾任职务">{{ detailData.previouWork }}</el-descriptions-item>
        <el-descriptions-item label="发文编号">{{ detailData.docNum }}</el-descriptions-item>
        <el-descriptions-item label="是否境外">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.overseasFlag" />
        </el-descriptions-item>
        <el-descriptions-item label="是否重要">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.significantFlag" />
        </el-descriptions-item>
        <el-descriptions-item label="实施单位">{{
          detailData.implementDeptName
        }}</el-descriptions-item>
        <el-descriptions-item label="立项依据" :span="4">{{
          detailData.projectGist
        }}</el-descriptions-item>
      </el-descriptions>
      <div class="detail-title common-border-left-blue">
        <span>审计对象信息</span>
      </div>
      <!-- <el-row>
        <el-col :span="24" class="text-right">
          <el-button type="primary" plain @click="showTeamDetail(formData.id,'')">查看小组信息</el-button>
        </el-col>
      </el-row> -->
      <el-table
        border
        :data="detailData.auditTargetList"
        :stripe="true"
        :show-overflow-tooltip="true"
      >
        <el-table-column label="#" type="index" width="50" />
        <el-table-column label="项目编号" align="left" prop="projectNo" min-width="180" />
        <el-table-column label="项目名称" align="left" prop="projectName" min-width="260" />
        <el-table-column label="审计对象" align="left" prop="auditTarget" min-width="180" />
        <el-table-column label="组长" align="center" prop="groupLeader" min-width="120" />
        <el-table-column label="项目状态" align="center" prop="projectStatus" min-width="88" />
        <el-table-column label="操作" align="center" :width="180" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="showTeamDetail('', scope.row.id)"
              >小组信息</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="mb-10px mt-10px flex_center">
        <div class="flex_center fles_interval title common-border-left-blue"> 迎审资料清单 </div>
        <!-- <div>
          <el-button type="primary" plain @click="handleImportFile('all')">附件上传</el-button>
        </div> -->
      </div>
      <el-table :data="fileList" :stripe="true" :show-overflow-tooltip="true">
        <el-table-column label="#" type="index" width="50" />
        <el-table-column label="文档名称" align="left" prop="fileName" min-width="180" />
        <el-table-column label="文档类型" align="center" prop="fileTypeName" min-width="120" />
        <el-table-column label="编制单位" align="center" prop="deptName" min-width="180" />
        <el-table-column label="编制人" align="center" prop="creatorName" />
        <el-table-column label="生成时间" align="center" prop="createTime">
          <template #default="{ row }">
            <span>{{ formatDate(row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" :width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="handleDownload(scope.row?.fileUrl, scope.row?.fileName)"
              v-hasPermi="['system:user:update']"
              >下载</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleShowMessage(scope.row.fileName, 'VIEW', scope.row.fileId)"
              v-hasPermi="['system:user:update']"
              >查看</el-button
            >
            <!-- <el-button
              type="danger"
              link
              @click="handleFileDelete('projectAssignAttaRespVOList', scope.$index)"
              v-hasPermi="['system:user:update']"
              >删除</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>

  <!-- 附件上传 -->
  <FileForm
    ref="formImgRef1"
    @success="handleUploadSuccess1"
    :type="fileType"
    :showRadio="true"
    :radioListDic="radioListDic"
  />
  <!-- 文件上传 -->
  <FileForm ref="formImgRef" @success="handleUploadSuccess" :type="fileType" :limit="fileLimit" />
  <!-- 审计小组 -->
  <TeamDetail ref="teamDetailRef" />
  <!-- 导入 -->
  <ImportForm ref="importFormRef" @success="handleData" />
  <DialogFlie ref="dialogFlieRef" />
  <AllocationModal ref="allocationRef" @success="checkSuccess" @other="otherSuccess" />
  <CheckPersonList ref="checkPersonRef" @success="checkSuccess" />
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE, getDictLabel } from '@/utils/dict'
import { ProjectDataListApi } from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectDataList'
import FileForm from '@/views/infra/file/FileForm.vue'
import TeamDetail from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/TeamDetail.vue'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { CheckPersonList } from '@/components/CheckPersonList'
import type { TableInstance } from 'element-plus'
import { DialogFlie } from '@/components/DialogFlie'
import { formatDate } from '@/utils/formatTime'
import ImportForm from '@/views/auditManagement/projectAssignment/auditImplementation/suppleAuditMaterials/ImportForm.vue'
import { formatTime } from '@/utils'
import { ProjectApprovalApi } from '@/api/auditManagement/reviewManagement/projectApproval'
import AllocationModal from './child/AllocationModal.vue'
import { set } from 'min-dash'
import { propTypes } from '@/utils/propTypes'
import { YearPlanPreparationApi } from '@/api/auditManagement/plan/auditPlan/yearPlanPreparation'
import download from '@/utils/download'
defineOptions({ name: 'PreparationApproval' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { wsCache } = useCache()
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const detailData = ref({
  projectName: undefined,
  projectNo: undefined,
  id: undefined,
  auditTypeDesc: undefined,
  projectYear: undefined,
  orgType: undefined,
  planStart: undefined,
  planEnd: undefined,
  auditPeriod: undefined,
  specialFlag: undefined,
  overallReportFlag: undefined,
  companyLeader: undefined,
  previouWork: undefined,
  docNum: undefined,
  overseasFlag: undefined,
  significantFlag: undefined,
  implementDeptName: undefined,
  projectGist: undefined,
  auditTargetList: [],
  auditGroupList: []
})
const formData = ref({
  id: undefined,
  endTime: undefined,
  relevantRequ: undefined,
  materialList: [],
  supplementalInput: true,
  projectAssignAttaRespVOList: []
})
const props = defineProps({
  id: propTypes.number.def(undefined),
  isApprove: propTypes.bool.def(),
  formVariables: {
    default: () => ({}),
    type: Object
  },
  saveAndSubmit: {
    default: false,
    type: Boolean
  }
})
watch(
  () => props.saveAndSubmit,
  (newVal) => {
    if (newVal) {
      submitForm(2)
    }
  }
)
const formRules = reactive({
  // relevantRequ: [{ required: true, message: '相关要求不能为空', trigger: 'blur' }],
  // endTime: [{ required: true, message: '截止日期不能为空', trigger: 'change' }]
  // provideFlag: [{ required: true, message: '是否提供为空', trigger: 'change' }],
  // noProvideReason: [{ required: true, message: '未提供原因说明为空', trigger: 'change' }],
  // linkUserName: [{ required: true, message: '相关联系人不能为空', trigger: 'change' }],
  // tel: [{ required: true, message: '电话不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

const allocationRef = ref()
const checkPersonRef = ref()
// 选择人员
const selectFileListOne = ref([])
const isSelectOne = ref(false)
console.log(selectFileListOne.value, '**********')
const allocationHandleUser = () => {
  isSelectOne.value = false
  if (selectFileList.value.length < 1) {
    message.error('请选择需要添加的文件')
    return
  }
  let tFlag = false
  selectFileList.value.map((ele) => {
    if (ele.templateFlag == 1 && !ele.fileName) {
      tFlag = true
    }
  })
  if (tFlag) {
    message.error('存在未上传模板的资料，请检查并上传后再分配人员')
    return
  }
  allocationRef.value.open(detailData.value.id)
}
const allocationHandleUserOne = (row: any) => {
  console.log(row)
  if (row.templateFlag == 1 && !row.fileName) {
    message.error('该资料模板未上传，请先上传模板。')
    return
  }
  selectFileListOne.value = [row]
  isSelectOne.value = true
  allocationRef.value.open(detailData.value.id)
}
const selectFileList = ref([])
const handleSelectionChange = (val: []) => {
  console.log(val)
  selectFileList.value = val
}
const otherSuccess = () => {
  checkPersonRef.value.open()
}
// let list = []
const checkSuccess = async (perArr) => {
  let select
  let list = []
  if (isSelectOne.value) {
    select = selectFileListOne.value
  } else {
    select = selectFileList.value
  }
  console.log(perArr, '111111111')
  await select.map((ele) => {
    console.log(ele)
    perArr.value.map((item) => {
      if (item.nickname) {
        list.push({
          ...ele,
          sameResourceIdForData: ele.id,
          linkUserId: item.id,
          linkUserName: item.nickname,
          tel: item.mobile
        })
      } else {
        list.push({
          ...ele,
          sameResourceIdForData: ele.id,
          linkUserId: item.userId,
          linkUserName: item.userName,
          tel: item.tel
        })
      }
    })
  })
  // formData.value.materialList.push(...list)

  const oldList = formData.value.materialList.filter((item) => !select.includes(item))
  oldList.forEach((ele) => {
    ele.sameResourceIdForData = ele.id
  })
  let newList = [...oldList, ...list]
  console.log(newList, '222222222222')
  formData.value.materialList = newList
}
/** 打开弹窗 */
const open = async (id: string) => {
  dialogVisible.value = true
  dialogTitle.value = t('录入')
  resetForm()
  formData.value.id = id

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      // 项目基本信息
      // detailData.value = await ProjectDataListApi.getProjectInitiationDetail(id)
      const data = await ProjectDataListApi.getProjectInitiationMaterialDetail2(id)
      detailData.value = data

      // if (data.resourceRespVO) {
      //   formData.value = data.resourceRespVO
      //   formData.value.materialList = []
      //   formData.value.relevantRequ = data.suppleResourceRespVO.relevantRequ
      //   formData.value.endTime = data.suppleResourceRespVO.endTime
      // } else {
      formData.value = {
        endTime: data.endTime,
        relevantRequ: data.relevantRequ,
        materialList: [],
        projectAssignAttaRespVOList: data.projectAssignAttaRespVOList
      }
      formData.value.projectId = detailData.value.id
      // }
    } finally {
      formLoading.value = false
    }
  }
}
// defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 项目资料清单多选
const multipleTableRef = ref<TableInstance>()
const multipleSelection = ref([])
const ids = ref([])
const multiple = ref(true)

// 资料清单模板下载
const handleTemplateDownload = async () => {
  const res = await YearPlanPreparationApi.getTemplateFile('padding_infra_import_template')
  // download.excel(res.data, '导入模版.xls')
  // window.open(res, '_blank')
  download.downFileByFileUrl(res, '资料清单填报')
}
// 数据导入
const importFormRef = ref()
const handleDataImport = () => {
  importFormRef.value.open()
}
const handleData = (val) => {
  formData.value.materialList = val
}
// 批量删除
const handleProfileBatchDelete = async () => {
  let newData = formData.value.materialList.filter((a) => !ids.value.some((b) => a.id === b))
  // 删除的二次确认
  try {
    await message.delConfirm()
    formData.value.materialList = newData
    message.success(t('common.delSuccess'))
  } catch {}
}
let materialId = 0
// 资料清单新增
const handleProfileAdd = () => {
  if (!formData.value.materialList) {
    formData.value.materialList = []
  }
  formData.value.materialList.push({
    id: materialId++,
    materialName: undefined,
    resourceCode: undefined,
    templateId: undefined,
    templateFlag: undefined
  })
}
// 资料清单编辑
const handleProfileEditor = () => {}
// 上传
const indexUpLoad = ref()
const fileTypeNumber = ref(0)
const handleProfileUpload = (type: string, val: number, index: number) => {
  fileType.value = type
  fileLimit.value = type === 'file' ? 5 : 1
  indexUpLoad.value = index
  fileTypeNumber.value = val
  formImgRef.value.open()
}
// 资料清单删除
const handleProfileDelete = async (index) => {
  // 删除的二次确认
  try {
    await message.delConfirm()
    formData.value.materialList.splice(index, 1)
    message.success(t('common.delSuccess'))
  } catch {}
}
// 查看小组信息
const teamDetailRef = ref()
const showTeamDetail = (parId?: number | string, sonId?: number | string) => {
  console.log(parId, sonId)
  teamDetailRef.value.open(parId, sonId)
}
// 小组信息
const handleGroupInfo = () => {}

// const handleAuto = () => {
// 	message.confirm('自动生成审计方案会覆盖原有审计方案,请确认操作！')
// }

//上传
const formImgRef = ref()
const formImgRef1 = ref()
const radioListDic = ref()
const fileType = ref('img')
const fileLimit = ref(1)
// 上传方法
const handleImportFile = (type: string) => {
  formImgRef1.value.open()
  fileType.value = type
  radioListDic.value = 'wel_check_file_type'
}

// 文件上传
const handleUploadSuccess = (fileList) => {
  if (fileTypeNumber.value == 0) {
    let fileArr =
      fileList && fileList.length > 0
        ? fileList.map((item) => {
            return {
              url: item.response.data,
              templateId: item.response.data.id,
              name: item.name
            }
          })
        : []
    console.log(fileArr, '0000')
    formData.value.materialList[indexUpLoad.value].templateId = fileArr[0].templateId
    formData.value.materialList[indexUpLoad.value].fileName = fileArr[0].name
  } else {
    let fileArr =
      fileList && fileList.length > 0
        ? fileList.map((item) => {
            return {
              url: item.response.data,
              fileName: item.name,
              fileTypeName: undefined,
              deptName: undefined,
              creatorName: wsCache.get(CACHE_KEY.USER).user.nickname,
              createTime: new Date().toLocaleTimeString()
            }
          })
        : []
    if (fileType.value === 'file') {
      if (!formData.value.projectAssignAttaRespVOList) {
        formData.value.projectAssignAttaRespVOList = []
      }
      formData.value.projectAssignAttaRespVOList =
        formData.value.projectAssignAttaRespVOList.concat(...fileArr)
    } else if (fileType.value === 'img') {
      formData.value.showImg = fileArr
      formRef.value.validateField('showImg')
    }
  }
}
// 附件上传
const handleUploadSuccess1 = async (fileList, radioType) => {
  if (radioListDic.value === 'wel_check_file_type') {
    // 创建一个数组来保存所有文件上传的Promise
    const uploadPromises = fileList.map(async (file) => {
      await ProjectApprovalApi.bindProjectFile({
        fileId: file?.response.data.id,
        // resourceDetailLinkId:
        projectId: detailData.value.id,
        fileType: radioType,
        fileTypeName: getDictLabel('wel_check_file_type', radioType)
      })
    })
    await Promise.all(uploadPromises)
    await message.success('上传成功')
  }
}
//预览编辑
const dialogFlieRef = ref()
const handleShowMessage = (title: string, flietype: string, id: number) => {
  dialogFlieRef.value.open(title, flietype, id)
}
// 下载
const handleDownload = (url: string, name: string) => {
  download.downFileByFileUrl(url, name)
}
// 附件删除
const handleFileDelete = async (type: string, index: number = 0) => {
  await message.delConfirm()
  formData.value[type].splice(index, 1)
  await message.success(t('common.delSuccess'))
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (volidUpLoad(formData.value.materialList)) return
  console.log(formData.value)
  // return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    data.supplementalInput = true

    console.log(formData.value)

    await ProjectDataListApi.registrationProjectSupplementalData(data)
    message.success(t('common.createSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
// 校验是否上传
const volidUpLoad = (list: any) => {
  console.log(list)
  let templatestatus = false
  list.map((ele) => {
    // ele.resourceName =
    if (ele.templateFlag == 1 && !ele.templateId) {
      templatestatus = true
      message.error('有待上传文件项')
      // return
    }
    // ele.templateId
  })
  return templatestatus
}
onMounted(() => {
  console.log(props)
  open(props.id)
  // getWelAuditGroupList()
})
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    endTime: undefined,
    relevantRequ: undefined,
    materialList: [],
    supplementalInput: true,
    projectAssignAttaRespVOList: []
  }
  formRef.value?.resetFields()
}
</script>
<style lang="scss" scoped>
.title {
  font-weight: 900;
  font-size: 16px;
  margin-bottom: 16px;
  margin-top: 16px;
  display: flex;
  align-items: center;
}

:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
  width: 80px;
}
</style>
