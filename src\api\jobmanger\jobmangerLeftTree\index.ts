import request from '@/config/axios'

// 查询列表API 访问日志
export const getJobLeftTreeList = (url : string) => {
	return request.get({ url })
}
export const createJobLeftTreeList = (url : string, data : any) => {
	return request.post({ url, data })
}
export const updateJobLeftTreeList = (url : string, data : any) => {
	return request.post({ url, data })
}
export const delJobLeftTreeList = (url : string, id : number) => {
	return request.delete({ url: `${url}?id=${id}` })
}
export const getJobLeftTreeDetails = (url : string, id : number) => {
	return request.get({ url: `${url}?id=${id}` })
}

