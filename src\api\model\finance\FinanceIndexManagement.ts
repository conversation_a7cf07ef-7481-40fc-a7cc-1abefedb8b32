import request from '@/config/axios'

export const FinanceIndexManagementAPI = {

  /**
   * 获取财务指标目录树
   */
  financeIndexCatalogTree: async () => {
    return request.post({
      url: '/model/financeIndexManagement/financeIndexCatalogTree'
    })
  },

  /**
   * 保存财务指标目录数据
   * @param data
   */
  saveFinanceIndexCatalogData: async (data: any) => {
    return request.post({
      url: '/model/financeIndexManagement/saveFinanceIndexCatalogData',
      data: data
    })
  },

  /**
   * 删除财务指标目录数据
   * @param id
   */
  deleteFinanceIndexCatalogData: async (id: string) => {
    return request.post({
      url: `/model/financeIndexManagement/deleteFinanceIndexCatalogData/${id}`
    })
  },

  /**
   * 获取目标财务指标目录数据
   * @param id
   */
  targetFinanceIndexCatalogData: async (id: string) => {
    return request.post({
      url: `/model/financeIndexManagement/targetFinanceIndexCatalogData/${id}`
    })
  },

  /**
   * 获取财务指标分页数据
   * @param data
   */
  financeIndexPageData: async (data: any) => {
    return request.post({
      url: '/model/financeIndexManagement/financeIndexPageData',
      data: data
    })
  },

  /**
   * 获取目标财务指标数据
   * @param id
   */
  targetFinanceIndexData: async (id: string) => {
    return request.post({
      url: `/model/financeIndexManagement/targetFinanceIndexData/${id}`
    })
  },

  /**
   * 保存财务指标数据
   * @param data
   */
  saveFinanceIndexData: async (data: any) => {
    return request.post({
      url: '/model/financeIndexManagement/saveFinanceIndexData',
      data: data
    })
  },

  /**
   * 删除财务指标数据
   * @param id
   */
  deleteFinanceIndexData: async (id: string) => {
    return request.post({
      url: `/model/financeIndexManagement/deleteFinanceIndexData/${id}`
    })
  },

  /**
   * 获取财务科目树数据
   */
  financeAccountTreeData: async () => {
    return request.post({
      url: '/model/financeAnalysisCommonParameter/financeAccountTreeData'
    })
  },

  /**
   * 导出财务指标分析数据
   * @param params
   */
  exportFinanceIndexData: async (params: any) => {
    return await request.download({
      url: '/model/financeIndexManagement/exportFinanceIndexData',
      params: params
    })
  }

}
