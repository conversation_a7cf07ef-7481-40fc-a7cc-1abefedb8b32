import request from '@/config/axios'


// 查询目录树
export function getResourceTree() {
  return request.post({
    url: '/model/resourceCatalog/resourceCatalogTreeData'
  })
}

// 新增目录树节点
export function saveResourceCatalog(data) {
  return request.post({
    url: '/model/resourceCatalog/saveResourceCatalogData',
    data: data
  })
}

// 查询单节点详情
export function getResourceCatalog(id) {
  return request.post({
    url: '/model/resourceCatalog/resourceCatalogNodeData/' + id
  })
}

// 删除树节点
export function deleteResourceCatalog(id) {
  return request.post({
    url: '/model/resourceCatalog/deleteResourceCatalogNodeData/' + id
  })
}

// 右侧数据分页查询
export function getResourceCatalogData(query) {
  return request.post({
    url: '/model/resourceTable/resourceTablePageData',
    data: query
  })
}


// 获取数据资源表信息数据
export function getResourceTableData(id) {

  return request.post({
    url: `/model/resourceTable/resourceTableInfoData/${id}`
  })
}

// 获取全部数据资源表记录
export function getResourceTableList() {
  return request.post({
    url: '/model/resourceTable/allResourceTableRecords',
  })
}

// 获取需求提出人下拉选项 废弃
export function getDemandProposerOptions(id, userName) {
  return request.get({
    url: `/system/user/userDeptTreeData?parentId=${id}&userName=${userName}`
  })
}

// 获取需求提出人列表
export function getDemandProposerList(params) {
  return request.get({
    url: '/system/user/userPageData',
    params
  })
}

// 获取目标表字段数据
export function getTargetTableField(data) {
  return request.post({
    url: '/model/resourceTableField/targetTableFieldData',
    data: data
  })
}

// 更新字段关键字段属性
export function updateFieldKey(data) {
  return request.post({
    url: '/model/resourceTableField/updateFieldKeyWordProperty',
    data: data
  })
}

// 保存数据资源表数据
export function saveResourceTable(data) {
  return request.post({
    url: '/model/resourceTable/saveResourceTableData',
    data: data
  })
}

// 删除资源数据
export function deleteResourceTable(id) {
  return request.post({
    url: '/model/resourceTable/deleteResourceTableData/' + id
  })
}

// 获取sql模型页面左侧树
export function TableResourceCatalogTreeData(){
  return request.post({
    url: '/model/resourceCatalog/hasTableResourceCatalogTreeData'
  })
}

/**
 * 验证目标表是否存在
 * @param tableName
 * @returns {*}
 */
export function validTargetTableExist(tableName) {
  return request.post({
    url: `/model/resourceTable/validTargetTableExist/${tableName}`
  })
}



