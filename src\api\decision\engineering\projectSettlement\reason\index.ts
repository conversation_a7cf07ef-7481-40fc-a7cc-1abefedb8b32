/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-12-09 16:03:27
 * @Description: 审减原因ApI =>
 */
import request from '@/config/axios'

// 审计费VO
export interface ReasonDeductioneVO{
	id?: string;
	 /*投资单位 */
     investmentUnit: string;
     /*审定结算数量（项） */
     settlementCount: number;
     /*报审结算总金额（万元） */
     reportedAmount: Record<string, unknown>;
     /*审定结算总金额（万元） */
     approvedAmount: Record<string, unknown>;
     /*累计审减值（万元） */
     reductionValue: Record<string, unknown>;
     /*平均审减率 */
     avgReductionRate: Record<string, unknown>;
     /*甲方应付审计费（元） */
     partyAReasonDeductione: Record<string, unknown>;
     /*乙方应付审计费（元） */
     partyBReasonDeductione: Record<string, unknown>;
}
export interface ReasonDeductioneDetailsVO {
	id : number // 主键，唯一标识
	attTypeCode : string // 附件类型编码
	attTypeName : string // 附件类型名称
	documentType: number
	uploadFlag: number
	mustPass : number // 是否比传0否， 1是
	orderNum : number //排序
	status : number //状态
	orgName : string // 组织机构名称（公司名称）
	adder : string
	createTime : Date
	creatorName: string
}

// 审减原因 API
export const ReasonDeductioneApi = {
	// 查询审减原因表分页
	getReasonDeductioneList: async (params : any) => {
		return await request.get({ url: `/manage/en-settle/settlement-review-reduction-reasons`, params })
	},

	// 审减原因表导出 Excel
	exportReasonDeductione: async (params : any) => {
		return await request.download({ url: `/manage/en-settle/export-settlement-review-reduction-reasons-excel`, params })
	},

    // 审减原因费详情
	getReasonDeductioneDetails: async (params : any) => {
		return await request.get({ url: `/manage/en-settle/get-settlement-reduction-reasons`, params })
	},

	// 详情列表的合计
	getReasonDeductioneDetailsTotal: async (params : any) => {
		return await request.get({ url: ``, params })
	},

	// 审减原因详情 Excel-初审
	exportReasonDeductioneDetails: async (params : any) => {
		return await request.download({ url: `/manage/en-settle/export-settlement-reduction-reasons-cs-excel`, params })
	},
    
    // 审减原因详情 Excel-复审
	exportReasonDeductioneDetailsTwo: async (params : any) => {
		return await request.download({ url: `/manage/en-settle/export-settlement-reduction-reasons-fs-excel`, params })
	},
}