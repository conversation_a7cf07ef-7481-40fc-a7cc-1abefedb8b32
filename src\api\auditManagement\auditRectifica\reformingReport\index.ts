import request from '@/config/axios'

// 整改报告 VO
export interface ReportingVO {
	/*ID;ID */
	id : number;
	/* */
	enterNum : number;
	/* */
	projectName : string;
	parentProjectName : string;
	/* */
	projectNo : number;
	/* */
	orgType : number;
	/* */
	auditType : number;
	/* */
	auditTypeDesc : string;
	/* */
	auditObject : string;
	/* */
	projectYear : string;
	/* */
	quesCount : number;
	/* */
	rectificationDeadline : number;
	/* */
	reportState : number;
}
export interface ReportingDetailVO {
	/*ID;ID */
	id : number;
	/* */
	enterNum : number;
	/* */
	projectName : string;
	parentProjectName: string;
	/* */
	projectNo : number;
	/* */
	orgType : number;
	auditYear : number;
	/* */
	auditType : number;
	/* */
	auditTypeDesc : string;
	/* */
	auditObject : string;
	/* */
	projectYear : string;
	/* */
	quesCount : number;
	/* */
	rectificationDeadline : number;
	rectifyContact: string;
	rectifyContactPhone: string;
	reportCreateTime: string;
	/* */
	reportState : number;
	questionMaterials: []
	rectificationMaterialsVOList: []
}

// 整改报告 API
export const ReportingApi = {
	// 查询整改报告列表分页
	getReportingList: async (params : any) => {
		return await request.get({ url: `/manage/rectific-report/page`, params })
	},

	// 查询整改报告详情
	getReporting: async (id : number) => {
		return await request.get({ url: `/manage/rectific-report/get?questionId=` + id })
	},

	// 创建整改报告
	createReporting: async (data : ReportingVO) => {
		return await request.post({ url: `/manage/rectific-report/create`, data })
	},

	// 修改整改报告
	updateReporting: async (data : ReportingVO) => {
		return await request.put({ url: `/manage/rectific-report/update`, data })
	},

	// 审核整改报告
	submitReporting: async (data : ReportingVO) => {
		return await request.post({ url: `/manage/rectific-report/submit`, data })
	},
	// 生成整改报告
	createFileReporting: async (id : number) => {
		return await request.get({ url: `/manage/rectific-report/create-file?noticeId=` + id })
	},
	// 整改报告复核
	reviewReporting: async (data : ReportingVO) => {
		return await request.post({ url: `/manage/notification-association-link/review`, data })
	},
}