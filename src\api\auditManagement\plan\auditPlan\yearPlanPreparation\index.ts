import request from '@/config/axios'

// 年度计划编制

export interface SearchVo {
	pageNo: number
	pageSize: number
	planName?: string
	planNo?: number
	editDeptId?: number
	creatorName?: string
	planYear?: string
	planStatus?: number
	createTime?: Array<string>
}
export interface fileVo {
	id: number
	fileType: number
	fileName: string
	creatorName: string
	createTime: string
}
export interface YearPlanVo {
	id?: number
	planYear: ''
	editDeptName: ''
	editDeptId: number
	creatorName: ''
	planNo: number
	planName: ''
	endTime: undefined
	overallRequire: undefined
	editPrinciple: undefined
	mainContent: undefined
	reportRequest: undefined
	saveType: number
	fileList: Array<fileVo>
}
export interface UpdateDeptYearPlanVo {
	id: number
	planYear: ''
	editDeptName: ''
	editDeptId: number
	creatorName: ''
	planNo: number
	planName: ''
	endTime: string
	guidingIdeology: string
	workingPrinciples: string
	projectArrangement: string
	mainMeasuresEnsure: string
	saveType: number
	fileList: Array<fileVo>
}
export interface ProjectListVo {
	pageNo: number
	pageSize: number
	planId: number
	deptPlanId?: number

}
export interface importFileVo {
	planId: number
	id: number
	fileType: number
}
export interface importDeptFileVo {
	deptPlanId: number
	id: number
	fileType: number
}

export interface planEditBatchVo {
	idList: Array<number>
	implementDeptId: number
}
export interface planStatusVo {
	planId: number
}
export interface idArrVo {
	id: number
}
// 年度计划编制api
export const YearPlanPreparationApi = {
	getYearPlanPreparationList: async (params: SearchVo) => {
		return await request.get({ url: `/manage/year-plan-notice/page`, params })
	},
	delYearPlanById: async (id: number) => {
		return await request.delete({ url: `/manage/year-plan-notice/delete?id=${id}` })

	},
	getYearPlanById: async (id: number) => {
		return await request.get({ url: `/manage/year-plan-notice/get?id=${id}` })
	},
	getDeptYearPlanById: async (id: number) => {
		return await request.get({ url: `/manage/year-plan-report-dept/get?id=${id}` })
	},
	// 年度计划下发详情
	getByCurrentUnit: async (id: number) => {
		return await request.get({ url: `/manage/year-plan-report-dept/getByCurrentUnit?id=${id}` })
	},
	exportYearPlan: async (params: SearchVo) => {
		return await request.download({ url: `/manage/year-plan-notice/export-excel`, params })
	},
	createYearPlan: async (data: YearPlanVo) => {
		return await request.post({ url: `/manage/year-plan-notice/create`, data })
	},
	updateYearPlan: async (data: YearPlanVo) => {
		return await request.put({ url: `/manage/year-plan-notice/update`, data })
	},
	getAuditProgrammesAll: async () => {
		return await request.get({ url: `/system/audit-type/getAll` })
	},
	//新增-保存-查询所有二级部门
	getSecondDeptList: async (params: any) => {
		return await request.get({ url: `/manage/year-plan-report-dept/getSecondDeptList`,params })
	},
	// 汇总调整查询填报单位
	getDeptListByPlanId: async (id: number) => {
		return await request.get({ url: `/manage/year-plan-report-dept/getDeptListByPlanId?id=${id}` })
	},
	//二级部门弹窗保存
	saveSecondDept: async (data: any) => {
		return await request.post({ url: `/manage/year-plan-report-dept/savePlanDept `, data })
	},
	// 汇总调整-项目清单列表
	selectProjectListByDeptIdAndPlanId: async (params: ProjectListVo) => {
		return await request.get({ url: `/manage/year-plan-project/selectProjectListByDeptIdAndPlanId`, params })
	},
	// 资料清单列表
	getCheckFileListByPlanIdDeptPlanId: async (params: ProjectListVo) => {
		return await request.get({ url: `/manage/year-plan-file/getCheckFileListByPlanIdDeptId`, params })
	},
	// 汇总调整-资料清单列表
	bindFileToPlan: async (data: importFileVo) => {
		return await request.post({ url: `/manage/year-plan-file/bindFileToPlan`, data })
	},
	bindDeptFileToPlan: async (data: importDeptFileVo) => {
		return await request.post({ url: `/manage/year-plan-file/bindFileToDeptPlan`, data })
	},
	// 汇总调整-调整保存
	planEditBatch: async (data: planEditBatchVo) => {
		return await request.post({ url: `/manage/year-plan-project/planEditBatch`, data })
	},
	// 查看计划填报情况
	getYearPlanReportDeptPage: async (params: planStatusVo) => {
		return await request.get({ url: `/manage/year-plan-report-dept/getYearPlanReportDeptPage`, params })
	},
	remindersDept: async (data: Array<number>) => {
		return await request.post({ url: `/manage/year-plan-report-dept/remindersDept`, data })
	},
	// 填报-审计资料清单-删除
	deleteDeptFileById: async (id: number) => {
		return await request.delete({ url: `/manage/year-plan-file/delete?id=${id}` })
	},
	// 项目清单-删除
	deleteProjectById: async (id: number) => {
		return await request.delete({ url: `/manage/year-plan-project/delete?id=${id}` })
	},
	// 填报-保存
	updateDeptMessage: async (data: UpdateDeptYearPlanVo) => {
		return await request.put({ url: `/manage/year-plan-report-dept/update`, data })
	},

	//导入文件
	updateFile: async (data: any) => {
		return await request.upload({ url: `/manage/year-plan-project/import-excel`, data })
	},
	// 下载模版公用接口
	getTemplateFile: async (type: string) => {
		return await request.get({ url: `/infra/file/getTemplate?templateType=${type}` })
	},
	// 二进制流下载模版
	getTemplateFileSteam: async () => {
		return await request.download({ url: '/manage/year-plan-project/getTemplate' })
	},
	// 查看项目填报汇总情况
	selectPlanReportList: async (id: number) => {
		return await request.get({ url: `/manage/year-plan-report-dept/selectPlanReportList?id=${id}` })
	},
	// 项目清单-导入后编辑保存 
	updateProject: async (data: any) => {
		return await request.put({ url: `/manage/year-plan-project/update`, data })
	},
	// 驳回
	disagree: async (id: number) => {
		return await request.get({ url: `/manage/year-plan-report-dept/refuse?id=${id}` })
	},
	// 部门计划id获取审计资料清单
	getCheckFileList: async (id: number) => {
		return await request.get({ url: `/manage/year-plan-file/getCheckFileList?id=${id}` })
	},
	// 部门计划id获取项目清单
	getProjectListByDeptPlanId: async (id: number) => {
		return await request.get({ url: `/manage/year-plan-project/getProjectListByDeptPlanId?id=${id}` })
	},

}

