import request from '@/config/axios'
import { get } from 'min-dash'

export interface AddVO {
	projectId: number
	fileList?: []

}
export interface SaveVO {
	id?: number // 主键，唯一标识
	projectId: number // 年度计划项目ID
	templateId: number // 模板ID
	flowId: number // 审计方案-流程id
	flowStatus: number // 审计方案-流程状态
	adjustFlowId: number // 审计方案调整流程id
	adjustFlowStatus: number // 审计方案调整流程状态
	auditMattersSum: number // 审计事项数量
	draftSum: number // 底稿数量
	status: number //状态 0草稿1审核中 2已完成
	schemeCode: string //方案编码
	projectNo: string //项目编号
	projectName: string //项目名称
	auditGroupList: []
	projAuditMatterList: []
	projectAssignAttaRespList: []
}
// 审计方案调整 API
export const AdjustmentOfAuditPlanApi = {
	//审计方案调整-调整-获取审计事项和审计人员列表
	getAuditList: async (id: number) => {
		return await request.get({ url: `/manage/audit-implementation/get?id=${id}` })
	},
	//   审计方案调整-调整-保存
	saveAdjust: async (data: SaveVO) => {
		return await request.put({ url: `/manage/audit-implementation/update`, data })
	},
	// 根据项目id获得审计方案调整当前行的id
	getAdjustId: async (id: number) => {
		return await request.get({ url: `/manage/audit-implementation/get-id?id=${id}` })
	},
	// 生成审计方案
	createAdjustmentOfAuditPlan: async (id: number) => {
		return await request.get({ url: `/manage/audit-implementation/create-file?projectId=` + id })
	},

	// 删除项目作业-审计方案-审计事项
	deleteAuditMatters: async (id: number) => {
		return await request.delete({ url: `/manage/implementation-matter/delete?id=` + id })
	},

	// 审计方案-审计事项及存
	batchCreateMatterList: async (data: any) => {
		return await request.post({ url: `/manage/implementation-matter/batch-create`, data })
	},

	// 审计方案-审计人员及存
	batchCreateMatterUserList: async (data: any) => {
		return await request.post({ url: `/manage/implementation-matter/user/batch-create`, data })
	},
	// 提交弹窗查询流程相关内容
	getAdjustProcessContent: async (params: any) => {
		return await request.get({ url: `/manage/audit-implementation/get-show`, params })
	},

}