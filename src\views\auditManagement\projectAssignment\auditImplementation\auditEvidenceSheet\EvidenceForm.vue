
<template>
  <Dialog
    :title="dialogTitle"
    :scroll="true"
    v-model="dialogVisible"
    width="85%"
    setScrollTop="2vh"
    :loading="formLoading"
  >
    <el-row :gutter="16">
      <el-col :span="24">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="110px"
          class="common-submit-form"
        >
          <el-form-item label="被审计单位" prop="auditOrgName">
            <el-input v-model="formData.auditOrgName" disabled placeholder="请输入被审计单位" />
          </el-form-item>
          <el-form-item label="审计项目" prop="projectName">
            <el-input v-model="formData.projectName" disabled placeholder="请输入审计项目" />
          </el-form-item>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="审计事项" prop="auditMatter">
                <el-input
                  v-model="formData.auditMatter"
                  placeholder="请输入审计事项"
                  disabled
                  class="!w-240px mr-16px"
                />
                <!-- <el-button type="primary" @click="handleShowMessage(scope.row.id)">选择</el-button> -->
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="问题类型"
                :prop="formData.manuallyFillFlag !==2 ? 'questionType' : ''"
              >
                <el-input
                  v-model="formData.questionType"
                  placeholder="请输入问题类型"
                  :disabled="formData.manuallyFillFlag !==2"
                  class="!w-240px mr-16px"
                />
                <el-button type="primary" @click="handleChoice">选择</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item
            label="取证单问题标题"
            prop="certQuestionTitle"
            v-if="formData.manuallyFillFlag!==0 && !firstOpen"
          >
            <el-input v-model="formData.certQuestionTitle" placeholder="请输入取证单问题标题" />
          </el-form-item>
          <el-form-item
            label="取证单问题描述"
            prop="certQuestionDesc"
            v-if="formData.manuallyFillFlag!==0  && !firstOpen"
          >
            <el-input v-model="formData.certQuestionDesc" type="textarea" placeholder="请输入取证单问题描述" />
          </el-form-item>
          <el-form-item
            label="审计建议"
            prop="auditSugg"
            v-if="formData.manuallyFillFlag!==0  && !firstOpen"
          >
            <el-input v-model="formData.auditSugg" type="textarea" placeholder="请输入审计建议" />
          </el-form-item>
          <!-- <el-form-item label="审计事项摘要" prop="auditAbstract" v-if="!firstOpen">
            <el-input v-model="formData.auditAbstract" type="textarea" placeholder="请输入审计事项摘要" />
          </el-form-item>-->
          <el-row :gutter="16" v-if="!firstOpen">
            <el-col :span="12">
              <el-form-item label="审计人员" prop="auditUserName">
                <el-input
                  v-model="formData.auditUserName"
                  placeholder="请输入审计人员"
                  disabled
                  class="!w-240px mr-16px"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="编制日期" prop="editDate">
                <el-date-picker
                  v-model="formData.editDate"
                  type="date"
                  class="!w-240px mr-16px"
                  placeholder="请选择编制日期"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-row class="mb-8px">
          <el-col :span="8" class="text-left">
            <el-button type="primary" @click="showRelateModal">关联</el-button>
          </el-col>
          <el-col :span="16" class="text-right">
            <el-button @click="dialogVisible = false">关闭</el-button>
            <el-button @click="submitForm" type="primary" :disabled="formLoading">保存&生成</el-button>
            <el-button type="primary" plain @click="handleImportFile('all')">附件上传</el-button>
          </el-col>
          <!-- <el-col :span="12" class="text-right">
            <el-button @click="submitForm" type="primary" :disabled="formLoading">保存&生成</el-button>
          </el-col>-->
        </el-row>
        <el-table
          border
          :data="formData.linkSaveReqVOList"
          :stripe="true"
          :show-overflow-tooltip="true"
        >
          <el-table-column label="#" width="50" align="center">
            <template
              #default="{ $index }"
            >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
          </el-table-column>
          <el-table-column label="取证单名称" align="left" prop="projectName" min-width="260" />
          <el-table-column label="编制人" align="center" prop="auditUserName" min-width="100" />
          <el-table-column label="资料清单" align="left" prop="materialName" min-width="200" />
          <el-table-column label="资料类型" align="center" prop="resourceName" />
          <el-table-column label="上传人" align="center" prop="uploadUser" />
          <el-table-column label="上传时间" align="center" prop="uploadTime" min-width="120">
            <template #default="{row}">
              <span>{{ formatTime(row.uploadTime, 'yyyy-MM-dd') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" :width="180" fixed="right">
            <template #default="{row,$index}">
              <el-button type="danger" link @click="handleDelete($index)">删除</el-button>
              <el-button
                type="primary"
                link
                @click="handleDownload(row.fileUrl,row.materialName)"
              >下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <!-- <template #footer>
     
    </template>-->
  </Dialog>
  <TeamDetail ref="teamDetailRef" />
  <RelatedModal ref="relatedModalRef" @success="relateSuccess" />
  <ChoiceDispositionModal ref="disposRef" @success="choiceSuccess" />
  <FileForm
    ref="formImgRef"
    @success="handleUploadSuccess"
    :type="fileType"
    :showRadio="true"
    :radioListDic="'cert_upload_file_type'"
    :limit="fileLimit"
  />
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE, getDictLabel } from '@/utils/dict'
import { AuditRoleApi, AuditRoleVO } from '@/api/basicData/auditRole'
import FileForm from '@/views/infra/file/FileForm.vue'
import { CommonApi } from '@/api/common'
import { ProjectInitiationApi } from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectInitiation'
import ChoiceDispositionModal from './child/ChoiceDispositionModal.vue'
import { useUserStore } from '@/store/modules/user'
import TeamDetail from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/TeamDetail.vue'
import RelatedModal from './child/CreateEvidenceReleated.vue'
import {
  AuditEvidenceSheetApi,
  createVo
} from '@/api/auditManagement/projectAssignment/auditImplem/auditEvidenceSheet'
import download from '@/utils/download'
import { formatTime } from '@/utils'
defineOptions({ name: 'EvidenceForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const userStore = useUserStore()
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
interface ListVo {
  projectName: string
  auditUserName: string
  resourceName: string
  mattersSourceId: number
  materialName: string
  uploadUser: string
  uploadTime: Date
  fileUrl: string
}
const formData = ref({
  projectId: 0,
  auditOrgName: undefined,
  projectName: undefined,
  auditMatter: undefined,
  questionType: undefined,
  certQuestionTitle: undefined,
  auditAbstract: undefined,
  auditSugg: undefined,
  auditUserName: '',
  manuallyFillFlag: 0, //0为啥也没有默认隐藏输入框，1为单选按钮选择，2为点击手动新增
  certQuestionDesc: undefined,
  auditMatterId: undefined,
  projectNo: '',
  editDate: new Date(),
  linkSaveReqVOList: [] as ListVo[],
  questionId: undefined,
  questionTitle: undefined,
  questionAbstract: undefined
})
const list = ref([{}])
const formRules = reactive({
  auditOrgName: [{ required: true, message: '不能为空', trigger: 'blur' }],
  projectName: [{ required: true, message: '不能为空', trigger: 'blur' }],
  auditMatter: [{ required: true, message: '不能为空', trigger: 'blur' }],
  questionType: [{ required: true, message: '不能为空', trigger: 'blur' }],
  certQuestionTitle: [{ required: true, message: '不能为空', trigger: 'blur' }],
  certQuestionDesc: [{ required: true, message: '不能为空', trigger: 'blur' }],
  auditSugg: [{ required: true, message: '不能为空', trigger: 'blur' }],
  auditAbstract: [{ required: true, message: '不能为空', trigger: 'blur' }],
  auditUserName: [{ required: true, message: '不能为空', trigger: 'blur' }],
  editDate: [{ required: true, message: '不能为空', trigger: 'change' }]
})

const choiceSuccess = (row) => {
  firstOpen.value = false
  if (row) {
    formData.value.manuallyFillFlag = 1
  } else {
    formData.value.manuallyFillFlag = 2
  }
  formData.value.questionType = row?.typeName ?? ''
  formData.value.questionId = row?.id ?? ''

  // formData.value.certQuestionTitle = row?.quesName ?? ''
  formData.value.certQuestionTitle = row?.quesName ?? ''
  formData.value.questionTitle = row?.quesName ?? ''
  formData.value.certQuestionDesc = row?.quesDesc ?? ''
  formData.value.questionAbstract = row?.quesDesc ?? ''
  formData.value.auditSugg = row?.auditSugg ?? ''
}
const relateSuccess = (arr) => {
  let newArr = arr.map((item) => {
    return {
      // projectName: formData.value.projectName,
      projectName: `${formData.value.projectNo ?? ''}-审计取证单-${formData.value.auditMatter}`,
      auditUserName: formData.value.auditUserName,
      // resourceName: item.resourceName,
      resourceName: item.fileTypeName,
      mattersSourceId: item.id,
      // materialName: item.materialName,
      materialName: item.fileName,
      uploadUser: item.creatorName,
      uploadTime: item.createTime,
      fileUrl: item.fileUrl
    }
  })
  formData.value.linkSaveReqVOList.push(...newArr)
}
const formRef = ref() // 表单 Ref
const adjustModal = ref()

const teamDetailRef = ref()

const relatedModalRef = ref()
const showRelateModal = () => {
  relatedModalRef.value.open(formData.value.linkSaveReqVOList || [])
}

const formImgRef = ref()
interface fileType {
  attTypeCode: string
  label: string
  value: string
  attTypeName: string
}
const fileType = ref('img')
const fileLimit = ref(100)
const fileMap = new Map<string, string>()
const firstOpen = ref(true) //进入页面隐藏标题摘要等字段，选择完问题后显示
const open = async (data: any, row: any) => {
  firstOpen.value = true

  formData.value.manuallyFillFlag = 0
  formLoading.value = true
  dialogVisible.value = true
  dialogTitle.value = t('审计取证单')

  resetForm()
  if (data) {
    fileTypeList.value = await ProjectInitiationApi.getFileType('ATT21')
    fileTypeList.value.forEach((item) => {
      item.label = item.attTypeName
      item.value = item.attTypeCode
      fileMap.set(item.attTypeCode, item.label)
    })
    formData.value.auditOrgName = data?.auditTargetList[0].auditTarget ?? ''
    formData.value.projectName = data?.projectName ?? ''
    formData.value.projectId = data?.id ?? -1
    formData.value.projectNo = data?.projectNo ?? ''
    formData.value.auditMatter = row?.auditMatterName ?? ''

    formData.value.auditMatterId = row?.auditMatterId ?? -1
    formData.value.auditUserName = userStore.user.nickname
    formLoading.value = false
  }
  // 修改时，设置数据
  if (row.id) {
    try {
      // formData.value = await AuditRoleApi.getAuditRole(id)
    } finally {
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
const disposRef = ref()
const fileTypeList = ref<fileType[]>([])

const handleChoice = () => {
  disposRef.value.open(formData.value.auditMatterId)
}
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  if (formData.value.linkSaveReqVOList.length === 0) {
    await message.confirm('取证单未关联资料，请确认是否继续生成')
    // return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as createVo
    await AuditEvidenceSheetApi.createEvidenceSheet(data)
    message.success('操作成功')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
const handleDownload = (url: string, name: string) => {
  download.downFileByFileUrl(url, name)
}
const handleUploadSuccess = async (fileList, radioType) => {
  let fileArr =
    fileList && fileList.length > 0
      ? fileList.map((item) => {
          return {
            projectName: `${formData.value.projectNo ?? ''}-审计取证单-${
              formData.value.auditMatter
            }`,
            auditUserName: formData.value.auditUserName,
            resourceName: getDictLabel('cert_upload_file_type', radioType),
            resourceCode: 'ATT21',
            mattersSourceId: item.response.data.id,
            materialName: item.name,
            uploadUser: item.response.data.creatorName,
            uploadTime: item.response.data.createTime,
            fileUrl: item.response.data.fileUrl,
            fileId: item.response.data.id,
            fileType: radioType
          }
        })
      : []

  formData.value.linkSaveReqVOList.push(...fileArr)
}
const handleImportFile = (type: string) => {
  formImgRef.value.open()
  fileType.value = type
}
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    auditOrgName: undefined,
    projectName: undefined,
    auditMatter: undefined,
    auditMatterId: undefined,
    questionType: undefined,
    certQuestionTitle: undefined,
    auditSugg: undefined,
    auditAbstract: undefined,
    auditUserName: '',
    certQuestionDesc: undefined,
    manuallyFillFlag: 0,
    editDate: new Date(),
    projectNo: '',
    linkSaveReqVOList: []
  }
  formRef.value?.resetFields()
}
const handleDelete = async (index: number) => {
  await message.delConfirm()
  formData.value.linkSaveReqVOList.splice(index, 1)
  message.success('删除成功')
}
</script>
<style lang="scss" scoped>
.title {
  font-weight: 900;
  font-size: 16px;
  margin-bottom: 16px;
  margin-top: 16px;
  display: flex;
  align-items: center;
}
:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
  width: 80px;
}
</style>