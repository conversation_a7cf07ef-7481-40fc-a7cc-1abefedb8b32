import request from '@/config/axios'

// 招标控制价 
export interface ProjectOverviewVO {
	/*ID;ID */
	id: string;
	/*项目ID */
	epsprojid: string;
	/*项目名称 */
	projectName: string;
	/*工程名称 */
	name: string;
	/*投资单位 */
	proOwnersName: string;
	/*管理单位 */
	company: string;
	/*工程概况 */
	xmgk: string;
	/*中介机构名称 */
	zjjgname: string;
	/*中介机构ID */
	zjjgid: string;
	/*系统向中介机构发出新任务时间（报审日期） */
	sjbsenddate: Record<string, unknown>;
	/*中介机构领取审计定案表时间 */
	sjbsendhdbdate: Record<string, unknown>;
	/*审计报告送达时间 */
	sjbgQrsj: Record<string, unknown>;
	/*中介机构出具审计报告天数(纸版审计报告耗（天）) */
	zjjgSjbgDays: number;
	/*中介机构出具初审意见天数(初审意见天数（天）) */
	zjjgCsyjDays: number;
	/*审计定案表领取至审计报告送达间隔天数(审计定案表（天）) */
	zjjgHdbsjbgDays: number;
	/*施工单位原报结算值(控制价报审值（元）) */
	settlementamount: number;
	/*中介机构最终审定结算值(控制价审定值（元）) */
	jtsjJsmoney: number;
	/*中介机构审减值(审减值（元）（元）) */
	zjjgSjzj: number;
	/*审增值（元） */
	zjjgSjzjD: number;
	/*审计调整率 */
	sjtzl: number;
	/*审计费（元） */
	zjjgSjfy: number;
	/*编制单位（控制价编制单位） */
	bzdw: string;
}

export interface ProjectOverviewSettleVO {
	/*主键ID */
	id: string;
	/*项目名称 */
	projectName: string;
	/*工程名称 */
	title: string;
	/*工程概况 */
	xmgk: string;
	/*开竣工日期 */
	kjgdate: Record<string, unknown>;
	/*施工单位 */
	partyb: string;
	/*合同名称 */
	subcontractname: string;
	/*合同金额 */
	subcontractamount: number;
	/*管理单位 */
	company: string;
	/*投资单位 */
	proOwnersName: string;
	/*施工单位送达管理单位时间 */
	zbzlIsdate: Record<string, unknown>;
	/*施工单位原报结算值 */
	settlementamount: number;
	/*管理单位审定结算值 */
	jsdwJsmoney: number;
	/*中介机构审减值 */
	zjjgSjzj: number;
	/*中介机构审减率 */
	zjjgSjl: number;
	/*集团审计结算金额 */
	jtsjJsmoney: number;
	/*中介机构ID */
	zjjgid: string;
	/*合同类型 */
	projtype: string;
	/*审计部审查发出时间 */
	sjscfcsj: string;
	/*是否复审 */
	sffs: string;
	/*定案表领取至报告送达天数 */
	jssjfxwt: number;
	/*合同签订时间 */
	htqdsj: Record<string, unknown>;
	/*复审合同签订时间 */
	htqdsjFs: Record<string, unknown>;
	/*费用结算时间 */
	fyjssj: Record<string, unknown>;
	/*复审费用结算时间 */
	fyjssjFs: Record<string, unknown>;
	/*复审集团审计结算金额 */
	jtsjJsmoneyFs: number;
	/*复审中介机构审减值 */
	zjjgSjzjFs: number;
	/*复审中介机构审减率 */
	zjjgSjlFs: number;
	/*复审定案表领取至报告送达天数 */
	jssjfxwtFs: string;
	/*复审审计原因 */
	sjyyFs: string;
	/*初审中介 */
	zjjgnameCs: string;
	/*审批状态 */
	status: number;
	/*审计-纸版资料确认接收时间 */
	zbzlSjisdate: Record<string, unknown>;
	/*审计确认终审意见时间 */
	zsyjQrsj: Record<string, unknown>;
	/*审计确认审计核定表时间 */
	sjdaQrsj: Record<string, unknown>;
	/*中介机构出具终审意见天数 */
	zjjgSjyjDays: number;
	/*系统向中介机构发出委托的时间 */
	sjbsenddate: string;
	/*中介机构名称(一审单位) */
	zjjgname: string;
	/*审计部发出中介机构核定表时间 */
	sjbsendhdbdate: Record<string, unknown>;
	/*审计确认收到审计报告时间 */
	sjbgQrsj: Record<string, unknown>;
	/*中介机构出具审计报告天数 */
	zjjgSjbgDays: number;
	/*中介机构出具初审意见天数 */
	zjjgCsyjDays: number;
	/*审计定案表领取至审计报告送达间隔天数 */
	zjjgHdbsjbgDays: number;
	/*评价得分 */
	zjpj: number;
	/*中介机构审计费 */
	zjjgSjfy: number;
	/*序号 */
	sequ: number;
	/*项目id */
	epsprojid: string;
	/*管理单位ID */
	companyid: string;
	/*投资单位ID */
	proOwnersGuid: string;
	/*施工单位ID */
	partybGuid: string;
	/*合同ID */
	subcontractGuid: string;
	/*问题 */
	sjyy: string;
	/*中介机构出具终审意见时间 */
	zsyjQr: Record<string, unknown>;
	/*工程ID */
	ownprojid: string;
}

// 招标控制价 API
export const ProjectOverviewApi = {
	// 获得工程项目-栏表-控制
	getProjectOverviewList: async (params : any) => {
		return await request.get({ url: `/manage/control/all/page`, params })
	},
	// 获得工程_结算分页
	getProjectOverviewSettleList: async (params : any) => {
		return await request.get({ url: `/manage/en-settle/page`, params })
	},
}