<template>
  <Dialog :title="dialogTitle" :scroll="true" v-model="dialogVisible" width="60%" maxHeight="70vh">
    <el-row :gutter="16">
      <!-- <el-col :span="18">底稿名称:{{ data.projectName }}</el-col>
      <el-col :span="6" class="text-right mb-16px">编制人:{{ data.creatorName }}</el-col> -->
      <el-col :span="24" class="mb-16px">
        <el-radio-group
          v-model="radio1"
          @change="radioChange"
          v-for="(item, index) in dicList"
          :key="index"
        >
          <el-radio :value="item.value">{{ item.label }}</el-radio>
          <!-- <el-radio value="ATT08">关联取证单</el-radio> -->
          <!-- <el-radio value="ATT12">关联证明材料</el-radio> -->
        </el-radio-group>
      </el-col>

      <el-col :span="24">
        <el-card class="cardHeight" v-if="radio1 === dicList[0].value">
          <div class="detail-title common-border-left-blue">
            <span>取证单列表</span>
          </div>
          <el-table
            :data="list"
            ref="ATT08Ref"
            :stripe="true"
            :show-overflow-tooltip="true"
            @selection-change="handleSelectionProjAuditList"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="#" width="50" align="center">
              <template #default="{ $index }">{{
                (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
              }}</template>
            </el-table-column>
            <el-table-column label="取证单名称" align="center" prop="fileName" min-width="120" >
            <template #default="{ row }">
              <span class="click-pointer" @click="handleShowMessage(row.fileName, 'VIEW', row.fileId)">
                {{ row.fileName }}
              </span>
            </template>
          </el-table-column>
            <el-table-column label="编制人" align="center" prop="creatorName" />
            <el-table-column label="编制时间" align="center" prop="createTime">
              <template #default="{ row }">
                <span>{{ formatDate(row.createTime) =='Invalid Date' ?'--':formatDate(row.createTime) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
        <el-card class="cardHeight" v-else>
          <div class="detail-title common-border-left-blue">
            <span>证明材料列表</span>
          </div>
          <el-table
            :data="list1"
            :stripe="true"
            ref="ATT12Ref"
            :show-overflow-tooltip="true"
            @selection-change="handleSelectionProjResourceList"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="#" width="50" align="center">
              <template #default="{ $index }">{{
                (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1
              }}</template>
            </el-table-column>
            <el-table-column label="资料名称" align="center" prop="fileName" min-width="120" >
            <template #default="{ row }">
              <span class="click-pointer" @click="handleShowMessage(row.fileName, 'VIEW', row.fileId)">
                {{ row.fileName }}
              </span>
            </template>
          </el-table-column>
            <el-table-column label="资料类型" align="center" prop="fileTypeName" />
            <el-table-column label="上传人" align="center" prop="creatorName" />
            <el-table-column label="上传时间" align="center" prop="createTime" >
              <template #default="{ row }">
                <span>{{ formatDate(row.createTime) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">关联</el-button>
      <el-button @click="dialogVisible = false">取消</el-button>
    </template>
  </Dialog>

  <!-- <Dialog :title="dialogListTitle" v-model="dialogListVisible" width="70%">

  </Dialog>-->
  <!-- 文件上传 -->
  <FileForm ref="formImgRef" @success="handleUploadSuccess" :type="fileType" :limit="fileLimit" />
  <!-- <ConfirmUnit ref="confirmUnitRef" />
  <PlanFillingStatus ref="planFillingStatus" />
  <AdjustModal ref="adjustModal" />-->
  <!-- 查看 -->
  <DialogFlie ref="dialogFlieRef" />
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
// import { AuditRoleApi, AuditRoleVO } from '@/api/basicData/auditRole'
import { formatDate } from '@/utils/formatTime'
import { WorkingPaperApi } from '@/api/auditManagement/projectAssignment/auditImplem/workingPaper'
import FileForm from '@/views/infra/file/FileForm.vue'
import { DialogFlie } from '@/components/DialogFlie'
// import ConfirmUnit from './ConfirmUnit.vue'
// import PlanFillingStatus from './PlanFillingStatus.vue'
// import AdjustModal from './AdjustModal.vue'
/** 审计角色 表单 */
defineOptions({ name: 'RelatedModal' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})

const ATT08Ref = ref() //
const ATT12Ref = ref() //
const dicList = ref() //字典值
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const selectionProjAuditList = ref() // 取证单
const selectionProjResourceList = ref() // 证明材料
const handleSelectionProjAuditList = (val: []) => {
  selectionProjAuditList.value = val
}
const handleSelectionProjResourceList = (val: []) => {
  selectionProjResourceList.value = val
}
const formData = ref({
  id: undefined,
  auditRoleName: undefined,
  parentId: undefined,
  sort: undefined,
  status: undefined,
  projectPhase: undefined,
  projectId: undefined,
  date: undefined,
  content: undefined,
  wbflfgList: [],
  nbgzzdList: [],
  sjmxList: []
})
const radio1 = ref()
const radioChange = async (row) => {
  console.log(row)
  // return
  radio1.value = row
  if (row === dicList.value[0].value) {
    selectionTab(list.value)
  } else {
    selectionTab(list1.value)
  }
  // const data = await WorkingPaperApi.projResource(row, formData.value.projectId)
  // list.value = data.list
}
const list = ref()
const list1 = ref()
const formRules = reactive({
  auditRoleName: [{ required: true, message: '不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  parentId: [{ required: true, message: '父级编号不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref
const adjustModal = ref()
const showPlanAdjust = () => {
  adjustModal.value.open(0)
}
const activeName = ref('0')
const handleClick = () => {}
const planFillingStatus = ref()
const showPlanStatus = () => {
  planFillingStatus.value.open('detail', -1)
}
const formImgRef = ref()
const fileType = ref('img')
const fileLimit = ref(1)
// 查看
const dialogFlieRef = ref()
const handleShowMessage = (title: string, flietype: string, id: number) => {
  dialogFlieRef.value.open(title, flietype, id)
}
// 上传方法
const handleImport = (type: string) => {
  fileType.value = type
  fileLimit.value = type === 'file' ? 5 : 1
  formImgRef.value.open()
}
const handleUploadSuccess = (fileList) => {
  let fileArr =
    fileList && fileList.length > 0
      ? fileList.map((item) => {
          return {
            url: item.response.data,
            name: item.name
          }
        })
      : []
  if (fileType.value === 'file') {
    formData.value.attachments = formData.value.attachments.concat(...fileArr)
  } else if (fileType.value === 'img') {
    formData.value.showImg = fileArr
    formRef.value.validateField('showImg')
  }
}
const handleDelete = async (type: string, index: number = 0) => {
  await message.delConfirm()
  formData.value[type].splice(index, 1)
  await message.success(t('common.delSuccess'))
}
/** 打开弹窗 */
const ATT08 = ref()
// const ATT12 = ref()
const open = async (obj: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('底稿关联')
  formData.value.projectId = obj.projectId
  formData.value.id = obj.id
  // 修改时，设置数据
  formLoading.value = true
  try {
    getSelectionTab(obj.id)
    await getDicList(obj.projectId)
    selectionTab(list.value)
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
const getDicList = async (projectId: string) => {
  dicList.value = getStrDictOptions('audit_working_relation_file')
  radio1.value = dicList.value[0].value
  // let obj={
  //   fileType:'',
  //   projectId:projectId,
  // }
  let promiseAll = dicList.value.map((ele) => {
    return WorkingPaperApi.projResource(ele.value.split('_'), projectId)
  })
  // const data = await WorkingPaperApi.projResource('ATT08', projectId)
  // const data1 = await WorkingPaperApi.projResource('ATT12', projectId)
  const data = await Promise.all(promiseAll)
  list.value = data[0]
  list1.value = data[1]
  console.log(data)
}
const getSelectionTab = async (id: string) => {
  const data1 = await WorkingPaperApi.getMaterialByMattersid(id)
  ATT08.value = data1.map((ele) => {
    return ele.fileReleId
  })
}

const selectionTab = (list: any) => {
  // return
  list.map((ele) => {
    console.log(ATT08.value, ele.id, '3333333333')

    if (ATT08.value.includes(ele.id)) {
      nextTick(async () => {
        if (ele.fileType === dicList.value[0].value) {
          ;(await ele) && ATT08Ref.value!.toggleRowSelection(ele, true)
        } else {
          ;(await ele) && ATT12Ref.value!.toggleRowSelection(ele, true)
        }
      })
    }
  })
}
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  const obj = {
    mattersId: formData.value.id,
    assignAttaRespVOS: radio1.value === dicList.value[0].value?selectionProjAuditList.value:selectionProjResourceList.value
  }
  if (!obj.assignAttaRespVOS) {
    message.error('请选择要关联的材料')
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    console.log(obj)
    // return
    await WorkingPaperApi.releMaterial(obj)
    message.success(t('关联成功'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
onMounted(() => {
  // audit_working_relation_file
})
</script>
<style lang="scss" scoped>
.title {
  font-weight: 900;
  font-size: 16px;
  margin-bottom: 16px;
  margin-top: 16px;
  display: flex;
  align-items: center;
}
</style>
