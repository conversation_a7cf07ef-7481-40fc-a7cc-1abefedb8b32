/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-12-14 14:09:33
 * @Description: 画像指标管理API =>
 */
import request from '@/config/axios'

// 画像指标管理 VO
export interface PortraitManagementVO {
	/*主键 */
    id?: number;
    /*指标编码 */
    indicatorCode: string;
    /*指标名称 */
    indicatorName: string;
    /*指标含义 */
    indicatorDescription: string;
    /*指标属性*/ 
    metricAttributes: string;
    /*计算公式 */
    calculationFormula: string;
    /*指标分类 */
    indicatorCategory: number;
    /*指标分类名称 */
    indicatorCategoryName: string;
    /*指标权重 */
    indicatorWeight: number;
    /*启用状态 (启用/禁用) */
    enabledStatus: string;
    /*所属模块ID */
    moduleId: number;
    /*所属模块名称 */
    moduleName: string;
    /*执行的查询语句*/
    queryStatements: string;
}
export interface PortraitManagementDetailVO {
	id: number, 
    projectId: string, //项目ID
    questionCode: number,//问题编号id
    questionName: string,// 问题标题
    quesDigest: string,// 问题摘要
    auditSugg: string,// 审计建议
    quesTypeId: number,// 问题类型id
    quesTypeName: string //问题类型
    discoverUserId: number,// 发现人id
    discoverUserName: string, //发现人
    abarbeitungFlag: number,// 是否整改
    handOverFlag: number,// 是否移交
    createdBy: undefined, //创建人
    createdTime: undefined, //创建时间
    updatedBy: undefined, //更新人
    updatedTime: Date, //更新时间
}


// 画像指标管理 API
export const PortraitManagementApi = {
	// 查询画像指标管理列表分页
	PortraitManagementList: async (params: any) => {
		return await request.get({ url: `/model/evaluation-indicators/page`, params })
	},

    // 新增画像指标管理
	createPortraitManagement: async (data: PortraitManagementVO) => {
		return await request.post({ url: `/model/evaluation-indicators/create`, data })
	},

    // 编辑画像指标管理
	updatePortraitManagementList: async (data: PortraitManagementVO) => {
		return await request.put({ url: `/model/evaluation-indicators/update`, data })
	},

    // 画像指标管理详情
	detailsPortraitManagementList: async (id: number) => {
		return await request.get({ url: `/model/evaluation-indicators/get?id=` + id })
	},

    // 删除画像指标管理
	deletePortraitManagementList: async (id: number) => {
		return await request.delete({ url: `/model/evaluation-indicators/delete?id=` + id })
	},
}