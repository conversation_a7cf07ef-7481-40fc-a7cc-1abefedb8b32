import request from '@/config/axios'

// 案例库 VO
export interface CasebaseVO {
	id ?: number // 主键，唯一标识
	docNum : string // 文号
	publishMain : string //发布主体
	lawName : string //法律法规名称
	lawStatus : number //法律法规状态 0启用中 1已停用
	effeDate : string //生效日期
	publishDate : string //发布日期
}
export interface CasebaseDetailVO {
	id ?: number // 主键，唯一标识
	docNum : string // 文号
	publishMain : string //发布主体
	lawName : string //法律法规名称
	lawStatus : number //法律法规状态 0启用中 1已停用
	effeDate : string //生效日期
	publishDate : string //发布日期
	adder : string
	createTime : Date
}

// 案例库 API
export const CasebaseApi = {
	// 查询案例库列表分页
	getCasebaseList: async (params : any) => {
		return await request.get({ url: `/audit/tank-programmes/page`, params })
	},
	// 获得所有模版
	getSelectionList: async (params : any) => {
		return await request.get({ url: `/audit/tank-programmes/page`, params })
	},

	// 查询案例库详情
	getCasebase: async (id : number) => {
		return await request.get({ url: `/system/tank-programmes/get?id=` + id })
	},

	// 新增案例库
	createCasebase: async (data : CasebaseVO) => {
		return await request.post({ url: `/audit/tank-programmes/create`, data })
	},

	// 修改案例库
	updateCasebase: async (data : CasebaseVO) => {
		return await request.put({ url: `/system/tank-programmes/update`, data })
	},

	// 删除案例库
	deleteCasebase: async (id : number) => {
		return await request.delete({ url: `/system/tank-programmes/delete?id=` + id })
	},

	// 导出案例库 Excel
	exportCasebase: async (params : any) => {
		return await request.download({ url: `/system/tank-programmes/export-excel`, params })
	}
}

export interface CasebaseNew {
	id ?: number // 主键，唯一标识
	caseNo: string, //案例编码
	caseName: string, //案例名称
	writeBy: string, //填报人
	writeById: number, //填报人id
	writeDept: string, //填报单位
	writeDeptId: number, //填报单位id
	suitAudit: [], //适用审计事项
	illustrate: string, //案例说明
	fileIds: [], //文件信息集合

	shareFlag: 0, //共享状态
	outstandFlag: 0, //优秀案例
	status: 1, //案例状态
}

// 审计案例库
export const CasebaseApiNew = {
	// 获取审计案例库分页
	getCasebaseList: async (params : any) => {
		return await request.get({ url: `/audit/tank-case-lib/page`, params })
	},

	// 查询案例库详情
	getCasebase: async (id : number) => {
		return await request.get({ url: `/audit/tank-case-lib/get?id=` + id })
	},

	// 新增审计案例库
	createCasebaseNew: async (data: CasebaseNew) => {
		return await request.post({ url: `/audit/tank-case-lib/create`, data})
	},

	// 修改案例库
	editCasebaseNew: async (data: CasebaseNew) => {
		return await request.put({ url: `/audit/tank-case-lib/update`, data})
	},

	// 删除
	deleteCasebaseNew: async (id: number) => {
		return await request.delete({ url: `/audit/tank-case-lib/delete?id=` + id})
	},

	// 导出操作
	exportCasebaseNew: async (params: any) => {
		return await request.download({ url: `/audit/tank-case-lib/export-excel`, params})
	},

	// 获取用户数据
	getUserData: async (params : any) => {
		return await request.get({ url: `/system/user/list-all-simple`, params})
	},
}