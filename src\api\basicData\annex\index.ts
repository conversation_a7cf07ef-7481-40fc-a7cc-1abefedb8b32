import request from '@/config/axios'

// 附件类型 VO
export interface AnnexVO {
	id ?: number // 主键，唯一标识
	attTypeCode : string // 附件类型编码
	attTypeName : string // 附件类型名称
	documentType: number
	uploadFlag: number
	mustPass : number // 是否比传0否， 1是
	orderNum : number //排序
	status : number //状态
	orgName : string // 组织机构名称（公司名称）
}
export interface AnnexDetailVO {
	id : number // 主键，唯一标识
	attTypeCode : string // 附件类型编码
	attTypeName : string // 附件类型名称
	documentType: number
	uploadFlag: number
	mustPass : number // 是否比传0否， 1是
	orderNum : number //排序
	status : number //状态
	orgName : string // 组织机构名称（公司名称）
	adder : string
	createTime : Date
	creatorName: string
}

// 附件类型 API
export const AnnexApi = {
	// 查询附件类型列表分页
	getAnnexList: async (params : any) => {
		return await request.get({ url: `/system/attachment-type/page`, params })
	},
	// 所以附件类型
	getAnnexAll: async () => {
	    return await request.get({ url: `/system/attachment-type/getAll` })
	},

	// 查询附件类型详情
	getAnnex: async (id : number) => {
		return await request.get({ url: `/system/attachment-type/get?id=` + id })
	},

	// 新增附件类型
	createAnnex: async (data : AnnexVO) => {
		return await request.post({ url: `/system/attachment-type/create`, data })
	},

	// 修改附件类型
	updateAnnex: async (data : AnnexVO) => {
		return await request.put({ url: `/system/attachment-type/update`, data })
	},

	// 删除附件类型
	deleteAnnex: async (id : number) => {
		return await request.delete({ url: `/system/attachment-type/delete?id=` + id })
	},

	// 导出附件类型 Excel
	exportAnnex: async (params : any) => {
		return await request.download({ url: `/system/attachment-type/export-excel`, params })
	}
}