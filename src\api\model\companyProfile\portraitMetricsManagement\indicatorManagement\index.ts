import request from '@/config/axios'

// 企业综合评价标准值维护 API
export const IndicatorManagementApi = {
  // 查询企业综合评价标准值维护列表分页
  getEvalStandardsList: async (params: any) => {
    return await request.get({ url: `/model/ods/eval-standards/page`, params })
  },

  // 新增企业综合评价标准值维护
  createEvalStandards: async (data: any) => {
    return await request.post({ url: `/model/ods/eval-standards/create`, data })
  },
  // 删除企业综合评价标准值维护
  deleteEvalStandards: async (id: any) => {
    return await request.delete({ url: `/model/ods/eval-standards/delete?id=${id}` })
  },
  // 更新企业综合评价标准值维护列表启用状态
  updateEvalStandards: async (data: any) => {
    return await request.put({ url: `/model/ods/eval-standards-link/update`, data })
  },
  // 编辑接口详情
  getEvalStandards: async (id: any) => {
    return await request.get({ url: `/model/ods/eval-standards-link/get?id=${id}` })
  }
}
