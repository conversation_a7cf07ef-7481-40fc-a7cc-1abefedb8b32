import request from '@/config/axios'



// 整改销号 API
export const reformingCancellaApi = {
	// 查询整改销号列表分页
	getReformingCancellaList: async (params : any) => {
		return await request.get({ url: `/manage/rectific-destruct-info/page`, params })
	},

	// 查询整改销号详情
	getReformingCancella: async (data : any) => {
		return await request.post({ url: `/manage/rectific-destruct-info/detail-page` , data })
	},

  // 整改销号获取详情接口
  getProjectInitiationMaterialDetail: async (id : string,questionId:string,measuresId:string) => {
    return await request.get({ url: `/manage/notice-rectification/detail?id=${id}&questionId=${questionId}&measuresId=${measuresId}` })
  },
	// 整改销号获取详情接口2
	getProjectInitiationMaterialDetail2: async (id : string,questionId:string) => {
		return await request.get({ url: `/manage/notice-rectification/detail?id=${id}&questionId=${questionId}` })
	},
	// 整改销号提交接口
	saveNoticeRectificInfo: async (data:any) => {
		return await request.post({ url: `/manage/rectific-destruct-info/batch-create`,data })
	},
	// 整改销号获取详情复核
	getCheckDescAndStatus: async (noticeId:string,questionId:string) => {
		return await request.get({ url: `/manage/rectific-destruct-info/show/${noticeId}/${questionId}` })
	},

	
	// 导出整改销号详情
	getQuestionReformingPlan: async (params : any) => {
		return await request.download({ url: `/audit/rectific-destruct-info/export-excel`, params })
	},
}
