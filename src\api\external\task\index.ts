import request from '@/config/axios'

export type TaskVO = {
  id: number
}

export const approveTask = async (data: any,token: string) => {
  return await request.put({ url: '/bpm/task/approve',
    data,
    headers: {"user-external" :token}
  })
}

export const rejectTask = async (data: any,token: string) => {
  return await request.put({ url: '/bpm/task/reject', data,
    headers: {"user-external" :token}
  })
}

export const getTaskListByProcessInstanceId = async (processInstanceId: string,token: string) => {
  return await request.get({
    url: '/bpm/task/list-by-process-instance-id?processInstanceId=' + processInstanceId,
    headers: {"user-external" :token}
  })
}


// 获取所有可回退的节点
export const getTaskListByReturn = async (id: string,token: string) => {
  return await request.get({ url: '/bpm/task/list-by-return', params: { id },
    headers: {"user-external" :token}
  })
}

// 回退
export const returnTask = async (data: any,token: string) => {
  return await request.put({ url: '/bpm/task/return', data,
    headers: {"user-external" :token}
  })
}

// 委派
export const delegateTask = async (data: any,token: string) => {
  return await request.put({ url: '/bpm/task/delegate', data,
    headers: {"user-external" :token}
  })
}

// 转派
export const transferTask = async (data: any,token: string) => {
  return await request.put({ url: '/bpm/task/transfer', data,
    headers: {"user-external" :token}
  })
}

// 加签
export const signCreateTask = async (data: any,token: string) => {
  return await request.put({ url: '/bpm/task/create-sign', data,
    headers: {"user-external" :token}
  })
}

// 减签
export const signDeleteTask = async (data: any) => {
  return await request.delete({ url: '/bpm/task/delete-sign', data })
}

// 获取减签任务列表
export const getChildrenTaskList = async (id: string) => {
  return await request.get({ url: '/bpm/task/list-by-parent-task-id?parentTaskId=' + id })
}
