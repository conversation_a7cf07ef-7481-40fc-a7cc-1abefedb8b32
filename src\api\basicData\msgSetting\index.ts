import request from '@/config/axios'

// VO
export interface SettingReqVO {
  id: number // ID
  nodeId: string // 环节ID
  nodeName: string // 环节名称
  nodeCode: string  //环节编码
  timeValue: string // 设置时间
  timeUnit: string  // 时间单位：1秒，2：分，3：时，4天，5：月，6年
  templateCode: string
}

//环节设置时间 API
export const  settingApi = {
  // 查询新闻管理分页
  getPage: async (params: any) => {
    return await request.get({ url: `/system/node-message-settings/page`, params })
  },

  // 查询详情
  getDetail: async (id: number) => {
    return await request.get({ url: `/system/node-message-settings/get?id=` + id })
  },

  // 新增
  createSetting: async (data: SettingReqVO) => {
    return await request.post({ url: `/system/node-message-settings/create`, data })
  },

  getList: async (params: any) => {
    return await request.get({ url: `/system/audit-procedure/search-list`, params })

  },


  // 修改
  updateNews: async (data: SettingReqVO) => {
    return await request.put({ url: `/system/node-message-settings/update`, data })
  },
}
