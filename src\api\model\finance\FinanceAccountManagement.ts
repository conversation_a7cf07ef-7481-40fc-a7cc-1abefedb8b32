import request from '@/config/axios'

export const FinanceAccountManagementAPI = {

  /**
   * 获取财务科目分页数据
   * @param data
   * @param params
   */
  financeAccountPageData: async (data: any, params: any) => {
    return request.post({
      url: '/model/financeAccountManagement/financeAccountPageData',
      data: data,
      params: params
    })
  },

  /**
   * 获取目标财务科目数据
   * @param id
   */
  targetFinanceAccountData: async (id: string) => {
    return request.post({
      url: `/model/financeAccountManagement/targetFinanceAccountData/${id}`
    })
  },

  /**
   * 保存财务科目数据
   * @param data
   */
  saveFinanceAccountData: async (data: any) => {
    return request.post({
      url: '/model/financeAccountManagement/saveFinanceAccountData',
      data: data
    })
  },

  /**
   * 删除目标财务科目数据
   * @param id
   */
  deleteTargetFinanceAccountData: async (id: string) => {
    return request.post({
      url: `/model/financeAccountManagement/deleteTargetFinanceAccountData/${id}`
    })
  },

  /**
   * 导出财务科目数据
   * @param data
   */
  exportFinanceAccountData: async (data: any) => {
    return await request.download({
      url: '/model/financeAccountManagement/exportFinanceAccountData',
      params: data
    })
  },

  /**
   * 获取财务科目树数据
   */
  financeAccountTreeData: async () => {
    return request.post({
      url: '/model/financeAnalysisCommonParameter/financeAccountTreeData'
    })
  }

}
