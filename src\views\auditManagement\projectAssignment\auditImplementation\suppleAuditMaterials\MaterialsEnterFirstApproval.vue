<template>
  <el-row class="flex flex-col" v-loading="formLoading">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="110px" class="w-100%">
      <el-row>
        <el-col :span="16" class="text-right">
          <el-form-item label="截止日期" prop="endTime">
            <el-date-picker
              v-model="formData.endTime"
              value-format="x"
              disabled
              type="date"
              class="!w-200px"
              placeholder="请选择截止日期"
            />
          </el-form-item>
          <el-form-item label="相关要求" prop="relevantRequ">
            <el-input
              v-model="formData.relevantRequ"
              placeholder="请输入相关要求"
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <div class="mb-10px mt-10px flex_center">
        <div class="flex_center fles_interval title common-border-left-blue"> 资料清单填报 </div>
      </div>
      <el-table
        :data="formData.materialList"
        row-key="id"
        ref="multipleTableRef"
        @selection-change="handleSelectionChange"
        :stripe="true"
        :show-overflow-tooltip="true"
      >
        <el-table-column type="selection" width="40" />
        <el-table-column label="#" type="index" width="50" />
        <el-table-column label="资料名称" align="center" prop="materialName" min-width="120">
          <template #default="scope">
            <el-form-item
              :prop="'materialList.' + scope.$index + '.materialName'"
              :rules="formRules.materialName"
              label-width="0px"
            >
              {{ scope.row.materialName }}
              <!-- <el-input v-model="scope.row.materialName" placeholder="请输入资料名称" /> -->
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="资料类型" align="center" prop="resourceCode">
          <template #default="scope">
            <el-form-item
              :prop="'materialList.' + scope.$index + '.resourceCode'"
              :rules="formRules.resourceCode"
              label-width="0px"
            >
              {{ getDictLabel('wel_check_file_type', scope.row.resourceCode) }}
              <!-- <el-select
                v-model="scope.row.resourceCode"
                placeholder="请选择"
                disabled
                style="width: 100%"
              >
                <el-option
                  v-for="dict in getIntDictOptions('wel_check_file_type')"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select> -->
              <!-- <el-input v-model="scope.row.resourceName" placeholder="请输入资料类型" /> -->
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="是否有模版" align="center" prop="templateFlag" min-width="100">
          <template #default="scope">
            <el-form-item
              :prop="'materialList.' + scope.$index + '.templateFlag'"
              :rules="formRules.templateFlag"
              label-width="0px"
            >
              <!-- {{ scope.row.templateFlag }} -->
              <el-select
                v-model="scope.row.templateFlag"
                placeholder="是否有模版"
                disabled
                style="width: 100%"
              >
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_IS_OR_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="是否提供" align="center" prop="provideFlag">
          <template #default="scope">
            <el-form-item
              :prop="'materialList.' + scope.$index + '.provideFlag'"
              :rules="formRules.provideFlag"
              label-width="0px"
            >
              <el-select v-model="scope.row.provideFlag" placeholder="是否提供" style="width: 100%">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_IS_OR_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="未提供原因说明" align="center" prop="noProvideReason">
          <template #default="scope">
            <el-form-item
              :prop="'materialList.' + scope.$index + '.noProvideReason'"
              :rules="formRules.noProvideReason"
              label-width="0px"
            >
              <el-input v-model="scope.row.noProvideReason" :disabled="scope.row.provideFlag == 1" placeholder="请输入未提供原因说明" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="相关联系人" align="center" prop="linkUserName" min-width="100">
          <template #default="scope">
            <el-form-item
              :prop="'materialList.' + scope.$index + '.linkUserName'"
              :rules="formRules.linkUserName"
              label-width="0px"
            >
              {{ scope.row.linkUserName }}
              <!-- <el-input v-model="scope.row.linkUserName" placeholder="请输入相关联系人" /> -->
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="电话" align="center" prop="tel">
          <template #default="scope">
            <el-form-item
              :prop="'materialList.' + scope.$index + '.tel'"
              :rules="formRules.tel"
              label-width="0px"
            >
              {{ scope.row.tel }}
              <!-- <el-input v-model="scope.row.tel" placeholder="请输入电话" /> -->
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" :width="150" fixed="right">
          <template #default="scope">
            <!-- <el-button type="primary" link @click="handleProfileEditor(scope.row.id)"
										v-hasPermi="['system:user:update']">编辑</el-button> -->
            <el-button
              type="primary"
              link
              v-show="scope.row.templateFlag == 1"
              @click="handleTemplateDownload(scope.row)"
              >下载</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleImportFile(scope.row)"
              >上传</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="detail-title common-border-left-blue">
        <span>项目基本信息</span>
      </div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="项目名称">{{ detailData.projectName }}</el-descriptions-item>
        <el-descriptions-item label="项目编号">{{ detailData.projectNo }}</el-descriptions-item>
        <el-descriptions-item label="审计类型">{{ detailData.auditTypeDesc }}</el-descriptions-item>
        <el-descriptions-item label="立项年度">{{ detailData.projectYear }}</el-descriptions-item>
        <el-descriptions-item label="组织方式">
          <dict-tag :type="DICT_TYPE.ORGANIZATION_TYPE" :value="detailData.orgType" />
        </el-descriptions-item>
        <el-descriptions-item label="时间计划">{{ detailData.timeSchedule }}</el-descriptions-item>
        <el-descriptions-item label="审计期间">{{ detailData.auditPeriod }}</el-descriptions-item>
        <el-descriptions-item label="开展事项">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.specialFlag" />
        </el-descriptions-item>
        <el-descriptions-item label="整体报告">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.overallReportFlag" />
        </el-descriptions-item>
        <el-descriptions-item label="公司领导">{{ detailData.companyLeader }}</el-descriptions-item>
        <el-descriptions-item label="曾任职务">{{ detailData.previouWork }}</el-descriptions-item>
        <el-descriptions-item label="发文编号">{{ detailData.docNum }}</el-descriptions-item>
        <el-descriptions-item label="是否境外">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.overseasFlag" />
        </el-descriptions-item>
        <el-descriptions-item label="是否重要">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.significantFlag" />
        </el-descriptions-item>
        <el-descriptions-item label="实施单位">{{
          detailData.implementDeptName
        }}</el-descriptions-item>
        <el-descriptions-item label="立项依据" :span="4">{{
          detailData.projectGist
        }}</el-descriptions-item>
      </el-descriptions>
      <div class="detail-title common-border-left-blue">
        <span>审计对象信息</span>
      </div>
      <!-- <el-row>
        <el-col :span="24" class="text-right">
          <el-button type="primary" plain @click="showTeamDetail(formData.id,'')">查看小组信息</el-button>
        </el-col>
      </el-row> -->
      <el-table
        border
        :data="detailData.auditTargetList"
        :stripe="true"
        :show-overflow-tooltip="true"
      >
        <el-table-column label="#" type="index" width="50" />
        <el-table-column label="项目编号" align="left" prop="projectNo" min-width="180" />
        <el-table-column label="项目名称" align="left" prop="projectName" min-width="260" />
        <el-table-column label="审计对象" align="left" prop="auditTarget" min-width="180" />
        <el-table-column label="组长" align="center" prop="groupLeader" min-width="120" />
        <el-table-column label="项目状态" align="center" prop="projectStatus" min-width="88" />
        <el-table-column label="操作" align="center" :width="180" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="showTeamDetail('', scope.row.id)"
              >小组信息</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="mb-10px mt-10px flex_center">
        <div class="flex_center fles_interval title common-border-left-blue"> 迎审资料清单 </div>
        <!-- <div>
          <el-button type="primary" plain v-if="props.isApprove" @click="handleImport('file', 1)">
            <Icon icon="ep:upload" class="mr-5px" /> 附件上传
          </el-button>
        </div> -->
      </div>
      <el-table :data="fileList" :stripe="true" :show-overflow-tooltip="true">
        <el-table-column label="#" type="index" width="50" />
        <el-table-column label="文档名称" align="left" prop="fileName" min-width="180" />
        <el-table-column label="文档类型" align="center" prop="fileTypeName" min-width="120" />
        <el-table-column label="编制单位" align="center" prop="deptName" min-width="180" />
        <el-table-column label="编制人" align="center" prop="creatorName" />
        <el-table-column label="生成时间" align="center" prop="createTime">
          <template #default="{ row }">
            <span>{{ formatDate(row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" :width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="handleDownload(scope.row?.fileUrl, scope.row?.fileName)"
              v-hasPermi="['system:user:update']"
              >下载</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleShowMessage(scope.row.fileName, 'VIEW', scope.row.fileId)"
              v-hasPermi="['system:user:update']"
              >查看</el-button
            >
            <!-- <el-button
              type="danger"
              link
              @click="handleFileDelete('projectAssignAttaRespVOList', scope.$index)"
              v-hasPermi="['system:user:update']"
              >删除</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <div class="flex flex-justify-end">
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
    </div>
  </el-row>

  <!-- 文件上传 -->
  <FileForm ref="formImgRef" @success="handleUploadSuccess" :type="fileType" />
  <!-- 审计小组 -->
  <TeamDetail ref="teamDetailRef" />
  <!-- 导入 -->
  <ImportForm ref="importFormRef" @success="handleData" />
  <DialogFlie ref="dialogFlieRef" />
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE, getDictLabel } from '@/utils/dict'
import { ProjectDataListApi } from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectDataList'
import FileForm from '@/views/infra/file/FileForm.vue'
import TeamDetail from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/TeamDetail.vue'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import type { TableInstance } from 'element-plus'
import { DialogFlie } from '@/components/DialogFlie'
import { formatDate } from '@/utils/formatTime'
import { formatTime } from '@/utils'
import { propTypes } from '@/utils/propTypes'
import { ProjectApprovalApi } from '@/api/auditManagement/reviewManagement/projectApproval'
import { suppleAuditMaterialsApi } from '@/api/auditManagement/projectAssignment/auditImplem/suppleAuditMaterials'
import ImportForm from '@/views/auditManagement/projectAssignment/PreTrialPreparation/projectDataList/ImportForm.vue'
import download from '@/utils/download'
defineOptions({ name: 'MaterialsEnterApprovalFirst' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { wsCache } = useCache()
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const detailData = ref({
  projectName: undefined,
  projectNo: undefined,
  id: undefined,
  auditTypeDesc: undefined,
  projectYear: undefined,
  orgType: undefined,
  planStart: undefined,
  planEnd: undefined,
  auditPeriod: undefined,
  specialFlag: undefined,
  overallReportFlag: undefined,
  companyLeader: undefined,
  previouWork: undefined,
  docNum: undefined,
  overseasFlag: undefined,
  significantFlag: undefined,
  implementDeptName: undefined,
  projectGist: undefined,
  auditTargetList: [],
  auditGroupList: []
})
const formData = ref({
  id: undefined,
  endTime: undefined,
  relevantRequ: undefined,
  materialList: [],
  supplementalInput: true,
  projectAssignAttaRespVOList: []
})
const formRules = reactive({
  provideFlag: [{ required: true, message: '请选择是否提供', trigger: 'blur' }]
  // endTime: [{ required: true, message: '截止日期不能为空', trigger: 'change' }]
  // provideFlag: [{ required: true, message: '是否提供为空', trigger: 'change' }],
  // noProvideReason: [{ required: true, message: '未提供原因说明为空', trigger: 'change' }],
  // linkUserName: [{ required: true, message: '相关联系人不能为空', trigger: 'change' }],
  // tel: [{ required: true, message: '电话不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

const selectFileList = ref([])
const handleSelectionChange = (val: []) => {
  console.log(val)
  selectFileList.value = val
}
const props = defineProps({
  id: propTypes.number.def(undefined),
  isApprove: propTypes.bool.def(),
  formVariables: {
    default: () => ({}),
    type: Object
  },
  saveAndSubmit: {
    default: false,
    type: Boolean
  }
})
 watch(
  () => props.saveAndSubmit,
  (newVal) => {
    if (newVal) {
      submitForm()
    }
  }
)
/** 打开弹窗 */
const open = async (id: string) => {
  // dialogVisible.value = true
  // dialogTitle.value = t('录入')
  // resetForm()
  formData.value.id = id

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      // 项目基本信息
      // detailData.value = await ProjectDataListApi.getProjectInitiationDetail(id)
      const data = await ProjectDataListApi.getProjectInitiationMaterialDetail3(id, '1')
      detailData.value = data

      await getWellCheckFileList()
      formData.value = data
      formData.value.materialList = data.materialDetailList
      // formData.value = {
      //   endTime: data.endTime,
      //   relevantRequ: data.relevantRequ,
      //   materialList: data.materialDetailList,
      //   projectAssignAttaRespVOList: data.projectAssignAttaRespVOList
      // }
      formData.value.resourceId = detailData.value.resourceId
    } finally {
      formLoading.value = false
    }
  }
}

// 项目资料清单多选
const multipleTableRef = ref<TableInstance>()
const handleData = (val) => {
  formData.value.materialList = val
}
// 模板下载
const handleTemplateDownload = async (row) => {
  console.log(row.fileUrl)
  download.downFileByFileUrl(row.fileUrl, '资料清单填报模板')
}
// 上传
const formImgRef = ref()
const fileType = ref('img')
const radioType = ref()
const radioListDic = ref()
const rowId = ref()
// 上传方法
const handleImportFile = (row: any) => {
  console.log(row, 'row')

  formImgRef.value.open()
  fileType.value = 'all'
  radioType.value = row.resourceCode
  rowId.value = row.id
  radioListDic.value = 'wel_check_file_type'
}
// 附件上传
const handleUploadSuccess = async (fileList) => {
  console.log(radioType.value)
  const uploadPromises = fileList.map(async (file) => {
    await suppleAuditMaterialsApi.bindWelCheckFile({
      fileId: file?.response.data.id,
      resourceDetailLinkId: rowId.value,
      projectId: detailData.value.id,
      fileType: radioType.value,
      fileTypeName: getDictLabel('wel_check_file_type', radioType.value)
    })
  })
  await Promise.all(uploadPromises)
  await message.success('上传成功')
  await getWellCheckFileList()
}
// 查看小组信息
const teamDetailRef = ref()
const showTeamDetail = (parId?: number | string, sonId?: number | string) => {
  console.log(parId, sonId)
  teamDetailRef.value.open(parId, sonId)
}

const emit = defineEmits(['success', 'handleWatchStatus'])
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  console.log(formData.value)
  // return
  // 提交请求
  formLoading.value = true
  const data = formData.value
  data.supplementalInput = true

  await suppleAuditMaterialsApi.supplementalFlowSave(data)
  message.success(t('common.createSuccess'))
  formLoading.value = false
  emit('handleWatchStatus', 1)
  open(props.id)
  // 发送操作成功的事件
  // emit('success')
}
const fileList = ref([])
const getWellCheckFileList = async () => {
  fileList.value =
    (await ProjectApprovalApi.getCurrentUserWellCheckFileListByResourceId(
      detailData.value.resourceId,
      '1'
    )) || []
}
//预览编辑
const dialogFlieRef = ref()
const handleShowMessage = (title: string, flietype: string, id: number) => {
  dialogFlieRef.value.open(title, flietype, id)
}
// 下载
const handleDownload = (url: string, name: string) => {
  download.downFileByFileUrl(url, name)
}
onMounted(() => {
  emit('handleWatchStatus', 2)
  console.log(props)
  open(props.id)
})
onBeforeUnmount(() => {
  emit('handleWatchStatus', 1)
})
</script>
<style lang="scss" scoped>
.title {
  font-weight: 900;
  font-size: 16px;
  margin-bottom: 16px;
  margin-top: 16px;
  display: flex;
  align-items: center;
}

:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
  width: 80px;
}
</style>
