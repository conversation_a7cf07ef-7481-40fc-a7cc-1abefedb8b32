
<template>
  <Dialog
    :title="dialogTitle"
    :scroll="true"
    v-model="dialogVisible"
    width="80%"
    :loading="formLoading"
    maxHeight="72vh"
    setScrollTop="2vh"
  >
    <el-row :gutter="16">
      <el-col :span="24">
        <div class="detail-title common-border-left-blue">
          <span>项目基本信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="项目名称">{{ detailData.projectName }}</el-descriptions-item>
          <el-descriptions-item label="项目编号">{{ detailData.projectNo }}</el-descriptions-item>
          <el-descriptions-item label="审计类型">{{ detailData.auditTypeDesc}}</el-descriptions-item>
          <el-descriptions-item label="立项年度">{{ detailData.projectYear }}</el-descriptions-item>
          <el-descriptions-item
            label="组织方式"
          >{{getDictLabel('organization_type',detailData.orgType)}}</el-descriptions-item>
          <el-descriptions-item label="时间计划">{{detailData.timeSchedule }}</el-descriptions-item>
          <el-descriptions-item label="审计期间">{{ detailData.auditPeriod}}</el-descriptions-item>
          <el-descriptions-item label="开展事项">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.specialFlag" />
          </el-descriptions-item>
           <el-descriptions-item label="整体报告">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.overallReportFlag" />
          </el-descriptions-item>
          <el-descriptions-item label="公司领导">{{ detailData.companyLeader}}</el-descriptions-item>
          <el-descriptions-item label="曾任职务">{{ detailData.previouWork}}</el-descriptions-item>
          <el-descriptions-item label="发文编号">{{ detailData.docNum}}</el-descriptions-item>
          <el-descriptions-item label="是否境外">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.overseasFlag" />
          </el-descriptions-item>
          <el-descriptions-item label="是否重要">
            <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.significantFlag" />
          </el-descriptions-item>
          <el-descriptions-item label="实施单位">{{ detailData.implementDeptName}}</el-descriptions-item>
          <el-descriptions-item label="立项依据" :span="4">{{ detailData.projectGist}}</el-descriptions-item>
        </el-descriptions>
        <div class="detail-title common-border-left-blue">
          <span>审计对象信息</span>
        </div>
        <el-table
          border
          :data="detailData.auditTargetList"
          :stripe="true"
          :show-overflow-tooltip="true"
        >
          <el-table-column label="#" width="50" align="center">
            <template
              #default="{ $index }"
            >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
          </el-table-column>
          <el-table-column label="项目编号" align="left" prop="projectNo" min-width="180" />
          <el-table-column label="项目名称" align="left" prop="projectName" min-width="260" />
          <el-table-column label="审计对象" align="left" prop="auditTarget" min-width="180" />
          <el-table-column label="组长" align="center" prop="groupLeader" min-width="120" />
          <el-table-column label="项目状态" align="center" prop="projectStatus" min-width="88" />
          <el-table-column label="操作" align="center" :width="180" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="showTeamDetail('',scope.row.id)">查看小组信息</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="mb-10px flex_center">
          <div class="flex_center fles_interval common-border-left-blue">审计团队信息</div>
          <div>
            <el-button @click="openTemaList = !openTemaList" size="small" type="text" v-if="!openTemaList">
              展开
              <Icon icon="ic:round-expand-more" color="#2e4db5" size="20" class="ml-3px" />
            </el-button>
            <el-button @click="openTemaList = !openTemaList" size="small" type="text" v-else>
              收起
              <Icon icon="ic:round-expand-less" color="#2e4db5" size="20" class="ml-3px" />
            </el-button>
          </div>
        </div>
        <el-table
          border
          :data="detailData.auditGroupList"
          :stripe="true"
          :show-overflow-tooltip="true"
          v-if="openTemaList"
        >
          <el-table-column label="#" width="50" align="center">
            <template
              #default="{ $index }"
            >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
          </el-table-column>
          <el-table-column label="审计角色" align="center" prop="roleName" min-width="120" />
          <el-table-column label="项目成员" align="left" prop="userName" min-width="180" />
          <el-table-column label="身份类型" align="center" prop="identityName" min-width="120" />
          <el-table-column label="审计体系" align="center" prop="auditSystemName" min-width="120" />
          <el-table-column label="审计事项" align="left" prop="auditMatterName" min-width="180" />
        </el-table>
        <div class="mb-10px mt-10px flex_center">
          <div class="flex_center fles_interval title common-border-left-blue">审计访谈记录</div>
          <div>
            <el-button type="primary" plain @click="handleImportFile('all')">附件上传</el-button>
          </div>
        </div>
        <el-table
          :data="detailData.projectAssignAttaRespList || []"
          :stripe="true"
          :show-overflow-tooltip="true"
        >
          <el-table-column label="#" width="50" align="center">
            <template
              #default="{ $index }"
            >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
          </el-table-column>
          <el-table-column label="文档名称" align="left" prop="fileName" min-width="180" />
          <!-- <el-table-column label="文档类型" align="center" prop="fileTypeName" min-width="120" /> -->
          <el-table-column label="审计事项" align="center" prop="matterName" min-width="180" />
          <el-table-column label="审计角色" align="center" prop="roleName" min-width="180" />
          <el-table-column label="编制人" align="center" prop="creatorName" />
          <el-table-column label="生成时间" align="center" prop="createTime" min-width="120">
            <template #default="{row}">
              <span>{{ formatTime(row.createTime, 'yyyy-MM-dd') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" :width="200" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="handleDownload(scope.row?.fileUrl,scope.row?.fileName)"
              >下载</el-button>
              <el-button
                type="primary"
                link
                @click="handleShowMessage(scope.row.fileName, 'view', scope.row.fileId)"
              >预览</el-button>
              <el-button type="danger" link @click="handleDelete(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <template #footer>
      <el-button @click="submitForm(1)" type="primary" :disabled="formLoading">保存</el-button>
      <el-button @click="dialogVisible = false">取消</el-button>
    </template>
  </Dialog>
  <FileFormSelect
    ref="formImgRef"
    @success="handleUploadSuccess"
    :limit="fileLimit"
    :showRadioByInterface="true"
    :radioListByInterface="fileTypeList"
  />

  <TeamDetail ref="teamDetailRef" />
  <DialogFlie ref="dialogFlieRef" />
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE, getDictLabel } from '@/utils/dict'
import FileFormSelect from './FileFormSelect.vue'
import TeamDetail from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/TeamDetail.vue'
import { ProjectInitiationApi } from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectInitiation'
import { formatTime } from '@/utils'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { AuditInterviewApi } from '@/api/auditManagement/projectAssignment/auditImplem/auditInterview'
import { PreliminaryAuditApi } from '@/api/auditManagement/projectAssignment/auditReport/auditReportDraft'
import download from '@/utils/download'
import {  handleTree} from '@/utils/tree'
const { wsCache } = useCache()
import { DialogFlie } from '@/components/DialogFlie'
defineOptions({ name: 'InterviewEnter' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const detailData = ref({
  id: undefined,
  projectName: undefined,
  auditType: undefined,
  auditSencType: undefined,
  projectYear: undefined,
  orgType: undefined,
  timeSchedule: undefined,
  auditPeriod: undefined,
  specialFlag: undefined,
  overallReportFlag: undefined,
  companyLeader: undefined,
  previouWork: undefined,
  docNum: undefined,
  overseasFlag: undefined,
  significantFlag: undefined,
  implementDeptName: undefined,
  projectGist: undefined,
  auditGroupList: [],

  sameAuditGroupFlag: true,
  auditTargetReqVo: [],
  projectAttaVOList: [],
  assignProgConfSaveReqVOList: [],
  projectAssignAttaRespList: []
})

const formRef = ref() // 表单 Ref

const teamDetailRef = ref()
const showTeamDetail = (parId?: number | string, sonId?: number | string) => {
  teamDetailRef.value.open(parId, sonId)
}
const planFillingStatus = ref()

const formImgRef = ref()
const fileType = ref('img')
const fileLimit = ref(1)
const handleUploadSuccess = (fileList, radioType) => {
  console.log(radioType);
  
  let fileArr =
    fileList && fileList.length > 0
      ? fileList.map((item) => {
          return {
            // id: item.response.data.id,
            fileId: item.response.data.id,
            fileTypeName: fileTypeMap.get('attTypeName'),
            fileType: fileTypeMap.get('attTypeCode'),
            deptId: wsCache.get(CACHE_KEY.USER).user.deptId,
            roleName: currentRoleName.value,
            mainId: radioType,
            matterName: fileMap.get(radioType),
            deptName: wsCache.get(CACHE_KEY.USER).user.deptName,
            fileName: item.name,
            fileUrl: item.response.data.fileUrl,
            creatorName: item.response.data.creatorName,
            createTime: item.response.data.createTime
          }
        })
      : []
  console.log(fileArr)

  detailData.value.projectAssignAttaRespList = detailData.value.projectAssignAttaRespList ?? []
  detailData.value.projectAssignAttaRespList = detailData.value.projectAssignAttaRespList.concat(
    ...fileArr
  )
}
const handleDelete = async (id: number) => {
  await message.delConfirm()
  detailData.value.projectAssignAttaRespList = detailData.value.projectAssignAttaRespList.filter(
    (item) => item.id != id
  )
  await message.success(t('common.delSuccess'))
}
interface fileType {
  id: number
  label: string
  value: number
  name: string
}
const fileTypeList = ref<fileType[]>([])
const fileMap = new Map<number, string>()
const fileTypeMap = new Map()
const currentRoleName = ref('')
const getCurrentAuditRole = async (projectId: number) => {
  const data = await AuditInterviewApi.getAuditRoleName({ projectId })
  currentRoleName.value = data.roleName ?? ''
}
const openTemaList = ref(false)
/** 打开弹窗 */
const open = async (id?: number) => {
  openTemaList.value = false
  dialogVisible.value = true
  dialogTitle.value = t('录入')
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true 
    try { 
      await getCurrentAuditRole(id)
      await getFileTypeMap()
      detailData.value = await ProjectInitiationApi.getProjectBasicDetail(id)
      fileTypeList.value = await AuditInterviewApi.getAuditTypeListByType()

      fileTypeList.value.forEach((item) => {
        item.label = item.name
        item.value = item.id
        fileMap.set(item.id, item.name)
      })
      await getFileList(id)
      fileTypeList.value = handleTree(fileTypeList.value, 'id', 'parentId')
    } finally {
      formLoading.value = false
    }
  }
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
const getFileTypeMap = async () => {
  let data = await ProjectInitiationApi.getFileType('AL08')
  if (data && data.length > 0) {
    fileTypeMap.set('attTypeCode', data[0].attTypeCode)
    fileTypeMap.set('attTypeName', data[0].attTypeName)
  }
}
const getFileList = async (id: number) => {
  // let data = await PreliminaryAuditApi.getProfileList({
  //   pageNo: 1,
  //   pageSize: 100,
  //   projectId: id,
  //   fileTypes: fileTypeList.value.map((item) => item.attTypeCode)
  // })
  let data = await PreliminaryAuditApi.getProfileListNew({
    pageNo: 1,
    pageSize: 100,
    progCode: 'AL08',
    projectId: id
  })
  detailData.value.projectAssignAttaRespList = data
}
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  formLoading.value = true
  try {
    const data = {
      progId: detailData.value.id as any,
      fileList: detailData.value.projectAssignAttaRespList as any
    }
    await AuditInterviewApi.saveFilelist(data)
    message.success(t('common.createSuccess'))
    dialogVisible.value = false
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const handleDownload = (url: string, name: string) => {
  download.downFileByFileUrl(url, name)
}
/** 重置表单 */
const resetForm = () => {
  detailData.value = {
    id: undefined,
    projectName: undefined,
    auditType: undefined,
    auditSencType: undefined,
    projectYear: undefined,
    orgType: undefined,
    timeSchedule: undefined,
    auditPeriod: undefined,
    specialFlag: undefined,
    overallReportFlag: undefined,
    companyLeader: undefined,
    previouWork: undefined,
    docNum: undefined,
    overseasFlag: undefined,
    significantFlag: undefined,
    implementDeptName: undefined,
    projectGist: undefined,
    auditGroupList: [],
    sameAuditGroupFlag: true,
    auditTargetReqVo: [],
    projectAttaVOList: [],
    assignProgConfSaveReqVOList: [],
    projectAssignAttaRespList: []
  }
  formRef.value?.resetFields()
}
const dialogFlieRef = ref()
const handleShowMessage = (title: string, flietype: string, id: number) => {
  dialogFlieRef.value.open(title, flietype, id)
}
const handleImportFile = (type: string) => {
  formImgRef.value.open()
  fileType.value = type
}
</script>
<style lang="scss" scoped>
.title {
  font-weight: 900;
  font-size: 16px;
  margin-bottom: 16px;
  margin-top: 16px;
  display: flex;
  align-items: center;
}
:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
  width: 80px;
}
</style>
