import request from '@/config/axios'

// 档案管理 VO
export interface FileManagementVO {
	id ?: number // 主键，唯一标识
	/*创建人姓名 */
	creatorName : string;
	/*创建时间;创建时间 */
	createTime : Record<string, unknown>;
	/*更新人姓名 */
	updaterName : string;
	/*年度审计计划ID */
	auditPlanId : number;
	/*年度审计计划项目ID */
	planProjectId : number;
	/*项目年度 */
	auditYear : string;
	/*项目编码 */
	projectNo : string;
	/*项目名称 */
	projectName : string;
	/*审计类别 */
	auditType : string;
	/*审计类别显示文本 */
	auditTypeDesc : string;
	/*审计二级类型 */
	auditSencType : string;
	/*审计二级类型显示文本 */
	auditSencTypeDesc : string;
	/*项目属性 */
	projectAttr : string;
	/*项目属性显示文本 */
	projectAttrDesc : string;
	/*投资额(万元) */
	investmentAmount : string;
	/*审计范围 */
	auditScope : string;
	/*立项年度 */
	projectYear : string;
	/*组织方式 */
	orgType : string;
	/*时间安排 */
	timeSchedule : string;
	/*时间计划-开始 */
	planStart : Record<string, unknown>;
	/*时间计划-结束 */
	planEnd : Record<string, unknown>;
	/*审计期间 */
	auditPeriod : string;
	/*审计期间-开始 */
	auditPeriodStart : Record<string, unknown>;
	/*审计期间-结束 */
	auditPeriodEnd : Record<string, unknown>;
	/*开展专项标记 0不开展 1开展 */
	specialFlag : number;
	/*整体报告标记 */
	overallReportFlag : number;
	/*公司领导 */
	companyLeader : string;
	/*曾任职务 */
	previouWork : string;
	/*委托书文号 */
	docNum : string;
	/*是否境外 1是 */
	overseasFlag : number;
	/*是否重要 1是 */
	significantFlag : number;
	/*编制单位id */
	editDeptId : number;
	/*编制单位 */
	editDeptName : string;
	/*实施单位id */
	implementDeptId : number;
	/*实施单位 */
	implementDeptName : string;
	/*立项依据 */
	projectGist : string;
	/*项目立项流程id */
	setupProjFlowId : string;
	/*项目立项流程状态 */
	setupProjFlowStatus : string;
	/*状态 1已上报 */
	status : number;
	/*项目阶段 0已立项 1审前准备  2审计实施 3审计报告 4项目结项 */
	projStage : number;
	/*审计对象 */
	auditObject : string;
	/*是否下发 1是 */
	handOutFlag : number;
	/*下发时间 */
	handOutTime : Record<string, unknown>;
	/*实际结项日期 */
	actualCloseDate : Record<string, unknown>;
	/*是否归档 1是 */
	placeFileFlag : number;
	/*归档时间 */
	placeFileTime : Record<string, unknown>;
	/*调整类型 0未调整 1调整 2删除 3新增 */
	adjustType : number;
	/*中介机构id */
	intermediOrgId : number;
	/*中介机构 */
	intermediOrgName : string;
	/*档案编号 */
	archivNo : string;
	/*档案名称 */
	archivName : string;
}
export interface FileManagementDetailVO {
	id ?: number // 主键，唯一标识
	/*创建人姓名 */
	creatorName : string;
	/*创建时间;创建时间 */
	createTime : Record<string, unknown>;
	/*更新人姓名 */
	updaterName : string;
	/*年度审计计划ID */
	auditPlanId : number;
	/*年度审计计划项目ID */
	planProjectId : number;
	/*项目年度 */
	auditYear : string;
	/*项目编码 */
	projectNo : string;
	/*项目名称 */
	projectName : string;
	/*审计类别 */
	auditType : string;
	/*审计类别显示文本 */
	auditTypeDesc : string;
	/*审计二级类型 */
	auditSencType : string;
	/*审计二级类型显示文本 */
	auditSencTypeDesc : string;
	/*项目属性 */
	projectAttr : string;
	/*项目属性显示文本 */
	projectAttrDesc : string;
	/*投资额(万元) */
	investmentAmount : string;
	/*审计范围 */
	auditScope : string;
	/*立项年度 */
	projectYear : string;
	/*组织方式 */
	orgType : string;
	/*时间安排 */
	timeSchedule : string;
	/*时间计划-开始 */
	planStart : Record<string, unknown>;
	/*时间计划-结束 */
	planEnd : Record<string, unknown>;
	/*审计期间 */
	auditPeriod : string;
	/*审计期间-开始 */
	auditPeriodStart : Record<string, unknown>;
	/*审计期间-结束 */
	auditPeriodEnd : Record<string, unknown>;
	/*开展专项标记 0不开展 1开展 */
	specialFlag : number;
	/*整体报告标记 */
	overallReportFlag : number;
	/*公司领导 */
	companyLeader : string;
	/*曾任职务 */
	previouWork : string;
	/*委托书文号 */
	docNum : string;
	/*是否境外 1是 */
	overseasFlag : number;
	/*是否重要 1是 */
	significantFlag : number;
	/*编制单位id */
	editDeptId : number;
	/*编制单位 */
	editDeptName : string;
	/*实施单位id */
	implementDeptId : number;
	/*实施单位 */
	implementDeptName : string;
	/*立项依据 */
	projectGist : string;
	/*项目立项流程id */
	setupProjFlowId : string;
	/*项目立项流程状态 */
	setupProjFlowStatus : string;
	/*状态 1已上报 */
	status : number;
	/*项目阶段 0已立项 1审前准备  2审计实施 3审计报告 4项目结项 */
	projStage : number;
	/*审计对象 */
	auditObject : string;
	/*是否下发 1是 */
	handOutFlag : number;
	/*下发时间 */
	handOutTime : Record<string, unknown>;
	/*实际结项日期 */
	actualCloseDate : Record<string, unknown>;
	/*是否归档 1是 */
	placeFileFlag : number;
	/*归档时间 */
	placeFileTime : Record<string, unknown>;
	/*调整类型 0未调整 1调整 2删除 3新增 */
	adjustType : number;
	/*中介机构id */
	intermediOrgId : number;
	/*中介机构 */
	intermediOrgName : string;
	/*档案编号 */
	archivNo : string;
	/*档案名称 */
	archivName : string;
}

// 档案管理 API
export const FileManagementApi = {
	// 查询档案管理列表分页
	getFileManagementList: async (params : any) => {
		return await request.get({ url: `/manage/archiv/projectArchivPage`, params })
	},

	// 查询档案管理详情
	getFileManagement: async (id : number) => {
		return await request.get({ url: `/manage/archiv/getArchivDetail?id=` + id })
	},

	// 档案归档-档案归档-归档-保存
	confirmFileManagement: async (data : FileManagementVO) => {
		return await request.post({ url: `/manage/archiv/confirm`, data })
	},

	// 查询档案管理被移除附件列表分页
	getProjectFileList: async (params : any) => {
		return await request.get({ url: `/manage/archiv/getProjectFileList`, params })
	},
}