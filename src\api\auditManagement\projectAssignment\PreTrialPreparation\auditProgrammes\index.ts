import request from '@/config/axios'

// 审计方案 VO
export interface AuditProgrammesVO {
	id ?: number // 主键，唯一标识
	projectId : number // 年度计划项目ID
	templateId : number // 模板ID
	flowId : number // 审计方案-流程id
	flowStatus : number // 审计方案-流程状态
	adjustFlowId : number // 审计方案调整流程id
	adjustFlowStatus : number // 审计方案调整流程状态
	auditMattersSum : number // 审计事项数量
	draftSum : number // 底稿数量
	status : number //状态 0草稿1审核中 2已完成
	schemeCode : string //方案编码
	projectNo : string //项目编号
	projectName : string //项目名称
}
export interface AuditProgrammesDetailVO {
	id : number // 主键，唯一标识
	projectId : number // 年度计划项目ID
	templateId : number // 模板ID
	flowId : number // 审计方案-流程id
	flowStatus : number // 审计方案-流程状态
	adjustFlowId : number // 审计方案调整流程id
	adjustFlowStatus : number // 审计方案调整流程状态
	auditMattersSum : number // 审计事项数量
	draftSum : number // 底稿数量
	status : number //状态 0草稿1审核中 2已完成
	schemeCode : string //方案编码
	projectNo : string //项目编号
	projectName : string //项目名称

}

// 审计方案 API
export const AuditProgrammesApi = {
	// 查询审计方案列表分页
	getAuditProgrammesList: async (params : any) => {
		return await request.get({ url: `/manage/proj-audit-scheme/page`, params })
	},

	getAuditProgrammesListpage: async (params : any) => {
		return await request.post({ url: `/manage/proj-audit-scheme/list-page`, params })
	},

	getAuditProgrammesAll: async () => {
		return await request.get({ url: `/manage/proj-audit-scheme/getAll` })
	},

	// 查询审计方案详情
	getAuditProgrammes: async (id : number) => {
		return await request.get({ url: `/manage/proj-audit-scheme/get?id=` + id })
	},

	// 新增审计方案
	createAuditProgrammes: async (data : AuditProgrammesVO) => {
		return await request.post({ url: `/manage/proj-audit-scheme/create`, data })
	},

	// 修改审计方案
	updateAuditProgrammes: async (data : AuditProgrammesVO) => {
		return await request.put({ url: `/manage/proj-audit-scheme/update`, data })
	},

	// 删除审计方案
	deleteAuditProgrammes: async (id : number) => {
		return await request.delete({ url: `/manage/proj-audit-scheme/delete?id=` + id })
	},

	// 导出审计方案 Excel
	exportAuditProgrammes: async (params : any) => {
		return await request.download({ url: `/manage/proj-audit-scheme/export-excel`, params })
	},

	// 获得项目作业-审计方案-审计事项分页
	getAuditMattersList: async (params : any) => {
		return await request.get({ url: `/manage/proj-audit-matter/page`, params })
	},

	// 更新项目作业-审计方案-审计事项
	updateAuditMatters: async (data : AuditProgrammesVO) => {
		return await request.put({ url: `/manage/proj-audit-scheme/update`, data })
	},

	// 多条更新 项目作业-审计方案-审计事项
	updateAuditMatterslist: async (data : AuditProgrammesVO) => {
		return await request.put({ url: `/manage/proj-audit-matter/update-list`, data })
	},

	// 删除项目作业-审计方案-审计事项
	deleteAuditMatters: async (id : number) => {
		return await request.delete({ url: `/manage/proj-audit-matter/delete?id=` + id })
	},

	// 获得项目作业-审计方案-审计人员小组分页
	getAuditMattersUserList: async (params : any) => {
		return await request.post({ url: `/manage/audit-group/page`, params })
	},

	// 查询审计方案详情
	getAuditProgrammesDetail: async (id : number) => {
		return await request.get({ url: `/manage/proj-audit-scheme/detail?id=` + id })
	},

	// 生成审计方案
	createFileAuditProgrammes: async (id : number, templateId?: number) => {
		return await request.get({ url: `/manage/proj-audit-scheme/create-file?projectId=` + id + `&templateId=` + templateId })
	},

	// 审计方案-审计事项及存
	batchCreateMatterList: async (data : any) => {
		return await request.post({ url: `/manage/proj-audit-matter/batch-create`, data })
	},	

	// 通过审计方案获取审计事项
	getMattersList: async (id : number) => {
		return await request.get({ url: `/audit/tank-programmes/get-matters?id=` + id })
	},

	// 审计方案-审计人员及存
	batchCreateMatterUserList: async (data : any) => {
		return await request.post({ url: `/manage/assign-audit-matter-user/batch-create`, data })
	},
	
	// 疑点列表
	getAuditworkModelDoubtful: async (params : any) => {
		return await request.get({ url: `/manage/audit-work-manuscript/get-auditwork-model-doubtful`, params })
	},

	// 附件预览接口
	getFileBySchemeId: async (params: any) => {
		return await request.get({ url: `/manage/audit-implementation/getFileBySchemeId`, params })
	},
}