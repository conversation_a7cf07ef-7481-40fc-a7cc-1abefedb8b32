import request from '@/config/axios'

// 审计取证单

export interface AddVO {
    projectId: number
    fileList?: []
}
export interface SearchProjResourceVo {
    projectId: number
}
export interface createVo {
    id: undefined,
    auditOrgName: undefined,
    projectName: undefined,
    auditMatter: undefined,
    questionType: undefined,
    questionTitle: undefined,
    auditAbstract: undefined,
    auditSugg: undefined,
    auditUserName: '',
    questionAbstract: undefined,
    auditMatterId: undefined,
    editDate: Date,
    linkSaveReqVOList: []
}
interface codeFileListVo {
    fileType: string
    projectId: number
    mainIdList: Array<number>
}
// 审计通知书 API
export const AuditEvidenceSheetApi = {
    // 获取审计取证单列表
    getEvidenceSheetList: async (params: any) => {
        return await request.get({ url: '/manage-cert/page', params })
    },
    //新增-生成取证单-关联资料清单列表
    getProjResourceList: async (params: SearchProjResourceVo) => {
        return await request.get({ url: '/manage/proj-resource-detail/list', params })
    },
    //新增-保存
    createEvidenceSheet: async (data: createVo) => {
        return await request.post({ url: '/manage/manage-cert/create-general-file', data })
    },
    batchSubmit: async (data: any) => {
        return await request.post({ url: '/manage/manage-cert/submit', data })
    },
    // 详情页获得审计取证单列表
    getEvidenceDetailList: async (data: any) => {
        return await request.post({ url: '/manage/manage-cert/link-detail/list', data })
    },
    getPageList: async (params: any) => {
        return await request.get({ url: '/manage/manage-cert/link-project/page', params })
    },
    deleteEvidence: async (id: number) => {
        return await request.delete({ url: `/manage/manage-cert/delete?id=${id}` })
    },
    listLink: async (data: any) => {
        return await request.post({ url: '/manage/proj-audit-cert-link/link', data })
    },
    getFileListByIds: async (data: any) => {
        return await request.post({ url: '/manage/project-assign-atta/page/post', data })
    },
    getFileListByCodeList: async (data: codeFileListVo) => {
        return await request.post({ url: `/manage/project-assign-atta/getlist/by-mainid`, data })
    },
    // 审计取证单列表-关联-可关联资料清单
    getNotLinkList: async (params: any) => {
        return await request.get({ url: `/manage/project-assign-atta/notLinkList`, params })
    },

}
