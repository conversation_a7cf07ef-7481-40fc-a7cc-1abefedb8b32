<template>
	<Dialog :title="dialogTitle" :scroll="true" v-model="dialogVisible" width="80%" maxHeight="70vh"
		:loading='formLoading'>
		<el-row :gutter="16">
			<el-col :span="24">
				<div class="detail-title common-border-left-blue">
					<span>资料清单填报</span>
				</div>
				<div v-for="(item, index) in proSourceList" :key="index">
					<el-descriptions :column="2" border>
						<el-descriptions-item label="截止时间">{{ formatTime(item.endTime, 'yyyy-MM-dd')
							}}</el-descriptions-item>
						<el-descriptions-item label="提出人">{{ item.creatorName }}</el-descriptions-item>
						<el-descriptions-item label="相关要求" :span="2">{{ item.relevantRequ }}</el-descriptions-item>
					</el-descriptions>
					<el-table border class="mb-10px mt-10px" :data="item.dataList || []" :stripe="true"
						:show-overflow-tooltip="true">
						<el-table-column label="#" type="index" width="50" align="center" />
						<el-table-column label="资料名称" align="left" prop="materialName" min-width="120" />
						<el-table-column label="资料类型" align="center" prop="resourceCode" min-width="120">
							<template #default="{ row }">
								<dict-tag :type="'wel_check_file_type'" :value="row.resourceCode" />
							</template>
						</el-table-column>
						<el-table-column label="是否有模板" key="templateFlag" align="center" min-width="88">
							<template #default="{ row }">
								<dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="row.templateFlag" />
							</template>
						</el-table-column>
						<el-table-column label="模板名称" align="center" prop="templateName" min-width="120">
							<template #default="scope">
								<span class="click-pointer" @click="handlePreview(scope.row.fileId)">{{ scope.row.templateName }}</span>
							</template>
						</el-table-column>
						<el-table-column label="是否提供" key="provideFlag" align="center" min-width="88">
							<template #default="{ row }">
								<dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="row.provideFlag" />
							</template>
						</el-table-column>
						<el-table-column label="未提供原因说明" align="left" prop="noProvideReason" min-width="180" />
						<el-table-column label="相关联系人" align="center" prop="linkUserName" min-width="100" />
						<el-table-column label="电话" align="center" prop="tel" min-width="120" />
						<el-table-column label="操作" align="center" :width="150" fixed="right">
							<template #default="scope">
								<el-button type="primary" link v-show="scope.row.templateFlag == 1"
								@click="handleTemplateDownload(scope.row)">下载</el-button>
								<!-- <el-button type="primary" link @click="handleImportDataList(scope.row)">上传</el-button> -->
							</template>
						</el-table-column>
					</el-table>
				</div>
				<div class="detail-title common-border-left-blue">
					<span>项目基本信息</span>
				</div>
				<el-descriptions :column="2" border>
					<el-descriptions-item label="项目名称">{{ formData.projectName }}</el-descriptions-item>
					<el-descriptions-item label="项目编号">{{ formData.projectNo }}</el-descriptions-item>
					<el-descriptions-item label="审计类型">{{ formData.auditTypeDesc }}</el-descriptions-item>
					<el-descriptions-item label="立项年度">{{ formData.projectYear }}</el-descriptions-item>
					<el-descriptions-item label="组织方式">
						<dict-tag :type="DICT_TYPE.ORGANIZATION_TYPE" :value="formData.orgType" />
					</el-descriptions-item>
					<el-descriptions-item label="时间计划">{{ formData.timeSchedule }}</el-descriptions-item>
					<el-descriptions-item label="审计期间" v-if="formData.auditTypeDesc !== '专项审计'">{{ formData.auditPeriod
						}}</el-descriptions-item>
					<el-descriptions-item label="开展事项">
						<dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="formData.specialFlag" />
					</el-descriptions-item>
					<el-descriptions-item label="整体报告">
						<dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="formData.overallReportFlag" />
					</el-descriptions-item>
					<el-descriptions-item label="公司领导" v-if="formData.auditTypeDesc !== '专项审计'">{{
						formData.companyLeader
						}}</el-descriptions-item>
					<el-descriptions-item label="曾任职务" v-if="formData.auditTypeDesc !== '专项审计'">{{ formData.previouWork
						}}</el-descriptions-item>
					<el-descriptions-item label="发文编号" v-if="formData.auditTypeDesc !== '专项审计'">{{ formData.docNum
						}}</el-descriptions-item>
					<el-descriptions-item label="是否境外">
						<dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="formData.overseasFlag" />
					</el-descriptions-item>
					<el-descriptions-item label="是否重要">
						<dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="formData.significantFlag" />
					</el-descriptions-item>
					<el-descriptions-item label="实施单位">{{ formData.implementDeptName }}</el-descriptions-item>
					<el-descriptions-item label="立项依据" :span="4">{{ formData.projectGist }}</el-descriptions-item>
				</el-descriptions>

				<div class="detail-title common-border-left-blue">
					<span>迎审资料清单</span>
				</div>
				<el-table border :data="wellCheckFileDetailList" :stripe="true"
					:show-overflow-tooltip="true">
					<el-table-column label="#" type="index" width="50" />
					<el-table-column label="文档名称" align="left" prop="fileName" min-width="180" />
					<el-table-column label="文档类型" align="center" prop="fileTypeName" min-width="120" />
					<el-table-column label="编制单位" align="center" prop="deptName" min-width="180" />
					<el-table-column label="编制人" align="center" prop="creatorName" />
					<el-table-column label="生成时间" align="center" prop="createTime">
						<template #default="scope">
							<span>{{ formatTime(scope.row.createTime, 'yyyy-MM-dd') }}</span>
						</template>
					</el-table-column>
					<el-table-column label="操作" align="center" :width="200" fixed="right">
						<template #default="scope">
							<el-button type="primary" link
								@click="handleDownload(scope.row?.fileUrl, scope.row?.fileName)">下载</el-button>
							<el-button type="primary" link @click="handlePreview(scope.row.fileId)">预览</el-button>
						</template>
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<template #footer>
			<el-button @click="dialogVisible = false">取消</el-button>
		</template>
	</Dialog>
	<!-- 预览附件 -->
	<DialogFlie ref="DialogFlieRef" />
</template>
<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { ProjectDataListApi } from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectDataList'
import { ProjectApprovalApi } from '@/api/auditManagement/reviewManagement/projectApproval'
import { suppleAuditMaterialsApi } from '@/api/auditManagement/projectAssignment/auditImplem/suppleAuditMaterials'
import { formatTime } from '@/utils'
import download from '@/utils/download'
// defineOptions({ name: 'PreparationApproval' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
	id: undefined,
	resourceRespVO: {
		endTime: undefined,
		relevantRequ: undefined,
		materialList: [],
		projectAssignAttaRespVOList: []
	},
	projectName: undefined,
	projectNo: undefined,
	auditTypeDesc: undefined,
	projectYear: undefined,
	orgType: undefined,
	planStart: undefined,
	planEnd: undefined,
	auditPeriod: undefined,
	specialFlag: undefined,
	overallReportFlag: undefined,
	companyLeader: undefined,
	previouWork: undefined,
	docNum: undefined,
	overseasFlag: undefined,
	significantFlag: undefined,
	implementDeptName: undefined,
	projectGist: undefined,
})
const proSourceList = ref([])
const wellCheckFileDetailList = ref([])
/** 打开弹窗 */
const open = async (id?: number) => {
	dialogVisible.value = true
	dialogTitle.value = t('详情')
	// // 修改时，设置数据
	if (id) {
		formLoading.value = true
		try {
			formData.value = await ProjectDataListApi.getProjectInitiationMaterialDetail(id)
			wellCheckFileDetailList.value = await suppleAuditMaterialsApi.getWellCheckFileDetailList(id)
			proSourceList.value = (await ProjectApprovalApi.getTotalProjSourceByProjectId(id||formData.value.id as unknown as number)) || []
		} finally {
			formLoading.value = false
		}
	}
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 预览
const DialogFlieRef = ref()
const handlePreview = (id: number) => {
	DialogFlieRef.value.open('预览', 'VIEW', id)
}
// 下载
const handleDownload = (url: string, name: string) => {
	download.downFileByFileUrl(url, name)
}
const handleTemplateDownload = async (row) => {
  download.downFileByFileUrl(row.fileUrl, '资料清单填报模板')
}
</script>
<style lang="scss" scoped>
.title {
	font-weight: 900;
	font-size: 16px;
	margin-bottom: 16px;
	margin-top: 16px;
	display: flex;
	align-items: center;
}

:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
	width: 80px;
}
</style>
