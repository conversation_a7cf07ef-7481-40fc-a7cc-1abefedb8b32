import request from '@/config/axios'

export interface DeptVO {
  id?: number
  name: string
  shortName : string
  parentId: number
  status: number
  sort: number
  deptTmpCode: string
  leaderUserId: number
  phone: string
  email: string
  createTime: Date
}
interface PageParam {
  id: number
}
interface UpdateVo {
  id?: number
  name: string
  shortName : string
  deptTmpCode: string
  sort: number
  parentId: number
  parentName?: string
}
// 查询部门（精简)列表
export const getSimpleDeptList = async (id: number = 0): Promise<DeptVO[]> => {
  return await request.get({ url: '/system/dept/simple-list?id=' + id })
}
export const getSimpleDeptByName = async (name: string): Promise<DeptVO[]> => {
  return await request.get({ url: '/system/dept/list-by-name?name=' + name })
}

// 查询部门列表
export const getDeptPage = async (params: PageParam) => {
  return await request.get({ url: '/system/dept/list', params })
}

// 查询部门详情
export const getDept = async (id: number) => {
  return await request.get({ url: '/system/dept/get?id=' + id })
}
// 查询部门详情包含父节点
export const getParentDept = async (id: number) => {
  return await request.get({ url: '/system/dept/getParentDept?id=' + id })
}

// 新增部门
export const createDept = async (data: DeptVO) => {
  return await request.post({ url: '/system/dept/create', data: data })
}

// 修改部门
export const updateDept = async (params: UpdateVo) => {
  return await request.put({ url: '/system/dept/update', data: params })
}

// 删除部门
export const deleteDept = async (id: number) => {
  return await request.delete({ url: '/system/dept/delete?id=' + id })
}

// 查询法人组织属性列表
export const getTmpOrganizationList = async (id: number) => {
  return await request.get({ url: '/system/orgdata/dept-tmplist?id=' + id})
}