import request from '@/config/axios'
export interface searchListVO {
	pageNo: number
	pageSize: number
	projectName?: string
	orgType?: number
	auditObject?: number
	auditType?: number
	auditYear?: string
	projStage?: number
	overseasFlag?: number
}
export interface createVO {
	projectName: string
	auditType: number
	auditSencType: number
	projectYear: string
	orgType: number
	timeSchedule: [string, string]
	auditPeriod: [string, string]
	sameAuditGroupFlag: boolean
	specialFlag: number
	overallReportFlag: number
	companyLeader: string
	previouWork: string
	docNum: string
	overseasFlag: number
	significantFlag: number
	implementDeptName: string
	projectGist: string
	auditGroupList?: []
	auditTargetReqVo?: []
	projectAttaVOList?: []
	assignProgConfSaveReqVOList?: []
}
// 项目立项
export const ProjectInitiationApi = {
	// 项目立项列表查询--子项目
	getProjPlanProjectList: async (data: searchListVO) => {
		return await request.post({ url: `/manage/proj-plan-project/page`, data })
	},
	// 项目立项列表查询--父项目
	getProjParentProjectList: async (params: searchListVO) => {
		return await request.get({ url: `/manage/proj-parent-project/page`, params })
	},
	// 项目立项列表--父项目--删除
	deleteParentProject: async (id: number) => {
		return await request.delete({ url: `/manage/proj-parent-project/delete?id=${id}` })
	},
	exportExcel: async (params: searchListVO) => {
		return await request.get({ url: `/manage/proj-plan-project/export-excel`, params })
	},
	getProjectBasicDetail: async (id: number, progCode: string) => {
		return await request.get({ url: `/manage/proj-plan-project/detail?id=${id}&progCode=${progCode}` })
	},
	getParentDetail: async (id: number) => {
		return await request.get({ url: `/manage/proj-parent-project/detail?id=${id}` })
	},
	// 子项目处理-获取菜单--二级菜单
	getSecondTree: async (id: number) => {
		return await request.get({ url: `/manage/assign-prog-conf/project-get?projectId=${id}` })
	},
	// 子项目立项-配置审计程序接口
	getConfigTree: async (id: number) => {
		return await request.get({ url: `/system/config-type-prog/get-tree?id=${id}` })
	},
	// 根据审计程序获取附件类型（code写死的）
	getFileType: async (code: string = 'QDAL1727680366530') => {
		return await request.get({ url: `/system/config-prog-atta/file-list?code=${code}` })
	},
	// 立项-内部立项新增
	createProject: async (data: createVO) => {
		return await request.post({ url: `/manage/proj-parent-project/project-approval`, data })
	},
	// 审计小组信息-所有
	getAllTeam: async (id: number | string) => {
		return await request.get({ url: `/manage/audit-target/get-group?projectId=${id}` })
	},
	// 审计小组信息-审计对象中的某一个
	getOneTeam: async (id: number | string) => {
		return await request.get({ url: `/manage/audit-target/target/get-group?targetId=${id}` })
	}, 
	// 父项目-处理-审前资料清单-删除
	deleteFileById: async (id: number | string) => {
		return await request.delete({ url: `/manage/project-assign-atta/delete?id=${id}` })
	},
	//立项--选择审计对象列表查询
	getSecondDeptList: async (params: any) => {
		return await request.get({ url: `/manage/year-plan-report-dept/getAllDeptList`,params })
	},
}