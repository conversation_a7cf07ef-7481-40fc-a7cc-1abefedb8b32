import request from '@/config/axios'
// 审计资料清单 API
// 案例库 VO
export interface searchListVO {
    pageNo: number
    pageSize: number
    mattersId?: number
    materialName?: string
    mattersName?: string
    templateFlag?: number
    materialDesc?: string
}

export interface AddCheckListVO {
    id?: number
    materialName: string
    templateFlag: number
    materialDesc: string
    mattersId: number
}

// 审计资料清单 API
export const CheckListApi = {
    // 审计资料清单列表查询
    getCheckList: async (params: searchListVO) => {
        return await request.get({ url: `/audit/tank-matters-material/page`, params })
    },
    // 审计资料清单-详情
    getCheckListDetail: async (id: number) => {
        return await request.get({ url: `/audit/tank-matters-material/get?id=${id}` })
    },
    // 审计资料清单-新增
    createCheckList: async (data: AddCheckListVO) => {
        return await request.post({ url: `/audit/tank-matters-material/create`, data })
    },

    updateCheckList: async (data: AddCheckListVO) => {
        return await request.put({ url: `/audit/tank-matters-material/update`, data })
    },
    deleteCheckListById: async (id: number) => {
        return await request.delete({ url: `/audit/tank-matters-material/delete?id=` + id })
    },
    // 获得所有模版
    getSelectionList: async (params: any) => {
        return await request.get({ url: `/audit/tank-programmes/page`, params })
    },
    // 事项名称下拉
    getMatters: async () => {
        // return await request.get({ url: `/audit/tank-matters-info/get-matterslist` })
        return await request.get({ url: `/audit/tank-trees-type/get?type=4` })
    },
    // 查询案例库详情
    getCasebase: async (id: number) => {
        return await request.get({ url: `/system/tank-programmes/get?id=` + id })
    },

    // 审计资料清单列表导出
    exportCheckList: async (params: searchListVO) => {
        return await request.download({ url: `/audit/tank-matters-material/export-excel`, params })
    },

}
