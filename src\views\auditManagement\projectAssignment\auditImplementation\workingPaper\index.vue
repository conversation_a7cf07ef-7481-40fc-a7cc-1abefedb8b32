<template>
  <el-row>
    <el-col :span="24" :xs="24">
      <!-- 查询 -->
      <ContentWrap class="common-card-search">
        <el-form
          class="-mb-15px common-search-form"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="84px"
        >
          <el-form-item label="底稿编号" prop="fileCode">
            <el-input
              v-model="queryParams.fileCode"
              placeholder="请输入底稿编号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="底稿名称" prop="fileName">
            <el-input
              v-model="queryParams.fileName"
              placeholder="请输入底稿名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="事项名称" prop="matterName">
            <el-input
              v-model="queryParams.matterName"
              placeholder="请输入事项名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="项目名称" prop="projectName">
            <el-input
              v-model="queryParams.projectName"
              placeholder="请输入项目名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="审计对象" prop="auditObject">
            <el-input
              v-model="queryParams.auditObject"
              placeholder="请输入审计对象"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="编制人员" prop="creatorName">
            <el-input
              v-model="queryParams.creatorName"
              placeholder="请输入编制人员"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
        </el-form>
        <div class="right-search-btn">
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" />搜索
          </el-button>
          <el-button @click="resetQuery"> <Icon icon="ep:refresh" class="mr-5px" />重置 </el-button>
        </div>
      </ContentWrap>
      <ContentWrap>
        <div class="button_margin15">
          <el-button @click="handleCreate" v-if="routeFlag" type="primary">新增</el-button>
          <!-- <el-button @click="handleCreate" type="primary">上传</el-button> -->
          <el-button @click="handleSubmitSel" v-if="routeFlag" type="primary">批量提交</el-button>
          <el-button plain @click="handleBatchDownload">批量下载</el-button>
          <el-button @click="handleExport">
            <Icon class="mr-5px" icon="ep:download" />导出
          </el-button>
        </div>
        <el-table
          border
          v-loading="loading"
          :data="list"
          ref="fileTable"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" :selectable="selectable" width="55" />
          <el-table-column label="#" width="50" align="center">
            <template #default="{ $index }">
              {{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column
            label="审计底稿编号"
            align="left"
            prop="fileCode"
            :show-overflow-tooltip="true"
            min-width="220"
          />

          <el-table-column
            label="审计底稿名称"
            align="left"
            min-width="260"
            :show-overflow-tooltip="true"
          >
            <template #default="{ row }">
              <span
                class="click-pointer"
                @click="handleShowMessage(row.fileName, 'VIEW', row.fileId)"
              >
                {{ row.fileName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="事项名称"
            align="left"
            prop="matterName"
            :show-overflow-tooltip="true"
            min-width="200"
          />
          <el-table-column
            label="附件类型"
            align="center"
            prop="fileTypeName"
            :show-overflow-tooltip="true"
            min-width="160"
          />
          <el-table-column
            label="项目编号"
            align="left"
            prop="projectNo"
            :show-overflow-tooltip="true"
            min-width="180"
          />
          <el-table-column
            label="项目名称"
            align="left"
            min-width="260"
            :show-overflow-tooltip="true"
          >
            <template #default="{ row }">
              <span class="click-pointer" @click="showProjectDetail(row.projectId)">
                {{ row.projectName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="审计对象"
            align="left"
            prop="auditObject"
            :show-overflow-tooltip="true"
            min-width="260"
          />
          <el-table-column
            label="编制人"
            align="center"
            prop="creatorName"
            :show-overflow-tooltip="true"
            min-width="100"
          />
          <el-table-column
            label="编制时间"
            align="center"
            prop="createTime"
            :show-overflow-tooltip="true"
            min-width="180"
          >
            <template #default="{ row }">
              <span>{{ formatDate(row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="审批状态" align="center" prop="flowStatus" min-width="100">
						<template #default="scope">
							<span class="click-pointer"
								@click="handleShowProcess(scope.row?.instanceId)">{{getApprovalStatus(scope.row.instanceState)}}</span>
						</template>
					</el-table-column>

          <el-table-column label="操作" align="center" :width="200" fixed="right">
            <template #default="scope">
              <el-button type="primary" link @click="showList(scope.row.id)">关联资料</el-button>
              <el-button
                type="primary"
                v-if="(scope.row.instanceState == 0 || scope.row.instanceState == 3) && routeFlag"
                link
                @click="handleShowMessage(scope.row.fileName, 'EDIT', scope.row.fileId)"
                >编辑</el-button
              >
              <el-button
                type="danger"
                link
                v-if="(scope.row.instanceState == 0 || scope.row.instanceState == 3) && routeFlag"
                @click="handleDelete(scope.row.id)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
  </el-row>

  <!-- 添加或修改用户对话框 -->
  <!-- <ProjectInitiationApproval ref="formRef" @success="getList" />
  <ChoiceInitiationModal ref="choiceRef" />
  <DetailMessage ref="messageRef" />-->
  <!-- <PlanListDetail ref="detailRef" />
  <PlanListEdit ref="editRef" />-->
  <!-- <CreateAudit ref="createAuditRef" /> -->
  <!-- <ProframmesApproval ref="createAuditRef" />
  <EditMatter ref="editMatterRef" />-->
  <DialogFlie ref="dialogFlieRef" />
  <QuantityOfWorkingPapers ref="quantityRef" />
  <AuditMattersList ref="mattersListRef" />
  <DetailMessage ref="detailMessageRef" />
  <WorkpaperList ref="paperListRef" />
  <CreateWorkPaperModal ref="createRef" @success="getList"/>
  <!-- <EditAdjust ref="editRef" /> -->
  <RelatedModal ref="relatedModalRef" />
  <!-- 审批详情-->
	<BpmDetail ref="bpmDetailRef" />
  <BatchSubmit ref="batchSubmitRef" @success="getList" />
</template>
<script lang="ts" setup>
// import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
// import { checkPermi } from '@/utils/permission'
// import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { formatDate } from '@/utils/formatTime'
import { CommonStatusEnum } from '@/utils/constants'
import { WorkingPaperApi } from '@/api/auditManagement/projectAssignment/auditImplem/workingPaper'
import DetailMessage from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/message/DetailMessage.vue'
import RelatedModal from './child/RelatedModal.vue'
import BpmDetail from '@/views/bpm/processInstance/detail/bpmDetail.vue'
import { getApprovalStatus } from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation'
import { DialogFlie } from '@/components/DialogFlie'
// import ProjectInitiationApproval from './ProjectInitiationApproval.vue'
// import ChoiceInitiationModal from './ChoiceInitiationModal.vue'
// import DetailMessage from './message/DetailMessage.vue'
// import PreparationForm from './PreparationForm.vue'
// import PreparationApproval from './PreparationApproval.vue'
// import PlanListDetail from './PlanListDetail.vue'
// import PlanListEdit from './PlanListEdit.vue'
// import CreateAudit from './CreateAudit.vue'
// import EditMatter from './child/EditMatter.vue'
import QuantityOfWorkingPapers from '@/views/auditManagement/projectAssignment/PreTrialPreparation/auditProgrammes/child/QuantityOfWorkingPapers.vue'
import AuditMattersList from '@/views/auditManagement/projectAssignment/PreTrialPreparation/auditProgrammes/child/AuditMattersList.vue'
// import ProframmesApproval from '@/views/auditManagement/projectAssignment/PreTrialPreparation/auditProgrammes/child/QuantityOfWorkingPapers.vue'
// import EditAdjust from './EditAdjust.vue'
import WorkpaperList from './WorkpaperList.vue'
import CreateWorkPaperModal from './CreateWorkPaperModal.vue'
import BatchSubmit from './BatchSubmit.vue'

import { useRoute } from 'vue-router'
import ChiefReviewerApproval from './ChiefReviewerApproval.vue'
defineOptions({ name: 'WorkingPaper' })
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const routeFlag = ref(false)
// const selProjId = ref()
const selectionList = ref([])
const route = useRoute() //路由传参
routeFlag.value = route.query.id ? true : false

const handleSelectionChange = (val: []) => {
  selectionList.value = val
}

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  fileCode: undefined,
  fileName: undefined,
  matterName: undefined,
  projectName: undefined,
  projId: route.query.id,
  auditObject: undefined,
  creatorName: undefined
})
const queryFormRef = ref() // 搜索的表单
const choiceRef = ref()
const fileTable = ref()
const handleChoice = () => {
  choiceRef.value.open()
}
const detailMessageRef = ref()
const showProjectDetail = (projectId: string) => {
  detailMessageRef.value.open(projectId)
}
const paperListRef = ref()
const showList = (id: number) => {
  paperListRef.value.open(id)
}

// 下载
const handleBatchDownload = async () => {
  if (!selectionList.value.length) {
    message.error('请选择需要下载的材料')
    return
  }
  try {
    loading.value = true

    const batchDownList = selectionList.value.map(async (item) => {
      await download.downFileByFileUrl(item?.fileUrl, item?.fileName)
    })

    await Promise.all(batchDownList)
    fileTable.value!.clearSelection()
  } catch (error) {
  } finally {
    loading.value = false
  }
}
const quantityRef = ref()
const showQuantity = () => {
  quantityRef.value.open()
}
const mattersListRef = ref()
const showMattersList = () => {
  mattersListRef.value.open()
}
const batchSubmitRef = ref()
const handleSubmitSel = () => {
  if (!selectionList.value.length) {
    message.error('请选择数据')
    return
  }
  let selProjId = selectionList.value[0].projectId
  let list = selectionList.value.filter((item) => item.projectId !== selProjId)
  console.log(selectionList.value, list, selProjId)

  if (list.length && list.length !== selectionList.value.length) {
    message.error('请选择同一项目数据')
  } else {
    let arr = selectionList.value
    batchSubmitRef.value.open(arr, selProjId)
  }
}
const handleSubmit = (obj: {}) => {
  let arr = [obj]
  batchSubmitRef.value.open(arr, obj.projectId)
}
const relatedModalRef = ref()
const showRelateModal = (obj: {}) => {
  console.log(obj)
  relatedModalRef.value.open(obj)
}
const editMatterRef = ref()
const handleEditMatterRef = (id: number) => {
  editMatterRef.value.open(id)
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await WorkingPaperApi.getWorkingPaperList(queryParams)

    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
const handleEdit = () => {
  editRef.value.open()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}
const dialogFlieRef = ref()
const handleShowMessage = (title: string, flietype: string, id: number) => {
  dialogFlieRef.value.open(title, flietype, id)
}
/** 处理部门被点击 */
const handleDeptNodeClick = async (row) => {
  queryParams.deptId = row.id
  await getList()
}

/** 添加/修改操作 */
const formRef = ref()
const detailRef = ref()
const openForm = (type: string, id?: number) => {
  if (type === 'detail') {
    detailRef.value.open()
  } else {
    formRef.value.open(type, id)
  }
}

/** 用户导入 */
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value.open()
}

/** 修改用户状态 */
const handleStatusChange = async (row: UserApi.UserVO) => {
  try {
    // 修改状态的二次确认
    const text = row.status === CommonStatusEnum.ENABLE ? '启用' : '停用'
    await message.confirm('确认要"' + text + '""' + row.username + '"用户吗?')
    // 发起修改状态
    await UserApi.updateUserStatus(row.id, row.status)
    // 刷新列表
    await getList()
  } catch {
    // 取消后，进行恢复按钮
    row.status =
      row.status === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE
  }
}

/** 导出按钮操作 */
const exportLoading = ref(false)
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await WorkingPaperApi.exportWork(queryParams)
    download.excel(data, '审计工作底稿.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: string) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await WorkingPaperApi.deleteFile(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

const bpmDetailRef = ref()
// 审批详情
const handleShowProcess = (setupProjFlowId: string) => {
	if (!setupProjFlowId||setupProjFlowId=='0') {
		message.warning('流程还未开始')
		return
	}
	bpmDetailRef.value.open(setupProjFlowId)
}
/** 重置密码 */
const handleResetPwd = async (row: UserApi.UserVO) => {
  try {
    // 重置的二次确认
    const result = await message.prompt(
      '请输入"' + row.username + '"的新密码',
      t('common.reminder')
    )
    const password = result.value
    // 发起重置
    await UserApi.resetUserPwd(row.id, password)
    message.success('修改成功，新密码是：' + password)
  } catch {}
}

/** 分配角色 */
const assignRoleFormRef = ref()
const handleRole = (row: UserApi.UserVO) => {
  assignRoleFormRef.value.open(row)
}
const createRef = ref()
const handleCreate = () => {
  console.log(route.query.id)
  // const id = route.query.id
  createRef.value.open(route.query.id)
}
/** 初始化 */
onMounted(() => {
  getList()
})
</script>
