<template>
  <el-row>
    <el-col :span="24" :xs="24">
      <!-- 查询 -->
      <ContentWrap class="common-card-search">
        <el-form
          class="-mb-15px common-search-form"
          :model="queryParams"
          ref="queryFormRef"
          :inline="true"
          label-width="84px"
        >
          <el-form-item label="取证单编号" prop="certNo">
            <el-input
              v-model="queryParams.certNo"
              placeholder="请输入取证单编号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="取证单名称" prop="certName">
            <el-input
              v-model="queryParams.certName"
              placeholder="请输入取证单名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <!-- <el-form-item label="项目编号" prop="username">
            <el-input
              v-model="queryParams.projectNo"
              placeholder="请输入项目编号"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="项目名称" prop="projectName">
            <el-input
              v-model="queryParams.projectName"
              placeholder="请输入项目名称"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>-->
          <el-form-item label="审计对象" prop="auditObject">
            <el-input
              v-model="queryParams.auditObject"
              placeholder="请输入审计对象"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
          <el-form-item label="编制人员" prop="creatorName">
            <el-input
              v-model="queryParams.creatorName"
              placeholder="请输入编制人员"
              clearable
              @keyup.enter="handleQuery"
              class="!w-200px"
            />
          </el-form-item>
        </el-form>
        <div class="right-search-btn">
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" />搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" />重置
          </el-button>
        </div>
      </ContentWrap>
      <ContentWrap>
        <div class="button_margin15">
          <el-button @click="handleCreate" v-if="queryId" type="primary">新增</el-button>
          <!-- <el-button @click="handleCreate" type="primary">上传</el-button> -->
          <el-button @click="handleSubmit(multipleSelection)" v-if="queryId" type="primary">提交</el-button>
          <el-button
            @click="handleDoenload(multipleSelection)"
            v-if="queryId"
            type="primary"
            :loading="downLoading"
          >下载</el-button>
        </div>
        <!-- <el-button @click="handleCreate" type="success">导出</el-button> -->
        <el-table border v-loading="loading" :data="list" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="40" />
          <el-table-column label="#" width="50" align="center">
            <template
              #default="{ $index }"
            >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
          </el-table-column>
          <el-table-column
            label="取证单编号"
            align="left"
            prop="certNo"
            :show-overflow-tooltip="true"
            min-width="220"
          />
          <el-table-column
            label="审计取证单名称"
            align="left"
            min-width="260"
            :show-overflow-tooltip="true"
          >
            <template #default="{row}">
              <span
                class="click-pointer"
                @click="handleDetailView('预览','VIEW',row)"
              >{{row.certName}}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="编制人"
            align="center"
            prop="creatorName"
            :show-overflow-tooltip="true"
            min-width="100"
          />
          <el-table-column
            label="审计事项"
            align="center"
            prop="auditMatter"
            :show-overflow-tooltip="true"
            min-width="160"
          />
          <!-- <el-table-column
            label="附件类型"
            align="center"
            prop="nickname"
            :show-overflow-tooltip="true"
            min-width="120"
          />-->
          <!-- <el-table-column
            label="项目编号"
            align="left"
            prop="projectNo"
            :show-overflow-tooltip="true"
            min-width="180"
          />
          <el-table-column label="项目名称" align="left" min-width="260" :show-overflow-tooltip="true">
            <template #default="{row}">
              <span
                class="click-pointer"
                @click="showProjectDetail(row.projectId)"
              >{{row.projectName}}</span>
            </template>
          </el-table-column>-->
          <el-table-column
            label="审计对象"
            align="left"
            prop="auditObject"
            :show-overflow-tooltip="true"
            min-width="260"
          />

          <el-table-column label="编制时间" align="center" prop="createTime" min-width="120">
            <template #default="{row}">
              <span>{{ formatTime(row.createTime, 'yyyy-MM-dd') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="审批状态" align="center" prop="flowStatus" min-width="100">
						<template #default="scope">
							<span class="click-pointer"
								@click="handleShowProcess(scope.row?.flowId)">{{getApprovalStatus(scope.row.flowStatus)}}</span>
						</template>
					</el-table-column>
          <el-table-column label="操作" align="center" :width="160" fixed="right">
            <template #default="scope">
              <!-- <el-button
                type="primary"
                link
                @click="showList([{
                id: scope.row.id
              }])"
              >查看</el-button>-->
              <!-- <el-button type="primary" link @click="handleDetailView('预览','VIEW',scope.row)">预览</el-button> -->
              <!-- <el-button type="primary" link @click="handleDetailView('预览','VIEW',scope.row)">下载</el-button> -->
              <!-- <el-button
                type="primary"
                link
                @click="handleSubmit([{
                id: scope.row.id
              }])"
                v-if="queryId && scope.row.flowStatus == 0"
              >提交</el-button>-->
              <el-button
                type="primary"
                v-if="scope.row.flowStatus == 0  && queryId"
                link
                @click="handleDetailView('编辑','EDIT',scope.row)"
              >编辑</el-button>
              <el-button
                type="primary"
                link
                @click="showRelateModal(scope.row.id,[{
                id: scope.row.id
              }],scope.row)"
                v-if="scope.row.flowStatus == 0"
              >关联</el-button>
              <el-button
                type="danger"
                v-if="scope.row.flowStatus == 0 && queryId"
                link
                @click="handleDelete(scope.row.id)"
              >删除</el-button>
              <!-- <el-button type="primary" link @click="handleEditMatterRef(scope.row.id)">事项编辑</el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </ContentWrap>
    </el-col>
  </el-row>

  <QuantityOfWorkingPapers ref="quantityRef" />
  <AuditMattersList ref="mattersListRef" />
  <DetailMessage ref="detailMessageRef" />
  <SheetList ref="sheetListRef" />
  <CreateSheetModal ref="createRef" @success="getList" />
  <!-- <EditAdjust ref="editRef" /> -->
  <RelatedModal ref="relatedModalRef" @success="relatedSuccess" />
  <BatchSubmit ref="batchSubmitRef" @success="batchSubmitSuccess" />
  <DialogFlie ref="DialogFlieRef" />
  <!-- 审批详情-->
	<BpmDetail ref="bpmDetailRef" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { checkPermi } from '@/utils/permission'
import { dateFormatter } from '@/utils/formatTime'
import FileForm from '@/views/infra/file/FileForm.vue'
import { formatTime } from '@/utils'
import download from '@/utils/download'
import { DialogFlie } from '@/components/DialogFlie'
import { CommonStatusEnum } from '@/utils/constants'
import * as UserApi from '@/api/system/user'
import DetailMessage from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/message/DetailMessage.vue'
import RelatedModal from './child/RelatedModal.vue'
import { AuditEvidenceSheetApi } from '@/api/auditManagement/projectAssignment/auditImplem/auditEvidenceSheet'
import QuantityOfWorkingPapers from '@/views/auditManagement/projectAssignment/PreTrialPreparation/auditProgrammes/child/QuantityOfWorkingPapers.vue'
import AuditMattersList from '@/views/auditManagement/projectAssignment/PreTrialPreparation/auditProgrammes/child/AuditMattersList.vue'
import SheetList from './SheetList.vue'
import CreateSheetModal from './CreateSheetModal.vue'
import BatchSubmit from './BatchSubmit.vue'
import { CommonApi } from '@/api/common'
import ChiefReviewerApproval from './ChiefReviewerApproval.vue'
import BpmDetail from '@/views/bpm/processInstance/detail/bpmDetail.vue'
import { getApprovalStatus } from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation'
defineOptions({ name: 'AuditEvidenceSheet' })
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const { query } = useRoute() // 查询参数
const queryId = query.id as unknown as number
const list = ref([]) // 列表的数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  certNo: undefined,
  certName: undefined,
  projectNo: undefined,
  auditObject: undefined,
  creatorName: undefined,
  projectId: query.id as unknown as number
})
const queryFormRef = ref() // 搜索的表单
const detailMessageRef = ref()
const showProjectDetail = (id: number) => {
  detailMessageRef.value.open(id)
}

const sheetListRef = ref()
const showList = (ids) => {
  sheetListRef.value.open(ids)
}
const DialogFlieRef = ref()
const handleDetailView = async (name: string, type: string, val) => {
  const data = await CommonApi.getOneFileByMainIdAndProjectId({
    mainId: val.id,
    projectId: val.projectId
  })
  await DialogFlieRef.value.open(name, type, data.fileId)
}
const batchSubmitRef = ref()
const downLoading = ref(false)
const handleSubmit = (idarr) => {
  if (idarr && idarr.length === 0) {
    message.error('请选择需要提交的数据')
    return
  }
  batchSubmitRef.value.open(queryId, idarr)
}
const handleDoenload = async (idarr) => {
  if (idarr && idarr.length === 0) {
    message.error('请选择需要提交的数据')
    return
  }
  await message.confirm('确定进行批量下载吗?')
  await Promise.all(
    idarr.map(async (item) => {
      try {
        downLoading.value = true
        const data = await CommonApi.getOneFileByMainIdAndProjectId({
          mainId: item.id,
          projectId: item.projectId
        })
        download.downFileByFileUrl(data.fileUrl, data.fileName)
      } catch (error) {
      } finally {
        downLoading.value = false
      }
    })
  )
}
const relatedModalRef = ref()
const currentId = ref()
const showRelateModal = (id: number, ids: any,row:any) => {
  currentId.value = id
  relatedModalRef.value.open([], ids,row)
}
const editMatterRef = ref()
const handleEditMatterRef = (id: number) => {
  editMatterRef.value.open()
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await AuditEvidenceSheetApi.getPageList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
const handleEdit = () => {
  editRef.value.open()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}
const messageRef = ref()
const handleShowMessage = (id: number) => {
  messageRef.value.open()
}
/** 添加/修改操作 */
const formRef = ref()
const detailRef = ref()
/** 导出按钮操作 */
const exportLoading = ref(false)
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AuditEvidenceSheetApi.deleteEvidence(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}
const batchSubmitSuccess = () => {
  getList()
  multipleSelection.value = []
}
const multipleSelection = ref([])
const handleSelectionChange = (val: any) => {
  multipleSelection.value = val
}

const createRef = ref()
const handleCreate = () => {
  createRef.value.open(queryId)
}
const relatedSuccess = async (arr) => {
  // let data = {
  //   certId: currentId.value,
  //   projectId: queryId,
  //   saveReqVOList: arr
  // }
  // await AuditEvidenceSheetApi.listLink(data)
  getList()
}
const bpmDetailRef = ref()
// 审批详情
const handleShowProcess = (setupProjFlowId: string) => {
	if (!setupProjFlowId||setupProjFlowId=='0') {
		message.warning('流程还未开始')
		return
	}
	bpmDetailRef.value.open(setupProjFlowId)
}
/** 初始化 */
onMounted(() => {
  getList()
})
</script>
