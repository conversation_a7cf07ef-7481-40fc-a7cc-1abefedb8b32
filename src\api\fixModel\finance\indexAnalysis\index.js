import request from '@/config/axios'

// 获取财务指标目录
export function getIndexThemeTreeData() {
  return request.post({
    url: '/model/financeAnalysisCommonParameter/financeIndexThemeTreeData',
  })
}

// 根据财务指标目录获取指标名称选中的指标选项 与上接口形成联动关系
export function getIndexNameOptions(data) {
  return request.post({
    url: '/model/financeAnalysisCommonParameter/targetCatalogIndexData',
    data
  })
}

// 获取全部指标名称选项
export function getAllIndexNameOptions() {
  return request.post({
    url: '/model/financeAnalysisCommonParameter/allFinanceIndexTreeData'
  })
}

// 获取全部法人组织树数据
export function getAllLegalOrgTreeData() {
  return request.post({
    url: '/model/financeAnalysisCommonParameter/allLegalPersonOrganizationTreeData'
  })
}

// 获取下方表表头
export function getTableHeader(data) {
  return request.post({
    url: '/model/financeIndexAnalysis/targetIndexTableHeader',
    data
  })
}

// 获取表格数据
export function getTableData(data) {
  return request.post({
    url: '/model/financeIndexAnalysis/targetFinanceIndexTableData',
    data
  })
}

// 点击单位 获取财务指标分析图形数据
export function getAnalysisChartData(data) {
  console.log(data)
  return request.post({
    url: '/model/financeIndexAnalysis/financeIndexAnalysisChartData',
    data
  })
}

// 点击单位 获取财务指标分析表格数据
export function getAnalysisTableData(data) {
  return request.post({
    url: '/model/financeIndexAnalysis/financeIndexAnalysisTableData',
    data
  })
}

// 导出财务指标分析汇总数据 保存参数
export function saveExportParameter(data) {
  return request.post({
    url: '/model/financeIndexAnalysis/saveExportParameter',
    data
  })
}

// 导出财务指标分析汇总数据
export function exportFinanceIndexAnalysis(key) {
  return request.download({
    url: '/model/financeIndexAnalysis/exportFinanceIndexAnalysisSummaryData/' + key,
  })
}

// 导出财务指标分析钻取数据
export function exportFinanceIndexAnalysisDrillData(key) {
  return request.download({
    url: '/model/financeIndexAnalysis/exportFinanceIndexAnalysisDrillData/' + key,
  })
}
