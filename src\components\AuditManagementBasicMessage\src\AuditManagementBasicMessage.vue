<template>
  <div>
    <el-descriptions :column="2" border>
      <el-descriptions-item label="项目名称">{{ detailData?.projectName }}</el-descriptions-item>
      <el-descriptions-item label="项目编号">{{ detailData?.projectNo }}</el-descriptions-item>
      <el-descriptions-item label="审计类型">{{ detailData?.auditTypeDesc}}</el-descriptions-item>
      <el-descriptions-item label="立项年度">{{ detailData?.projectYear }}</el-descriptions-item>
      <el-descriptions-item label="组织方式">
        <dict-tag :type="DICT_TYPE.ORGANIZATION_TYPE" :value="detailData?.orgType" />
      </el-descriptions-item>
      <el-descriptions-item label="时间计划">{{detailData?.timeSchedule}}</el-descriptions-item>
      <el-descriptions-item label="审计期间">{{ detailData?.auditPeriod}}</el-descriptions-item>
      <el-descriptions-item label="开展专项" v-if="detailData?.auditTypeDesc !== '专项审计'">
        <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData?.specialFlag" />
      </el-descriptions-item>
      <template v-if="isExpand">
        <el-descriptions-item label="整体报告" v-if="detailData.auditTypeDesc !== '专项审计'">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData?.overallReportFlag" />
        </el-descriptions-item>
        <el-descriptions-item
          label="公司领导"
          v-if="detailData.auditTypeDesc === '经济责任审计'"
        >{{ detailData?.companyLeader}}</el-descriptions-item>
        <el-descriptions-item
          label="曾任职务"
          v-if="detailData.auditTypeDesc === '经济责任审计'"
        >{{ detailData?.previouWork}}</el-descriptions-item>
        <el-descriptions-item label="发文编号">{{ detailData?.docNum}}</el-descriptions-item>
        <el-descriptions-item label="是否境外">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData?.overseasFlag" />
        </el-descriptions-item>
        <el-descriptions-item label="是否重要">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData?.significantFlag" />
        </el-descriptions-item>
        <el-descriptions-item label="实施单位">{{ detailData?.implementDeptName}}</el-descriptions-item>
        <el-descriptions-item label="立项依据" :span="4">{{ detailData?.projectGist}}</el-descriptions-item>
      </template>
    </el-descriptions>
    <div class="expand-btn mt-16px">
      <el-button @click="toggleExpand" size="small" type="text">
        <span v-if="isExpand">
          收起
          <Icon icon="ic:round-expand-less" color="#2e4db5" />
        </span>
        <span v-else>
          展开
          <Icon icon="ic:round-expand-more" color="#2e4db5" />
        </span>
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getIntDictOptions, getStrDictOptions, DICT_TYPE, getDictLabel } from '@/utils/dict'
defineOptions({ name: 'AuditManagementBasicMessage' })
const props = defineProps({
  detailData: {
    type: Object,
    default: () => {}
  }
})
const isExpand = ref(false)
const toggleExpand = () => {
  isExpand.value = !isExpand.value
}
</script>

<style scoped lang='scss'>
.expand-btn {
  text-align: center;
  width: 100%;
  border: var(--el-descriptions-table-border);
}
.expand-btn .el-button:hover {
  background-color: #fff;
}
.expand-btn .el-button {
  border-radius: 4px;
}
.expand-btn .el-button--small {
  height: 22px;
}
</style>
