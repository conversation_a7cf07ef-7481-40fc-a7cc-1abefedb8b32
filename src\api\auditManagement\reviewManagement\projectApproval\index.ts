import request from '@/config/axios'


export interface SearchVo {
    pageNo: number
    pageSize: number
    projectName?: string
    projectNo?: string
    auditObject?: number
    auditType?: number
    auditYear?: string
    projStage?: number
}
export interface BaseInfoVo {
    id: number
    projectName: string
    projectNo: string
    auditTypeDesc: string
    projectYear: string
    orgType: number
    timeSchedule: string
    auditPeriod: string

}
interface AddRoleVo {
    auditGroupList: []
    resultDetailList: []
    projectId?: number
}
export interface fileReceiveVo {
    createTime: number
    creatorName: string
    feedback: string
    feedbackFlag: number
    fileId: number
    fileName: string
    fileProjectId: string
    fileTypeName: string
    fileUrl: string
    id: number
    projectId: string
    receiveFlag: number
    receiveTime: string
}
export interface fileVo {
    fileId: number
    fileType: number
    fileTypeName: string
    projectId: number
}


export interface AllocationVo {
    auditGroupList: []
    resultDetailList: []
    projectId: number
}
interface templatVo {
    id: number
    noProvideReason: string
    provideFlag: number
}
// 项目迎神管理api
export const ProjectApprovalApi = {
    // 迎审管理列表分页查询
    getProjectReviewList: async (data: SearchVo) => {
        return await request.post({ url: `/manage/proj-plan-project/projectReviewPage`, data })
    },
    // 迎审管理-迎审小组管理tab-迎审文件列表查询
    getFileReceiveList: async (id: number) => {
        return await request.get({ url: `/manage/file-receive/fileReceiveList?projectId=${id}` })
    },
    // 审计取证单--文书接收取证单
    fileAuditCheckReceiveList: async (data: any) => {
        return await request.post({ url: `/manage/file-receive/fileAuditCheckReceiveList`, data })
    },
    // 迎审管理-迎审小组管理tab-迎审文件-接收
    fileReceive: async (data: fileReceiveVo) => {
        return await request.post({ url: `/manage/file-receive/fileReceive`, data })
    },
    // 迎审管理-迎审小组管理tab-迎审小组列表查询
    getWelAuditGroupList: async (id: number) => {
        return await request.get({ url: `/manage/audited-group/welAuditGroupList?projectId=${id}` })
    },
    // 迎审管理-迎审小组管理tab-迎审小组列表保存
    welAuditGroupChangePhone: async (data: any) => {
        return await request.post({ url: `/manage/audited-group/welAuditGroupChangePhone`,data })
    },
    // 迎审管理-迎审小组管理tab-迎审小组新增保存
    welAuditGroupAdd: async (data: any) => {
        return await request.post({ url: `/manage/audited-group/welAuditGroupAdd`, data })
    },
    // 迎审管理-迎审小组管理tab-迎审小组删除
    deleteWelAuditGroup: async (id: number) => {
        return await request.delete({ url: `/manage/audited-group/delete?id=${id}` })
    },
    // 迎审管理-提交
    welAuditGroupSave: async (data: any) => {
        return await request.post({ url: `/manage/audited-group/welAuditGroupSave`, data })
    },
    // 迎审管理-项目迎审tab-资料清单填报列表查询
    getgetProjSourceByProjId: async (id: number) => {
        return await request.get({ url: `/manage/group-up-data/getProjSourceByProjectId?id=${id}` })
    },
    // 迎审管理-项目迎审tab-资料清单填报列表查询--只在迎审管理里面用
    getTotalProjSourceByProjectId: async (id: number) => {
        return await request.get({ url: `/manage/group-up-data/getTotalProjSourceByProjectId?id=${id}` })
    },
    // 迎审管理-迎审人员提报审计资料-资料清单填报列表查询
    getgetCurrentUserProjSourceByProjId: async (id: number) => {
        return await request.get({ url: `/manage/group-up-data/getCurrentUserProjSourceByProjectId?id=${id}` })
    },
    // 迎审管理-迎审人员提报审计资料-提交时保存资料清单-提交未提交资料原因说明
    saveReportWelCheck: async (data: any) => {
        return await request.post({ url: `/manage/group-up-data/saveReportWelCheck`,data })
    },
    // 审计联络人分配资料清单-分配人员列表查询
    getAssignPersonList: async (id: number) => {
        return await request.get({ url: `/manage/proj-plan-project/projectDocumentAndDept?id=${id}` })
    },
    // 审计联络人分配资料清单-分配保存
    distributionDocument: async (data: any) => {
        return await request.post({ url: `/manage/proj-plan-project/distributionDocument`, data })
    },
    //审计联络人确认资料清单-一件催办
    urgency: async (data: any) => {
        return await request.post({ url: `/manage/group-up-data/urgency`, data })
    },
    // 未提供原因说明保存
    welCheckModify: async (data: templatVo) => {
        return await request.post({ url: `/manage/proj-resource-detail/welCheckModify`, data })
    },
    // 获取审计资料清单列表
    getWellCheckFileList: async (id: number) => {
        return await request.get({ url: `/manage/project-assign-atta/getWellCheckFileList?projectId=${id}`, })
    },
    
    // 获取审计资料清单列表---审计联络人确认资料清单专用
    getWellCheckFileDetailList: async (id: number) => {
        return await request.get({ url: `/manage/project-assign-atta/getWellCheckFileDetailList?projectId=${id}`, })
    },
    // 获取审计资料清单列表--只查看自己
    getCurrentUserWellCheckFileList: async (id: number) => {
        return await request.get({ url: `/manage/project-assign-atta/getCurrentUserWellCheckFileList?projectId=${id}`, })
    },
    // 获取审计资料清单列表--只查看自己--补充审计资料
    getCurrentUserWellCheckDetailFileList: async (params: any) => {
        return await request.get({ url: `/manage/project-assign-atta/getCurrentUserWellCheckDetailFileList`, params })
    },
    // 获取审计资料清单列表--只查看自己--补充审计资料
    getCurrentUserWellCheckFileListByResourceId: async (id: number, shouldFilter: any) => {
        return await request.get({ url: `/manage/project-assign-atta/getCurrentUserWellCheckFileListByResourceId?resourceId=${id}&shouldFilter=${shouldFilter}` })
    },


    // 审计资料清单-附件上传保存
    bindProjectFile: async (data: fileVo) => {
        return await request.post({ url: `/manage/project-assign-atta/bindProjectFile`, data })
    },
    // 审计资料清单删除
    deleteProjectFileByProjectFileId: async (id: number) => {
        return await request.get({ url: `/manage/project-assign-atta/deleteProjectFileByProjectFileId?id=${id}`, })
    },
    // 资料清单填报列表查询
    getProjectDocumentAndDept: async (id: number) => {
        return await request.post({ url: `/manage/proj-plan-project/projectDocumentAndDept?id=${id}` })
    },
    // 删除资料清单已分配数据
    deleteGroupUpData: async (id: number) => {
        return await request.delete({ url: `/manage/group-up-data/delete?id=${id}` })
    },
    // 迎审管理-资料清单填报-上传
    bindWelCheckFileToDetail: async (data: any) => {
        return await request.post({ url: `/manage/project-assign-atta/bindWelCheckFileToDetail`, data })
    },
    // 迎审管理-资料清单填报-上传(不在选择文件类型)
    bindCurrentResourceTypeWelCheckFileToDetail: async (data: any) => {
        return await request.post({ url: `/manage/project-assign-atta/bindCurrentResourceTypeWelCheckFileToDetail`, data })
    },
}

