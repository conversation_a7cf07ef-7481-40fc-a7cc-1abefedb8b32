import request from '@/config/axios'

// 审计类型 VO
export interface AuditTypeVO {
	id ?: number // 主键，唯一标识
	auditTypeName : string // 审计类型名称
	parentId : number // 父级id
	involvingExternalAudit : string // 是否涉及外审单位
	sort : number //排序
	status : number //状态
}
export interface AuditDetailVO {
	id : number // 主键，唯一标识
	auditTypeName : string // 审计类型名称
	parentId : number // 父级id
	involvingExternalAudit : string // 是否涉及外审单位
	sort : number //排序
	status : number //状态
	adder : string
	createTime : Date

}

// 审计类型 API
export const AuditTypeApi = {
	// 查询审计类型列表分页
	getAuditTypeList: async (params : any) => {
		return await request.get({ url: `/system/audit-type/page`, params })
	},
	// 查询审计类型，包括一、二级
	getAuditTypeAll: async () => {
		return await request.get({ url: `/system/audit-type/getAll` })
	},
	// 审计审计类型，包含一级
	getAuditTypeFirst: async (id : number) => {
		return await request.get({ url: `/system/audit-type/getFirst?parentId=` + id })
	},

	// 配置审计程序接口
	getAuditTypeTree: async (id : number) => {
		return await request.get({ url: `/system/config-type-prog/get-tree?id=${id}` })
	},

	// 获得二级审计类型分页
	getAuditTypeSecondList: async (params : any) => {
		return await request.get({ url: `/system/audit-type/secondPage`, params })
	},


	// 查询审计类型详情
	getAuditType: async (id : number) => {
		return await request.get({ url: `/system/audit-type/get?id=` + id })
	},

	// 新增审计类型
	createAuditType: async (data : AuditTypeVO) => {
		return await request.post({ url: `/system/audit-type/create`, data })
	},

	// 修改审计类型
	updateAuditType: async (data : AuditTypeVO) => {
		return await request.put({ url: `/system/audit-type/update`, data })
	},

	// 删除审计类型
	deleteAuditType: async (id : number) => {
		return await request.delete({ url: `/system/audit-type/delete?id=` + id })
	},

	// 导出审计类型 Excel
	exportAuditType: async (params : any) => {
		return await request.download({ url: `/system/audit-type/export-excel`, params })
	},

	// 配置-提交
	getAuditTypeArray: async (data) => {
		return await request.put({ url: `/system/config-type-prog/updatebatch`, data })
	},
}