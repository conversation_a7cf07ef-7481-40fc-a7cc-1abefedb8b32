import request from '@/config/axios'

// 审计方案 VO

export interface SearchVo {
    pageNo: number
    pageSize: number
    planYear?: string
    planName?: string
    planNo?: number
    reportUnitName?: string
    projectName?: string
    auditType?: number
    orgType?: number
    deptPlanId?: number,
    tableKey?: string
}

// 年度计划总览
export const YearPlanOverviewApi = {
    // 计划清单列表
    getPlanNoticePage: async (params: SearchVo) => {
        return await request.get({ url: `/manage/year-plan-project/getYearPlanProjectTotalPage`, params })
    },
    getYearPlanTotalHerder: async () => {
        return await request.get({ url: `/manage/year-plan-report-dept/getYearPlanTotalHerder` })

    },
    getYearPlanTotalPage: async (params: SearchVo) => {
        return await request.get({ url: `/manage/year-plan-report-dept/getYearPlanTotalPage`, params })
    },
}

