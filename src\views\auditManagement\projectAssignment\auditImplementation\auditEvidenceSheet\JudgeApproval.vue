
<template>
  <!-- 主审审核取证单 -->
  <el-row :gutter="16" v-loading="formLoading">
    <el-col :span="24">
      <div class="detail-title common-border-left-blue">
        <span>项目基本信息</span>
      </div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="项目名称">{{ detailData.projectName }}</el-descriptions-item>
        <el-descriptions-item label="项目编号">{{ detailData.projectNo }}</el-descriptions-item>
        <el-descriptions-item label="审计类型">{{ detailData.auditTypeDesc}}</el-descriptions-item>
        <el-descriptions-item label="立项年度">{{ detailData.projectYear }}</el-descriptions-item>
        <el-descriptions-item label="组织方式">
          <dict-tag :type="DICT_TYPE.ORGANIZATION_TYPE" :value="detailData.orgType" />
        </el-descriptions-item>
        <el-descriptions-item label="时间计划">{{detailData.timeSchedule }}</el-descriptions-item>
        <el-descriptions-item label="审计期间">{{ detailData.auditPeriod}}</el-descriptions-item>
        <el-descriptions-item label="开展事项">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.specialFlag" />
        </el-descriptions-item>
        <el-descriptions-item label="整体报告">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.overallReportFlag" />
        </el-descriptions-item>
        <el-descriptions-item label="公司领导">{{ detailData.companyLeader}}</el-descriptions-item>
        <el-descriptions-item label="曾任职务">{{ detailData.previouWork}}</el-descriptions-item>
        <el-descriptions-item label="发文编号">{{ detailData.docNum}}</el-descriptions-item>
        <el-descriptions-item label="是否境外">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.overseasFlag" />
        </el-descriptions-item>
        <el-descriptions-item label="是否重要">
          <dict-tag :type="DICT_TYPE.COMMON_IS_OR_STATUS" :value="detailData.significantFlag" />
        </el-descriptions-item>
        <el-descriptions-item label="实施单位">{{ detailData.implementDeptName}}</el-descriptions-item>
        <el-descriptions-item label="立项依据" :span="4">{{ detailData.projectGist}}</el-descriptions-item>
      </el-descriptions>
      <div class="detail-title common-border-left-blue">
        <span>审计对象信息</span>
      </div>
      <el-table
        border
        :data="detailData.auditTargetList"
        :stripe="true"
        :show-overflow-tooltip="true"
      >
        <el-table-column label="#" width="50" align="center">
          <template
            #default="{ $index }"
          >{{ (queryParams.pageNo - 1) * queryParams.pageSize + $index + 1 }}</template>
        </el-table-column>
        <el-table-column label="项目编号" align="left" prop="projectNo" min-width="180" />
        <el-table-column label="项目名称" align="left" prop="projectName" min-width="260" />
        <el-table-column label="审计对象" align="left" prop="auditTarget" min-width="180" />
        <el-table-column label="组长" align="center" prop="groupLeader" min-width="120" />
        <el-table-column label="项目阶段" key="projStage" align="center" min-width="100">
          <template #default="scope">
            <dict-tag :type="'proj_parent_project_stage'" :value="scope.row.projStage" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" :width="120" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="showTeamDetail('',scope.row.id)">查看小组信息</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="detail-title common-border-left-blue">
        <span>审计团队信息</span>
      </div>
      <el-table
        border
        :data="detailData.auditGroupList"
        :stripe="true"
        :show-overflow-tooltip="true"
      >
        <el-table-column label="#" type="index" width="50" />
        <el-table-column label="审计角色" align="center" prop="roleName" min-width="120" />
        <el-table-column label="项目成员" align="left" prop="userName" min-width="180" />
        <el-table-column label="身份类型" align="center" prop="identityName" min-width="120"/>
        <el-table-column label="审计体系" align="center" prop="auditSystemName"  min-width="120"/>
        <el-table-column label="审计事项" align="left" prop="auditMatterName" min-width="180" />
      </el-table>
      <div class="detail-title common-border-left-blue">
        <span>审计取证单</span>
      </div>
      <!-- <el-row>
          <el-col :span="24" class="text-right">
            <el-button type="primary" plain @click="handleImportFile('all')">附件上传</el-button>
          </el-col>
      </el-row>-->
      <el-table border :data="linkList" :stripe="true" :show-overflow-tooltip="true">
        <el-table-column label="#" type="index" width="50" />
        <el-table-column label="文档名称" align="left" :width="180">
          <template #default="{row}">
            <span
              class="click-pointer"
              @click="showList([{
                id: row.mainId
              }])"
            >{{row.fileName}}</span>
          </template>
        </el-table-column>
        <el-table-column label="文档类型" align="center" prop="fileTypeName" min-width="100" />
        <!-- <el-table-column label="资料清单" align="left" prop="materialName" min-width="180" />
        <el-table-column label="资料类型" align="center" prop="resourceName" min-width="100" />-->
        <el-table-column label="上传人" align="center" prop="creatorName" />
        <el-table-column label="上传时间" align="center" prop="createTime" min-width="120">
          <template #default="{row}">
            <span>{{ formatTime(row.createTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" :width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleDownload(scope.row?.fileUrl,scope.row?.fileName)">下载</el-button>
            <el-button
              type="primary"
              link
              @click="handleShowMessage(scope.row.fileName, 'view', scope.row.fileId)"
            >预览</el-button>
            <el-button
              type="primary"
              link
              v-if="isApprove"
              @click="handleShowMessage(scope.row.fileName, 'EDIT', scope.row.fileId)"
            >编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-col>
  </el-row>
  <TeamDetail ref="teamDetailRef" />
  <DialogFlie ref="dialogFlieRef" />
  <FileForm
    ref="formImgRef"
    @success="handleUploadSuccess"
    :type="fileType"
    :limit="fileLimit"
    :showRadioByInterface="true"
    :radioListByInterface="fileTypeList"
  />
  <SheetList ref="sheetListRef" />
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { AuditRoleApi, AuditRoleVO } from '@/api/basicData/auditRole'
import { DialogFlie } from '@/components/DialogFlie'
import FileForm from '@/views/infra/file/FileForm.vue'
import SheetList from './SheetList.vue'
import TeamDetail from '@/views/auditManagement/projectAssignment/PreTrialPreparation/ProjectInitiation/TeamDetail.vue'
import { ProjectDataListApi } from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectDataList'
import { formatTime } from '@/utils'
import { CommonApi } from '@/api/common'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { ProjectInitiationApi } from '@/api/auditManagement/projectAssignment/PreTrialPreparation/projectInitiation'
import { AuditEvidenceSheetApi } from '@/api/auditManagement/projectAssignment/auditImplem/auditEvidenceSheet'
import { link } from 'fs'
import download from '@/utils/download'
import { propTypes } from '@/utils/propTypes'
const { wsCache } = useCache()
defineOptions({ name: 'JudgeApproval' })
const props = defineProps({
  id: propTypes.number.def(undefined),
  formVariables: {
    default: () => ({}),
    type: Object
  },
  isApprove: propTypes.bool.def(true)
})

const { query } = useRoute() // 查询参数
const queryId = query.id as unknown as number // 从 URL 传递过来的 id 编号
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const detailData = ref({
  id: 0,
  auditRoleName: undefined,
  parentId: undefined,
  sort: undefined,
  status: undefined,
  projectPhase: undefined,
  date: undefined,
  content: undefined,
  ids: []
})
const list = ref()

const formRef = ref() // 表单 Ref

const teamDetailRef = ref()
const showTeamDetail = (parId?: number | string, sonId?: number | string) => {
  teamDetailRef.value.open(parId, sonId)
}
const formImgRef = ref()
const fileType = ref('img')
const fileLimit = ref(1)
// 上传方法
const handleImport = (type: string) => {
  fileType.value = type
  fileLimit.value = type === 'file' ? 5 : 1
  formImgRef.value.open()
}
const formData = ref()
const handleUploadSuccess = async (fileList, radioType) => {
  let fileArr =
    fileList && fileList.length > 0
      ? fileList.map((item) => {
          return {
            // url: item.response.data,
            fileName: item.name,
            fileTypeName: fileMap.get(radioType),
            fileType: radioType,
            fileUrl: item.response.data.fileUrl,
            fileId: item.response.data.id,
            deptId: wsCache.get(CACHE_KEY.USER).user.deptId,
            deptName: wsCache.get(CACHE_KEY.USER).user.deptName,
            creatorName: item.response.data.creatorName,
            createTime: item.response.data.createTime,
            projectId: detailData.value.id
            // mainId: formData.value.auditNoticeRespVO.id
          }
        })
      : []
  await CommonApi.createCommon(fileArr[0])
  await getInfo()
  // formData.value.projectAttaVOList = formData.value.projectAttaVOList.concat(...fileArr)
}
interface fileType {
  attTypeCode: string
  label: string
  value: string
  attTypeName: string
}
const fileTypeList = ref<fileType[]>([])
const fileMap = new Map<string, string>()
const getInfo = async () => {
  detailData.value = await ProjectDataListApi.getProjectInitiationDetail(props.id || queryId, 'Q')
}
const linkList = ref([])
const getLinkList = async () => {
  // let data = {
  //   // ids: detailData.value.ids
  //   mainIds: detailData.value.ids
  // }
  // // linkList.value = await AuditEvidenceSheetApi.getEvidenceDetailList(data)
  // let res = await AuditEvidenceSheetApi.getFileListByIds(data)
  // linkList.value = res.list

  let data = {
    projectId: detailData.value.id,
    mainIdList: detailData.value.ids
  }
  linkList.value = (await AuditEvidenceSheetApi.getFileListByCodeList(data)) || []
}
const getFileListByids = async () => {
  let data = {
    mainIds: detailData.value.ids
  }
  let res = await AuditEvidenceSheetApi.getFileListByIds(data)
  detailData.value.projectAssignAttaRespList = res.list || []
}
const sheetListRef = ref()
const showList = (ids) => {
  sheetListRef.value.open(ids)
}
/** 打开弹窗 */
const open = async (id: number, arr: any) => {
  detailData.value.id = id

  dialogVisible.value = true
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      await getInfo()
      fileTypeList.value = await ProjectInitiationApi.getFileType('Q')
      fileTypeList.value.forEach((item) => {
        item.label = item.attTypeName
        item.value = item.attTypeCode
        fileMap.set(item.attTypeCode, item.label)
      })
      detailData.value.ids = arr.map((item) => item.id)
      await getLinkList()
      // await getFileListByids()
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
const handleDownload = (url: string, name: string) => {
  download.downFileByFileUrl(url, name)
}
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const dialogFlieRef = ref()
const handleShowMessage = (title: string, flietype: string, id: number) => {
  dialogFlieRef.value.open(title, flietype, id)
}
const handleImportFile = (type: string) => {
  formImgRef.value.open()
  fileType.value = type
}

onMounted(async () => {
  try {
    formLoading.value = true
    await getInfo()
    fileTypeList.value = await ProjectInitiationApi.getFileType('Q')
    fileTypeList.value.forEach((item) => {
      item.label = item.attTypeName
      item.value = item.attTypeCode
      fileMap.set(item.attTypeCode, item.label)
    })
    detailData.value.ids = props.formVariables?.ids ?? []

    await getLinkList()
  } catch (error) {
  } finally {
    formLoading.value = false
  }
  // await getFileListByids()
})
</script>
<style lang="scss" scoped>
.title {
  font-weight: 900;
  font-size: 16px;
  margin-bottom: 16px;
  margin-top: 16px;
  display: flex;
  align-items: center;
}
:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell) {
  width: 80px;
}
</style>
