import request from '@/config/axios'


// 审计通知书 API
export const suppleAuditMaterialsApi = {
  // 流程页-提交
  supplementalFlowSave: async (data: any) => {
    return await request.post({ url: `/manage/proj-resource/supplementalFlowSave`, data })
  },
  // 流程页-绑定文件
  bindWelCheckFile: async (data: any) => {
    return await request.post({ url: `/manage/project-assign-atta/bindWelCheckFile`, data })
  },
    // 获取审计资料清单
    getWellCheckFileDetailList: async (id: number) => {
      return await request.get({ url: `/manage/project-assign-atta/getWellCheckedFileDetailList?projectId=${id}`, })
  },
}
