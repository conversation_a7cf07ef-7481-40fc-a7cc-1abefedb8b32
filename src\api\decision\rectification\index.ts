import request from '@/config/axios'

// 整改问题分类数据 VO
export interface TypePineVO {
	 /*类型名称 */
	 typeName: string;
	 /*类型ID */
	 typeId: string;
	 /*类型对应数量 */
	 typeNum: number;
}

// 总看板 VO
export interface TotalVO {
	 /*金额总计 */
	 money: number;
	 /*补齐税费 */
	 taxMoney: number;
	 /*回收资金 */
	 recycleMoney: number;
	 /*会计资金 */
	 accountingMoney: number;
	 /*归还资金 */
	 backMoney: number;
	 /*损失资金 */
	 lossMoney: number;
	 /*非金额类总计数 */
	 noTeeTotal: number;
	 /*新增新制度总计数 */
	 newInstitutionTotal: number;
	 /*完善业务流程数 */
	 betterBusinessTotal: number;
	 /*清理银行账户数 */
	 clearBankAccountTotal: number;
	 /*修订完善制度数 */
	 betterInstitutionTotal: number;
	 /*清理债券债务数 */
	 clearClaims: number;
	 /*追责汇总数 */
	 accountabilityTotal: number;
	 /*移送纪委数 */
	 moveDisciplineNum: number;
	 /*纪律处分数 */
	 handleDisciplineNum: number;
	 /*问责处理人数 */
	 handleAccountabilityNum: number;
	 /*年度考核人数 */
	 handleExamineNum: number;
	 /*行政警告人数 */
	 handleWarningNum: number;
}

// 顶部数字 VO
export interface TopNumVO {
	 /*整改问题总数 */
	 questionNum: number;
	 /*未发起整改问题数 */
	 unRunQuestionNum: number;
	 /*未发起整改占比 */
	 unRunQuestionNumRate: string;
	 /*已发起整改数量 */
	 executeQuestionNum: number;
	 /*已发起整改占比 */
	 executeQuestionNumRate: string;
	 /*整改中数量 */
	 executeIngQuestionNum: number;
	 /*整改中占比 */
	 executeIngQuestionNumRate: string;
	 /*已完成整改数量 */
	 executedQuestionNum: number;
	 /*已完成整改数量占比 */
	 executedQuestionNumRate: string;
}

// 发现问题数与整改问题数 VO
export interface QuestionNumChartVO {
	 /*问题类型名称(y 轴) */
	 typeName: Record<string, unknown>[];
	 /*发现问题 */
	 findQuestionNum: Record<string, unknown>[];
	 /*整改问题书 */
	 questionNum: Record<string, unknown>[];
}

// 整改问题销号情况 VO
export interface QuestionFinishVO {
	 /*总数 */
	 totalNum: number;
	 /*未销号期内整改数 */
	 unFinishNum: number;
	 /*未销号逾期未销号数 */
	 unFinishTimeOutNum: number;
	 /*已销号按期销号数 */
	 finishNum: number;
	 /*已销号逾期销号数 */
	 finishTimeOutNum: number;
}

// 问题整改统计图 VO
export interface QuestionChartVO {
	/*未发起占比，需要自己拼接% */
	unRunQuestionRate: number;
	/*问题数 */
	questionNum: number;
	/*未发起数量 */
	unRunQuestionNum: number;
	/*已发起数量 */
	executeQuestionNum: number;
	/*整改中占比，需要自己拼接% */
	executeIngQuestionRate: number;
	/*整改中数量 */
	executeIngQuestionNum: number;
	/*已完成整改中占比，需要自己拼接% */
	executedQuestionRate: number;
	/*已完成整改数量 */
	executedQuestionNum: number;
	/*分类ID */
	typeId: number;
	/*分类名称 */
	typeName: string;
}

// 整改看板完成率 VO
export interface FinishRateVO {
	/*公司名称 */
    companyName: string;
    /*公司ID */
    companyId: number;
    /*总问题数 */
    questionTotal: number;
    /*正常整改数 */
    normalNum: number;
    /*逾期整改数 */
    timeOutNum: number;
    /*完成整改数 */
    finishNum: number;
    /*整改率 */
    rate: string;
}

// 整改看板 API
export const RectificationApi = {
	// 整改问题分类数据
	getTypePine: async (params : any) => {
		return await request.get({ url: `/manage/graph/rectification/type-pine`, params })
	},
	// 获得总看板
	getTotal: async (params : any) => {
		return await request.get({ url: `/manage/graph/rectification/total`, params })
	},
	// 获得顶部数字
	getTopNum: async (params : any) => {
		return await request.get({ url: `/manage/graph/rectification/top-num`, params })
	},
	// 获得发现问题数与整改问题数
	getQuestionNumChart: async (params : any) => {
		return await request.get({ url: `/manage/graph/rectification/question-num-chart`, params })
	},
	// 获得整改问题销号情况
	getQuestionFinish: async (params : any) => {
		return await request.get({ url: `/manage/graph/rectification/question-finish`, params })
	},
	// 获得问题整改统计图
	getQuestionChart: async (params : any) => {
		return await request.get({ url: `/manage/graph/rectification/question-chart`, params })
	},
	// 获得整改看板完成率
	getFinishRate: async (params : any) => {
		return await request.get({ url: `/manage/graph/rectification/finish-rate`, params })
	},
}